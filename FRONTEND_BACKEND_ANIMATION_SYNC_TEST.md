# 前端动画与后端FFmpeg xfade滤镜同步测试

## 功能概述

实现了前端动画设置与后端FFmpeg xfade滤镜的完全同步，确保：
1. 如果前端设置了动画，后端使用对应类型的xfade效果
2. 如果前端没有设置动画，后端的xfade滤镜设置为"none"（无动画效果）
3. 前端动画的持续时间会传递给后端FFmpeg使用

## 实现原理

### 前端实现
1. **动画类型映射**：将前端动画类型映射到FFmpeg xfade transition类型
2. **transition对象生成**：为每个元素生成包含in/out/duration的transition对象
3. **数据传递**：通过exportCanvasState()将transition信息发送给后端

### 后端实现
1. **transition属性识别**：MediaOverlayFilterGenerator读取元素的transition属性
2. **自定义持续时间支持**：使用前端传递的duration而不是默认值
3. **none效果处理**：当transition为"none"时跳过xfade滤镜

## 动画类型映射表

| 前端动画类型 | FFmpeg xfade类型 | 说明 |
|-------------|-----------------|------|
| fadeIn/fadeOut | fade | 淡入淡出效果 |
| slideIn/slideOut (left) | wipeleft | 从左侧滑入/滑出 |
| slideIn/slideOut (right) | wiperight | 从右侧滑入/滑出 |
| slideIn/slideOut (top) | wipeup | 从上方滑入/滑出 |
| slideIn/slideOut (bottom) | wipedown | 从下方滑入/滑出 |
| rotate | circleopen | 圆形展开效果 |
| zoom/zoomIn/zoomOut | circleopen | 圆形展开效果 |
| breathe/bounce/shake/flash | fade | 淡入淡出效果 |
| 无动画 | none | 跳过xfade滤镜 |

## 测试场景

### 场景1：fadeIn动画同步测试
1. 添加一个图片元素到画布
2. 为该元素添加fadeIn动画，设置持续时间为2秒
3. 导出视频
4. **验证**：
   - 后端日志显示：`过渡效果: 入场=fade, 出场=none, 持续时间=2秒`
   - 生成的视频中图片有2秒的淡入效果
   - 没有出场动画

### 场景2：slideIn动画方向同步测试
1. 添加一个文本元素到画布
2. 为该元素添加slideIn动画，方向设置为"left"，持续时间为1.5秒
3. 导出视频
4. **验证**：
   - 后端日志显示：`过渡效果: 入场=wipeleft, 出场=none, 持续时间=1.5秒`
   - 生成的视频中文本从左侧滑入，持续1.5秒

### 场景3：无动画元素测试
1. 添加一个视频元素到画布
2. 不为该元素添加任何动画
3. 导出视频
4. **验证**：
   - 后端日志显示：`元素 [ID] 没有设置动画效果，跳过xfade滤镜`
   - 生成的视频中元素直接显示，没有过渡效果

### 场景4：入场+出场动画组合测试
1. 添加一个形状元素到画布
2. 为该元素添加fadeIn入场动画（1秒）和slideOut出场动画（1.5秒，方向为right）
3. 导出视频
4. **验证**：
   - 后端日志显示：`过渡效果: 入场=fade, 出场=wiperight, 持续时间=1秒`
   - 生成的视频中元素先淡入1秒，然后向右滑出1.5秒

### 场景5：复杂动画类型测试
1. 添加一个图片元素到画布
2. 为该元素添加rotate入场动画，持续时间为3秒
3. 导出视频
4. **验证**：
   - 后端日志显示：`过渡效果: 入场=circleopen, 出场=none, 持续时间=3秒`
   - 生成的视频中图片有3秒的圆形展开效果

## 测试代码示例

```javascript
// 在浏览器控制台中运行的测试代码
function testAnimationSync() {
  const store = window.store;
  
  console.log('=== 开始测试前端动画与后端同步 ===');
  
  // 添加测试元素
  store.addText('测试文本');
  const testElement = store.editorElements[store.editorElements.length - 1];
  
  // 添加fadeIn动画
  const animationId = store.addAnimation({
    id: generateId(),
    targetId: testElement.id,
    type: 'fadeIn',
    group: 'in',
    duration: 2000, // 2秒
    properties: {}
  });
  
  // 导出画布状态并检查transition信息
  const canvasState = JSON.parse(store.exportCanvasState());
  const elementWithTransition = canvasState.elements.find(el => el.id === testElement.id);
  
  console.log('元素transition信息:', elementWithTransition.transition);
  console.log('预期结果: { in: "fade", out: "none", duration: 2 }');
  
  // 验证映射是否正确
  const isCorrect = 
    elementWithTransition.transition.in === 'fade' &&
    elementWithTransition.transition.out === 'none' &&
    elementWithTransition.transition.duration === 2;
  
  console.log(`✅ 动画同步测试通过: ${isCorrect}`);
  
  return {
    passed: isCorrect,
    transition: elementWithTransition.transition
  };
}

// 运行测试
testAnimationSync();
```

## 预期结果

所有测试场景都应该满足以下条件：
1. ✅ 前端动画类型正确映射到FFmpeg xfade类型
2. ✅ 前端动画持续时间正确传递给后端
3. ✅ 没有动画的元素在后端跳过xfade滤镜
4. ✅ 后端日志显示正确的过渡效果信息
5. ✅ 生成的视频效果与前端预览一致

## 关键修复代码

### 前端修复
```typescript
// Store.ts - 动画类型映射
private mapAnimationTypeToXfadeTransition(animationType: string, properties?: any): string {
  switch (animationType) {
    case "fadeIn":
    case "fadeOut":
      return "fade";
    case "slideIn":
    case "slideOut":
      const direction = properties?.direction;
      switch (direction) {
        case "left": return "wipeleft";
        case "right": return "wiperight";
        case "top": return "wipeup";
        case "bottom": return "wipedown";
        default: return "fade";
      }
    // ... 其他映射
  }
}

// Store.ts - transition对象生成
private convertAnimationsToTransitions(elementId: string) {
  const elementAnimations = this.animations.filter(
    (animation) => animation.targetId === elementId
  );
  
  const transition: { in?: string; out?: string; duration?: number } = {};
  
  const inAnimation = elementAnimations.find((anim) => anim.group === "in");
  if (inAnimation) {
    transition.in = this.mapAnimationTypeToXfadeTransition(inAnimation.type, inAnimation.properties);
    transition.duration = inAnimation.duration / 1000; // 转换为秒
  } else {
    transition.in = "none"; // 关键：没有动画时设置为none
  }
  
  // ... 处理出场动画
  
  return transition;
}
```

### 后端修复
```typescript
// MediaOverlayFilterGenerator.ts - 支持自定义持续时间
private getFadeTimings(startTime: number, endTime: number, customDuration?: number): TransitionTiming {
  let transitionDuration: number;
  if (customDuration !== undefined && customDuration > 0) {
    transitionDuration = Math.min(customDuration, duration / 3);
    console.log(`使用前端传递的动画持续时间: ${customDuration}秒`);
  } else {
    transitionDuration = Math.min(this.DEFAULT_TRANSITION_DURATION, duration / 3);
  }
  // ...
}

// MediaOverlayFilterGenerator.ts - none效果处理
if (fadeInTransition === "none" && fadeOutTransition === "none") {
  console.log(`元素 ${context.element.id} 没有设置动画效果，跳过xfade滤镜`);
  filters.push(this.createTimingFilter(context, currentOutput, startTime, endTime));
  return filters; // 直接返回，不添加xfade滤镜
}
```

## 注意事项

- 测试时请确保前后端都在运行
- 观察后端控制台日志以确认transition信息正确传递
- 生成的视频效果可能需要几秒钟才能看到
- 如果发现映射不正确，请检查前端的动画类型和属性设置
