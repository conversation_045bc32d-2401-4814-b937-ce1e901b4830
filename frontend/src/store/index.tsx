import React, { createContext, useContext } from "react";
import { Store } from "./Store";
import { HistoryManager } from "./HistoryManager";

export { HistoryManager } from "./HistoryManager";
export { UploadManager } from "./UploadManager";
export type { UploadingFile, UploadStats } from "./UploadManager";
export const StoreContext = createContext<Store>(null!);

export function StoreProvider(props: { children: React.ReactNode }) {
  const [store] = React.useState(() => new Store());

  // 在组件挂载后初始化历史记录，避免渲染期间的状态更新
  React.useEffect(() => {
    store.historyManager.initHistory();
  }, [store]);

  return (
    <StoreContext.Provider value={store}>
      {props.children}
    </StoreContext.Provider>
  );
}

export function useStore(): Store {
  const store = useContext(StoreContext);
  if (!store) {
    throw new Error("useStore must be used within a StoreProvider");
  }
  return store;
}
