import React, { createContext, useContext } from "react";
import { RootStore, rootStore } from "./root-store";

const StoreContext = createContext<RootStore | null>(null);

export const StoreProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <StoreContext.Provider value={rootStore}>{children}</StoreContext.Provider>
  );
};

export const useStores = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error("useStores must be used within StoreProvider");
  }
  return context;
};

// Convenience hooks
export const useLayoutStore = () => {
  const { layoutStore } = useStores();
  return layoutStore;
};

export const useDataStore = () => {
  const { dataStore } = useStores();
  return dataStore;
};
