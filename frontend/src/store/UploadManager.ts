import { makeAutoObservable, action } from "mobx";
import { s3UploadService, MediaMetadata } from "../services/s3UploadService";

// 上传文件状态类型
export interface UploadingFile {
  id: string;
  file: File;
  progress: number;
  status: "uploading" | "error" | "completed";
  error?: string;
  result?: MediaMetadata;
  uploadStartTime: number;
}

// 上传统计信息
export interface UploadStats {
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  totalProgress: number; // 总体进度百分比
}

export class UploadManager {
  uploadingFiles: Map<string, UploadingFile> = new Map();
  isUploading = false;

  constructor() {
    makeAutoObservable(this);
  }

  // 获取当前上传中的文件列表
  get currentUploads(): UploadingFile[] {
    return Array.from(this.uploadingFiles.values());
  }

  // 获取上传统计信息
  get uploadStats(): UploadStats {
    const uploads = this.currentUploads;
    const totalFiles = uploads.length;
    const completedFiles = uploads.filter(
      (f) => f.status === "completed"
    ).length;
    const failedFiles = uploads.filter((f) => f.status === "error").length;

    // 计算总体进度
    const totalProgress =
      totalFiles === 0
        ? 0
        : uploads.reduce((sum, file) => sum + file.progress, 0) / totalFiles;

    return {
      totalFiles,
      completedFiles,
      failedFiles,
      totalProgress,
    };
  }

  // 检查是否有上传进行中
  get hasActiveUploads(): boolean {
    return this.currentUploads.some((file) => file.status === "uploading");
  }

  // 检查是否有失败的上传
  get hasFailedUploads(): boolean {
    return this.currentUploads.some((file) => file.status === "error");
  }

  // 开始上传文件
  @action
  async uploadFiles(files: File[]): Promise<void> {
    this.isUploading = true;

    // 为每个文件创建上传状态
    const newUploadingFiles: UploadingFile[] = files.map((file) => ({
      id: `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      file,
      progress: 0,
      status: "uploading",
      uploadStartTime: Date.now(),
    }));

    // 添加到上传队列
    newUploadingFiles.forEach((uploadingFile) => {
      this.uploadingFiles.set(uploadingFile.id, uploadingFile);
    });

    // 开始上传每个文件
    const uploadPromises = newUploadingFiles.map((uploadingFile) =>
      this.uploadSingleFile(uploadingFile)
    );

    // 等待所有文件上传完成
    await Promise.allSettled(uploadPromises);

    this.isUploading = false;
  }

  // 上传单个文件
  private async uploadSingleFile(uploadingFile: UploadingFile): Promise<void> {
    try {
      const result = await s3UploadService.uploadFile(uploadingFile.file, {
        onProgress: (progress) => {
          // 使用 action 更新进度
          this.updateUploadProgress(uploadingFile.id, progress.percentage);
        },
      });

      // 上传成功，更新状态
      this.updateUploadCompleted(uploadingFile.id, result);

      // 延迟3秒后从队列中移除已完成的文件
      setTimeout(
        action(() => {
          this.removeUploadingFile(uploadingFile.id);
        }),
        3000
      );
    } catch (err) {
      // 上传失败，更新状态
      this.updateUploadError(
        uploadingFile.id,
        err instanceof Error ? err.message : "上传失败"
      );
    }
  }

  // 更新上传进度 (action)
  @action
  private updateUploadProgress(fileId: string, progress: number): void {
    const uploadingFile = this.uploadingFiles.get(fileId);
    if (uploadingFile) {
      const updatedFile = {
        ...uploadingFile,
        progress,
      };
      this.uploadingFiles.set(fileId, updatedFile);
    }
  }

  // 更新上传完成状态 (action)
  @action
  private updateUploadCompleted(fileId: string, result: MediaMetadata): void {
    const uploadingFile = this.uploadingFiles.get(fileId);
    if (uploadingFile) {
      const completedFile: UploadingFile = {
        ...uploadingFile,
        status: "completed",
        result,
        progress: 100,
      };
      this.uploadingFiles.set(fileId, completedFile);
    }
  }

  // 更新上传错误状态 (action)
  @action
  private updateUploadError(fileId: string, error: string): void {
    const uploadingFile = this.uploadingFiles.get(fileId);
    if (uploadingFile) {
      const errorFile: UploadingFile = {
        ...uploadingFile,
        status: "error",
        error,
      };
      this.uploadingFiles.set(fileId, errorFile);
    }
  }

  // 取消上传
  @action
  cancelUpload(fileId: string): void {
    this.uploadingFiles.delete(fileId);
  }

  // 重试上传
  @action
  async retryUpload(fileId: string): Promise<void> {
    const uploadingFile = this.uploadingFiles.get(fileId);
    if (!uploadingFile) return;

    // 重置状态
    const resetFile: UploadingFile = {
      ...uploadingFile,
      status: "uploading",
      progress: 0,
      error: undefined,
      uploadStartTime: Date.now(),
    };
    this.uploadingFiles.set(fileId, resetFile);

    // 重新上传
    await this.uploadSingleFile(resetFile);
  }

  // 移除上传文件
  @action
  removeUploadingFile(fileId: string): void {
    this.uploadingFiles.delete(fileId);
  }

  // 清除所有已完成的上传
  @action
  clearCompletedUploads(): void {
    const uploads = Array.from(this.uploadingFiles.entries());
    uploads.forEach(([id, file]) => {
      if (file.status === "completed") {
        this.uploadingFiles.delete(id);
      }
    });
  }

  // 清除所有失败的上传
  @action
  clearFailedUploads(): void {
    const uploads = Array.from(this.uploadingFiles.entries());
    uploads.forEach(([id, file]) => {
      if (file.status === "error") {
        this.uploadingFiles.delete(id);
      }
    });
  }

  // 清除所有上传记录
  @action
  clearAllUploads(): void {
    this.uploadingFiles.clear();
  }

  // 获取指定文件的上传状态
  getUploadStatus(fileId: string): UploadingFile | undefined {
    return this.uploadingFiles.get(fileId);
  }

  // 获取上传时长（毫秒）
  getUploadDuration(fileId: string): number {
    const uploadingFile = this.uploadingFiles.get(fileId);
    if (!uploadingFile) return 0;
    return Date.now() - uploadingFile.uploadStartTime;
  }

  // 添加上传文件到队列
  @action
  addUploads(files: UploadingFile[]): void {
    files.forEach((file) => {
      this.uploadingFiles.set(file.id, file);
    });
  }

  // 更新上传进度
  @action
  updateProgress(fileId: string, progress: number): void {
    const uploadingFile = this.uploadingFiles.get(fileId);
    if (uploadingFile) {
      const updatedFile = { ...uploadingFile, progress };
      this.uploadingFiles.set(fileId, updatedFile);
    }
  }

  // 更新上传状态
  @action
  updateStatus(
    fileId: string,
    status: "uploading" | "error" | "completed",
    result?: MediaMetadata,
    progress?: number,
    error?: string
  ): void {
    const uploadingFile = this.uploadingFiles.get(fileId);
    if (uploadingFile) {
      const updatedFile: UploadingFile = {
        ...uploadingFile,
        status,
        ...(result && { result }),
        ...(progress !== undefined && { progress }),
        ...(error && { error }),
      };
      this.uploadingFiles.set(fileId, updatedFile);
    }
  }

  // 移除单个上传
  @action
  removeUpload(fileId: string): void {
    this.uploadingFiles.delete(fileId);
  }
}
