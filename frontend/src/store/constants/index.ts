/**
 * 统一常量管理
 * 集中管理所有 store 相关的常量定义
 */

// ==================== 画布相关常量 ====================
export const CANVAS_CONSTANTS = {
  // 默认尺寸
  DEFAULT_WIDTH: 1920,
  DEFAULT_HEIGHT: 1080,
  DEFAULT_BACKGROUND: "#111111",

  // 缩放限制
  MIN_SCALE: 0.1,
  MAX_SCALE: 2,
  DEFAULT_SCALE: 0.15,

  // 缩放步长
  ZOOM_STEP: 0.1,

  // 窗口适配比例
  WINDOW_SCALE_FACTOR: 0.7,
  UI_OFFSET_HEIGHT: 260, // UI元素高度偏移
} as const;

// ==================== 时间线相关常量 ====================
export const TIMELINE_CONSTANTS = {
  // 时间限制
  MAX_TIME: 60 * 60 * 1000, // 1小时
  DEFAULT_DURATION: 30 * 1000, // 30秒
  DEFAULT_DISPLAY_DURATION: 10000, // 10秒
  MIN_ELEMENT_DURATION: 1000, // 1秒

  // 元素默认持续时间
  DEFAULT_ELEMENT_DURATION: {
    TEXT: 5000,
    IMAGE: 3000,
    SHAPE: 5000,
    VIDEO: 5000,
    AUDIO: 5000,
    GIF: 3000,
  },

  // 动画相关
  DEFAULT_FPS: 60,
  WHEEL_THROTTLE_INTERVAL: 16, // 约60fps

  // 平移相关
  PAN: {
    BASE_FACTOR: 6.0,
    BASE_SCROLL_FACTOR: 15.0,
    BASE_TIMELINE_LENGTH: 30 * 1000,
  },

  // 时间线UI
  HEIGHT: "36px",
  TRACK_HEIGHT: "36px",
} as const;

// ==================== 媒体元素相关常量 ====================
export const MEDIA_CONSTANTS = {
  // 默认尺寸
  DEFAULT_VIDEO_HEIGHT: 400,
  DEFAULT_IMAGE_SIZE: 300,
  DEFAULT_SHAPE_SIZE: 200,
  DEFAULT_LINE_WIDTH: 300,
  DEFAULT_LINE_HEIGHT: 5,
  DEFAULT_ELLIPSE_WIDTH: 250,
  DEFAULT_ELLIPSE_HEIGHT: 150,

  // 视频录制
  CAPTURE_FPS: 30,
  DEFAULT_VIDEO_WIDTH: 1280,
  DEFAULT_VIDEO_HEIGHT_RECORD: 720,

  // 支持的格式
  SUPPORTED_VIDEO_FORMATS: ["mp4", "webm"],
  DEFAULT_VIDEO_FORMAT: "mp4",
} as const;

// ==================== 元素类型常量 ====================
export const ELEMENT_TYPES = {
  VIDEO: "video",
  AUDIO: "audio",
  IMAGE: "image",
  GIF: "gif",
  TEXT: "text",
  SHAPE: "shape",
} as const;

// ==================== 颜色主题常量 ====================
export const COLOR_CONSTANTS = {
  // 元素类型颜色
  ELEMENT_COLORS: {
    text: "#ff9800",
    image: "#4caf50",
    video: "#f44336",
    audio: "#3f51b5",
    gif: "#e91e63",
    shape: "#9c27b0",
    default: "#9e9e9e",
  },

  // 轨道类型颜色
  TRACK_COLORS: {
    media: "#4caf50",
    audio: "#3f51b5",
    text: "#ff9800",
    caption: "#9c27b0",
  },

  // 字幕颜色
  CAPTION_COLOR: "#9c27b0",

  // 吸附颜色
  SNAP_COLOR: "#ff9800",
} as const;

// ==================== 动画相关常量 ====================
export const ANIMATION_CONSTANTS = {
  // 默认配置
  DEFAULT_TRANSITION: "fade",
  PRECISION_DIGITS: 2,

  // 动画类型映射
  TYPE_MAPPING: {
    fadeIn: "fade",
    fadeOut: "fade",
    breathe: "fade",
    bounce: "fade",
    shake: "fade",
    flash: "fade",
    rotate: "circleopen",
    zoom: "circleopen",
    zoomIn: "circleopen",
    zoomOut: "circleopen",
  },

  // 动画参数
  DEFAULT_ROTATION_DEGREES: 360,
  DEFAULT_LOOPS: 1,
  DEFAULT_SHAKE_INTENSITY: 30,
  SHAKE_INTENSITY_LIMIT: 0.2, // 对象宽度的20%
  MIN_SHAKE_DURATION: 500,
  MAX_SHAKE_COUNT: 10,
  SHAKE_INTERVAL: 200, // 每200ms一次抖动

  // 抖动衰减模式
  SHAKE_PATTERN: [0.8, -0.6, 0.4, -0.2, 0],
} as const;

// ==================== 项目保存相关常量 ====================
export const SAVE_CONSTANTS = {
  // 防抖延迟
  DELAY: 500,

  // 项目键名
  AUTO_SAVE_KEY_PREFIX: "fabric-canvas-state",
  DEFAULT_PROJECT_KEY: "fabric-canvas-state-default",

  // 时间延迟
  TIMING: {
    FINALIZE_DELAY: 800,
    FALLBACK_DELAY: 300,
  },
} as const;

// ==================== 元素操作相关常量 ====================
export const ELEMENT_OPERATION_CONSTANTS = {
  // 缩放阈值
  SCALING_THRESHOLD: 0.01,

  // 克隆偏移
  CLONE_OFFSET: 20,

  // 默认边框半径
  DEFAULT_BORDER_RADIUS: 10,

  // 元素间距
  ELEMENT_SPACING_MS: 10,
} as const;

// ==================== 拖拽相关常量 ====================
export const DRAG_CONSTANTS = {
  VERTICAL_DRAG_THRESHOLD: 3,
  HORIZONTAL_DRAG_THRESHOLD: 1,
  DRAG_ACTIVATION_DISTANCE: 3,
  DRAG_TOLERANCE: 3,
  DRAG_DELAY: 0,
} as const;

// ==================== 吸附相关常量 ====================
export const SNAP_CONSTANTS = {
  SNAP_THRESHOLD: 100,
  SNAP_COLOR: "#ff9800",
} as const;

// ==================== 轨道名称模板 ====================
export const TRACK_NAME_TEMPLATES = {
  media: "Media Track",
  audio: "Audio Track",
  text: "Text Track",
  caption: "Caption Track",
} as const;

// ==================== 滤镜方法映射 ====================
export const FILTER_METHOD_MAP = {
  brightness: "setBrightness",
  contrast: "setContrast",
  saturation: "setSaturation",
  hue: "setHue",
  blur: "setBlur",
} as const;

// ==================== 基础样式更新映射 ====================
export const BASIC_STYLE_UPDATES = {
  fontSize: "fontSize",
  textAlign: "textAlign",
  charSpacing: "charSpacing",
  lineHeight: "lineHeight",
  fontColor: "fill",
  fontFamily: "fontFamily",
  strokeWidth: "strokeWidth",
  strokeColor: "stroke",
} as const;

// ==================== 元素内容渲染映射 ====================
export const ELEMENT_CONTENT_MAP = {
  text: (element: any) => ({
    icon: "TextFields",
    text: element.properties?.text ?? element.name,
  }),
  image: (element: any) => ({
    icon: "Image",
    text: element.name,
  }),
  video: (element: any) => ({
    icon: "VideoFile",
    text: element.name,
  }),
  audio: (element: any) => ({
    icon: "AudioFile",
    text: element.name,
  }),
} as const;

// ==================== 字幕默认样式常量 ====================
export const CAPTION_STYLE_CONSTANTS = {
  // 字体相关
  DEFAULT_FONT_SIZE: 35,
  DEFAULT_FONT_FAMILY: "Arial",
  DEFAULT_FONT_COLOR: "#FFFFFF",
  DEFAULT_FONT_WEIGHT: 700,
  DEFAULT_TEXT_ALIGN: "center" as const,
  DEFAULT_LINE_HEIGHT: 1.2,
  DEFAULT_CHAR_SPACING: 0,
  DEFAULT_STYLES: ["bold"],

  // 描边相关
  DEFAULT_STROKE_WIDTH: 0.5,
  DEFAULT_STROKE_COLOR: "#000000",

  // 阴影相关
  DEFAULT_SHADOW_COLOR: "#000000",
  DEFAULT_SHADOW_BLUR: 2,
  DEFAULT_SHADOW_OFFSET_X: 1,
  DEFAULT_SHADOW_OFFSET_Y: 1,

  // 背景相关
  DEFAULT_BACKGROUND_COLOR: "transparent",
  DEFAULT_USE_GRADIENT: false,
  DEFAULT_GRADIENT_COLORS: ["#FFFFFF", "#000000"],

  // 位置相关
  DEFAULT_POSITION_X: 0,
  DEFAULT_POSITION_Y: 0,
  DEFAULT_ORIGIN_X: "center" as const,
  DEFAULT_ORIGIN_Y: "bottom" as const,

  // 变换相关
  DEFAULT_SCALE_X: 1,
  DEFAULT_SCALE_Y: 1,

  // 文本框相关
  DEFAULT_PADDING: 10,
  DEFAULT_BORDER_RADIUS: 8,
  DEFAULT_CURSOR_COLOR: "#FFFFFF",
  DEFAULT_CURSOR_WIDTH: 2,
  DEFAULT_WIDTH_RATIO: 0.8, // 相对于画布宽度
} as const;

// ==================== UI状态常量 ====================
export const UI_CONSTANTS = {
  // 默认菜单选项
  DEFAULT_MENU_OPTION: "Video",

  // 编辑模式
  EDIT_MODES: {
    MOVE: "move",
    HAND: "hand",
  } as const,

  // 加载状态
  DEFAULT_LOADING: false,
  DEFAULT_LOADING_MESSAGE: "",
} as const;

// ==================== 历史记录常量 ====================
export const HISTORY_CONSTANTS = {
  // 操作类型
  ACTION_TYPES: {
    ELEMENT_ADD: "元素添加",
    ELEMENT_DELETE: "元素删除",
    ELEMENT_MODIFY: "元素修改",
    ELEMENT_MOVE: "元素移动",
    ELEMENT_RESIZE: "元素调整大小",
    ELEMENT_ROTATE: "元素旋转",
    ELEMENT_PROPERTY_MODIFY: "元素属性修改",
    CANVAS_ADJUST: "画布调整",
    CAPTION_MODIFY: "字幕修改",
    TRACK_MODIFY: "轨道修改",
    INITIAL_STATE: "初始状态",
    OTHER_OPERATION: "其他操作",
  } as const,
} as const;

// ==================== 形状类型常量 ====================
export const SHAPE_TYPES = {
  RECT: "rect",
  CIRCLE: "circle",
  TRIANGLE: "triangle",
  POLYGON: "polygon",
  ELLIPSE: "ellipse",
  LINE: "line",
  PENTAGON: "pentagon",
  HEXAGON: "hexagon",
  OCTAGON: "octagon",
  ROUNDED_RECT: "roundedRect",
  ARCH: "arch",
  PARALLELOGRAM: "parallelogram",
  DIAMOND: "diamond",
  RIGHT_ARROW: "rightArrow",
  UP_ARROW: "upArrow",
  CROSS: "cross",
  DOWN_ARROW: "downArrow",
  WAVE: "wave",
  STAR: "star",
  FOUR_POINT_STAR: "fourPointStar",
  SIX_POINT_STAR: "sixPointStar",
  EIGHT_POINT_STAR: "eightPointStar",
  SUN_BURST: "sunBurst",
  SEMICIRCLE: "semicircle",
  QUARTER_CIRCLE: "quarterCircle",
  RING: "ring",
  HALF_RING: "halfRing",
  PLUS: "plus",
} as const;

// ==================== 统一导出所有常量 ====================
export const CONSTANTS = {
  CANVAS: CANVAS_CONSTANTS,
  TIMELINE: TIMELINE_CONSTANTS,
  MEDIA: MEDIA_CONSTANTS,
  ELEMENT_TYPES,
  COLOR: COLOR_CONSTANTS,
  ANIMATION: ANIMATION_CONSTANTS,
  SAVE: SAVE_CONSTANTS,
  ELEMENT_OPERATION: ELEMENT_OPERATION_CONSTANTS,
  DRAG: DRAG_CONSTANTS,
  SNAP: SNAP_CONSTANTS,
  TRACK_NAME_TEMPLATES,
  FILTER_METHOD_MAP,
  BASIC_STYLE_UPDATES,
  ELEMENT_CONTENT_MAP,
  CAPTION_STYLE: CAPTION_STYLE_CONSTANTS,
  UI: UI_CONSTANTS,
  HISTORY: HISTORY_CONSTANTS,
  SHAPE_TYPES,
} as const;

// 为了向后兼容，也导出原有的常量结构
export const LEGACY_CONSTANTS = {
  CANVAS: {
    DEFAULT_WIDTH: CANVAS_CONSTANTS.DEFAULT_WIDTH,
    DEFAULT_HEIGHT: CANVAS_CONSTANTS.DEFAULT_HEIGHT,
    DEFAULT_BACKGROUND: CANVAS_CONSTANTS.DEFAULT_BACKGROUND,
    MIN_SCALE: CANVAS_CONSTANTS.MIN_SCALE,
    MAX_SCALE: CANVAS_CONSTANTS.MAX_SCALE,
    DEFAULT_SCALE: CANVAS_CONSTANTS.DEFAULT_SCALE,
  },
  TIMELINE: {
    MAX_TIME: TIMELINE_CONSTANTS.MAX_TIME,
    DEFAULT_DURATION: TIMELINE_CONSTANTS.DEFAULT_DURATION,
    DEFAULT_DISPLAY_DURATION: TIMELINE_CONSTANTS.DEFAULT_DISPLAY_DURATION,
    DEFAULT_ELEMENT_DURATION: TIMELINE_CONSTANTS.DEFAULT_ELEMENT_DURATION,
  },
  ANIMATION: {
    DEFAULT_FPS: TIMELINE_CONSTANTS.DEFAULT_FPS,
    WHEEL_THROTTLE_INTERVAL: TIMELINE_CONSTANTS.WHEEL_THROTTLE_INTERVAL,
  },
  SAVE: {
    DELAY: SAVE_CONSTANTS.DELAY,
    DEFAULT_PROJECT_KEY: SAVE_CONSTANTS.DEFAULT_PROJECT_KEY,
  },
  MEDIA: {
    DEFAULT_VIDEO_HEIGHT: MEDIA_CONSTANTS.DEFAULT_VIDEO_HEIGHT,
    DEFAULT_IMAGE_SIZE: MEDIA_CONSTANTS.DEFAULT_IMAGE_SIZE,
    DEFAULT_SHAPE_SIZE: MEDIA_CONSTANTS.DEFAULT_SHAPE_SIZE,
    DEFAULT_LINE_WIDTH: MEDIA_CONSTANTS.DEFAULT_LINE_WIDTH,
    DEFAULT_LINE_HEIGHT: MEDIA_CONSTANTS.DEFAULT_LINE_HEIGHT,
    DEFAULT_ELLIPSE_WIDTH: MEDIA_CONSTANTS.DEFAULT_ELLIPSE_WIDTH,
    DEFAULT_ELLIPSE_HEIGHT: MEDIA_CONSTANTS.DEFAULT_ELLIPSE_HEIGHT,
  },
  PAN: TIMELINE_CONSTANTS.PAN,
} as const;
