import { makeAutoObservable } from "mobx";
import { Track, EditorElement, TrackType, TimeGap } from "../types";
import { getUid } from "../utils";
import { timeStringToMs } from "../utils/timeUtils";
import { CONSTANTS, TRACK_NAME_TEMPLATES } from "./constants";

// 使用统一的常量定义
const ELEMENT_SPACING_MS = CONSTANTS.ELEMENT_OPERATION.ELEMENT_SPACING_MS;

// 轨道类型到元素类型的映射
const TRACK_TYPE_ELEMENT_MAPPING: Record<TrackType, string[]> = {
  media: ["image", "video", "shape", "gif"],
  text: ["text"],
  audio: ["audio"],
  caption: ["caption"],
};

export class TrackManager {
  private store: any;
  tracks: Track[] = [];
  defaultTracks: Record<TrackType, string> = {
    media: "",
    audio: "",
    text: "",
    caption: "",
  };

  constructor(store: any) {
    this.store = store;
    this.tracks = [];
    makeAutoObservable(this);
  }

  // ==================== 通用工具方法 ====================

  /**
   * 通用查找方法
   */
  private findItemById<T extends { id: string }>(
    items: T[],
    id: string
  ): T | undefined {
    return items.find((item) => item.id === id);
  }

  /**
   * 统一的日志记录方法
   */
  private log(message: string, type: "info" | "warn" | "error" = "info") {
    const prefix = {
      info: "ℹ️",
      warn: "⚠️",
      error: "❌",
    }[type];
    console.log(`${prefix} [TrackManager] ${message}`);
  }

  /**
   * 验证轨道是否存在
   */
  private validateTrack(trackId: string): Track | null {
    const track = this.findTrackById(trackId);
    if (!track) {
      this.log(`轨道 ${trackId} 不存在`, "warn");
      return null;
    }
    return track;
  }

  /**
   * 验证元素是否存在
   */
  private validateElement(elementId: string): EditorElement | null {
    const element = this.findElementById(elementId);
    if (!element) {
      this.log(`元素 ${elementId} 不存在`, "warn");
      return null;
    }
    return element;
  }

  /**
   * 更新元素时间范围的统一方法
   */
  private updateElementTimeFrame(
    element: EditorElement,
    timeFrame: { start: number; end: number },
    triggerMaxTimeUpdate: boolean = true
  ) {
    this.store.updateEditorElementTimeFrame(
      element,
      timeFrame,
      triggerMaxTimeUpdate
    );
  }

  /**
   * 批量移动元素的通用方法
   */
  private moveElementsInTime(
    elements: EditorElement[],
    timeOffset: number,
    direction: "forward" | "backward" = "forward"
  ) {
    const multiplier = direction === "forward" ? 1 : -1;

    elements.forEach((element) => {
      const originalDuration = element.timeFrame.end - element.timeFrame.start;
      const newStartTime = Math.max(
        0,
        element.timeFrame.start + timeOffset * multiplier
      );
      const newEndTime = newStartTime + originalDuration;

      this.log(
        `移动元素 ${element.id}，从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${newStartTime}-${newEndTime}`
      );

      this.updateElementTimeFrame(element, {
        start: newStartTime,
        end: newEndTime,
      });
    });
  }

  // ==================== 核心查找方法 ====================

  /**
   * 根据ID查找轨道
   */
  private findTrackById(trackId: string): Track | undefined {
    return this.findItemById(this.tracks, trackId);
  }

  /**
   * 根据ID查找元素
   */
  private findElementById(elementId: string): EditorElement | undefined {
    return this.findItemById(this.store.editorElements, elementId);
  }

  // ==================== 轨道类型相关方法 ====================

  /**
   * 获取元素对应的轨道类型
   */
  getTrackTypeFromElementType(elementType: string): TrackType {
    for (const [trackType, elementTypes] of Object.entries(
      TRACK_TYPE_ELEMENT_MAPPING
    )) {
      if (elementTypes.includes(elementType)) {
        return trackType as TrackType;
      }
    }
    return elementType as TrackType;
  }

  /**
   * 检查元素类型是否与轨道类型匹配
   */
  private isElementTypeMatchingTrackType(
    elementType: string,
    trackType: TrackType
  ): boolean {
    const allowedTypes = TRACK_TYPE_ELEMENT_MAPPING[trackType] || [];
    return allowedTypes.includes(elementType);
  }

  /**
   * 生成轨道名称
   */
  private generateTrackName(type: TrackType): string {
    const template =
      TRACK_NAME_TEMPLATES[type] ||
      `${type.charAt(0).toUpperCase() + type.slice(1)} Track`;
    return `${template} ${this.getTrackCountByType(type) + 1}`;
  }

  /**
   * 获取指定类型的轨道数量
   */
  getTrackCountByType(type: TrackType): number {
    return this.tracks.filter((track) => track.type === type).length;
  }

  // ==================== 轨道管理方法 ====================

  /**
   * 初始化轨道，创建默认轨道并为现有元素分配轨道
   */
  initializeTracks() {
    this.tracks = [];
    this.createDefaultTracks();
    this.assignElementsToTracks();
  }

  /**
   * 创建默认轨道（媒体、音频、文本、字幕）
   */
  createDefaultTracks() {
    // 当前为空实现，保留接口以便将来扩展
  }

  /**
   * 将现有元素分配到相应类型的默认轨道
   */
  assignElementsToTracks() {
    // 处理字幕
    if (this.store.captions?.length > 0) {
      const captionTrackId = this.defaultTracks.caption;
      if (captionTrackId) {
        // 字幕由CaptionsTrackView单独处理，无需添加到轨道elementIds
      }
    }

    // 处理其他元素
    this.store.editorElements.forEach((element: EditorElement) => {
      this.assignElementToAppropriateTrack(element);
    });
  }

  /**
   * 将元素分配到合适的轨道
   */
  private assignElementToAppropriateTrack(element: EditorElement) {
    const trackType = this.getTrackTypeFromElementType(element.type);
    const defaultTrackId = this.defaultTracks[trackType];

    if (defaultTrackId) {
      this.addElementToTrack(defaultTrackId, element.id);
    } else {
      const track = this.createTrack(trackType);
      this.addElementToTrack(track.id, element.id);
    }
  }

  /**
   * 创建一个新的轨道
   */
  createTrack(
    type: TrackType,
    name?: string,
    setAsDefault: boolean = true
  ): Track {
    const trackId = getUid();
    const track: Track = {
      id: trackId,
      name: name || this.generateTrackName(type),
      type,
      elementIds: [],
      isVisible: true,
      isLocked: false,
    };

    this.tracks.unshift(track);

    if (!this.defaultTracks[type] && setAsDefault) {
      this.setDefaultTrack(type, trackId);
      this.log(`设置轨道 "${track.name}" 为 ${type} 类型的默认轨道`);
    }

    return track;
  }

  // ==================== 默认轨道管理 ====================

  /**
   * 获取指定类型的默认轨道ID
   */
  getDefaultTrackId(type: TrackType): string | null {
    return this.defaultTracks[type] || null;
  }

  /**
   * 设置指定类型的默认轨道
   */
  setDefaultTrack(type: TrackType, trackId: string) {
    this.defaultTracks[type] = trackId;
  }

  /**
   * 清除默认轨道设置
   */
  clearDefaultTrack(type: TrackType) {
    this.defaultTracks[type] = "";
  }

  /**
   * 检查轨道是否为默认轨道
   */
  isDefaultTrack(trackId: string): boolean {
    return Object.values(this.defaultTracks).includes(trackId);
  }

  // ==================== 元素轨道操作 ====================

  /**
   * 向轨道添加元素
   */
  addElementToTrack(trackId: string, elementId: string) {
    const track = this.validateTrack(trackId);
    if (!track) return;

    this.removeElementFromAllTracks(elementId);
    track.elementIds.push(elementId);

    const element = this.findElementById(elementId);
    if (element) {
      element.trackId = trackId;
    }
  }

  /**
   * 从所有轨道中移除元素
   */
  removeElementFromAllTracks(elementId: string) {
    this.tracks.forEach((track) => {
      const index = track.elementIds.indexOf(elementId);
      if (index !== -1) {
        track.elementIds.splice(index, 1);

        const element = this.findElementById(elementId);
        if (element) {
          element.trackId = undefined;
        }
      }
    });
  }

  /**
   * 将元素从一个轨道移动到另一个轨道
   */
  moveElementToTrack(elementId: string, targetTrackId: string): boolean {
    const element = this.validateElement(elementId);
    const targetTrack = this.validateTrack(targetTrackId);

    if (!element || !targetTrack) return false;

    this.addElementToTrack(targetTrackId, elementId);
    this.fixTrackElementsOverlap(targetTrackId);
    this.store.updateCanvasOrderByTrackOrder();
    this.store.setSelectedElement(element);

    return true;
  }

  // ==================== 轨道删除和清理 ====================

  /**
   * 处理默认轨道删除的通用逻辑
   */
  private handleTrackDeletion(track: Track, isDefaultTrack: boolean): string {
    const trackType = track.type;

    if (isDefaultTrack) {
      this.clearDefaultTrack(trackType);
      const sameTypeTrack = this.tracks.find(
        (t) => t.type === trackType && t.id !== track.id
      );

      if (sameTypeTrack) {
        this.setDefaultTrack(trackType, sameTypeTrack.id);
        return sameTypeTrack.id;
      }
    }

    const defaultTrackId = this.defaultTracks[trackType];
    if (defaultTrackId && this.tracks.some((t) => t.id === defaultTrackId)) {
      return defaultTrackId;
    }

    const newTrack = this.createTrack(trackType, undefined, true);
    return newTrack.id;
  }

  /**
   * 删除轨道
   */
  deleteTrack(trackId: string, forceDelete: boolean = false) {
    const index = this.tracks.findIndex((t) => t.id === trackId);
    if (index === -1) return;

    const track = this.tracks[index];
    const isDefaultTrack = this.isDefaultTrack(trackId);

    if (isDefaultTrack && !forceDelete) {
      this.log(
        `尝试删除默认轨道 "${track.name}"，但未启用强制删除模式`,
        "warn"
      );
      return;
    }

    if (track.elementIds.length > 0) {
      const targetTrackId = this.handleTrackDeletion(track, isDefaultTrack);
      this.migrateTrackElements(track, targetTrackId);
    }

    if (isDefaultTrack) {
      this.clearDefaultTrack(track.type);
      this.log(`清除 ${track.type} 类型的默认轨道设置`);
    }

    this.tracks.splice(index, 1);
    this.log(`已删除轨道 "${track.name}" (${track.type})`);
    this.store.updateCanvasOrderByTrackOrder();
  }

  /**
   * 迁移轨道中的元素到目标轨道
   */
  private migrateTrackElements(sourceTrack: Track, targetTrackId: string) {
    this.log(
      `轨道 "${sourceTrack.name}" 中有 ${sourceTrack.elementIds.length} 个元素需要处理`
    );

    sourceTrack.elementIds.forEach((elementId) => {
      this.addElementToTrack(targetTrackId, elementId);
      const targetTrack = this.findTrackById(targetTrackId);
      const trackName = targetTrack?.name || "未知轨道";
      this.log(`元素 ${elementId} 移动到轨道 "${trackName}"`);
    });
  }

  /**
   * 检查并删除所有空轨道
   */
  removeEmptyTracks(preserveDefaults: boolean = false) {
    this.log("开始检查空轨道...");

    const emptyTracks = this.tracks.filter(
      (track) => track.elementIds.length === 0
    );
    const { defaultTracks, nonDefaultTracks } =
      this.categorizeEmptyTracks(emptyTracks);

    this.log(
      `空轨道统计: 总计${emptyTracks.length}个, 默认轨道${defaultTracks.length}个, 非默认轨道${nonDefaultTracks.length}个`
    );

    const tracksToRemove = preserveDefaults ? nonDefaultTracks : emptyTracks;

    if (tracksToRemove.length > 0) {
      this.log(
        `将删除以下空轨道: ${tracksToRemove
          .map((t) => `${t.name} (${t.type})`)
          .join(", ")}`
      );

      tracksToRemove.forEach((track) => this.deleteTrack(track.id, true));
      this.store.updateCanvasOrderByTrackOrder();
      this.log(`成功删除了 ${tracksToRemove.length} 个空轨道`);
    } else {
      this.log("没有需要删除的空轨道");
    }

    return tracksToRemove.length > 0;
  }

  /**
   * 分类空轨道（默认/非默认）
   */
  private categorizeEmptyTracks(emptyTracks: Track[]) {
    const defaultTrackIds = Object.values(this.defaultTracks);

    return {
      defaultTracks: emptyTracks.filter((track) =>
        defaultTrackIds.includes(track.id)
      ),
      nonDefaultTracks: emptyTracks.filter(
        (track) => !defaultTrackIds.includes(track.id)
      ),
    };
  }

  // ==================== 元素查询和排序 ====================

  /**
   * 获取元素所在的轨道
   */
  getTrackByElementId(elementId: string): Track | undefined {
    return this.tracks.find((track) => track.elementIds.includes(elementId));
  }

  /**
   * 获取轨道中的所有元素
   */
  getElementsByTrackId(trackId: string): EditorElement[] {
    const track = this.findTrackById(trackId);
    if (!track) return [];

    return track.elementIds
      .map((id) => this.findElementById(id))
      .filter(Boolean) as EditorElement[];
  }

  /**
   * 获取轨道中的元素，按时间排序
   */
  getSortedElementsByTrackId(trackId: string): EditorElement[] {
    const elements = this.getElementsByTrackId(trackId);
    return elements.sort((a, b) => a.timeFrame.start - b.timeFrame.start);
  }

  /**
   * 获取所有轨道中的元素，按轨道分组
   */
  getAllTrackElements(): { track: Track; elements: EditorElement[] }[] {
    return this.tracks.map((track) => ({
      track,
      elements: this.getElementsByTrackId(track.id),
    }));
  }

  /**
   * 获取所有元素，按轨道从上到下，同一轨道从左到右排序
   * 轨道顺序：tracks数组中索引0的轨道在最上方（前景），索引越大的轨道越在下方（背景）
   * 元素层级：前景轨道的元素应该有更高的z-index，背景轨道的元素应该有更低的z-index
   * 同轨道排序：同一轨道内的元素按时间从左到右排序（开始时间升序）
   *
   * 注意：由于fabric.js的moveTo方法中，较大的索引值对应更高的z-index，
   * 所以我们需要从最后一个轨道开始遍历，让前景轨道的元素在数组后面获得更高的索引
   */
  getAllElementsInDisplayOrder(): EditorElement[] {
    const trackElements = this.getAllTrackElements();
    const result: EditorElement[] = [];

    // 从最后一个轨道开始遍历（索引大的轨道为背景层）
    // 这样背景轨道的元素会被添加到数组前面，获得较低的z-index
    // 前景轨道的元素会被添加到数组后面，获得较高的z-index
    for (let i = trackElements.length - 1; i >= 0; i--) {
      const { elements } = trackElements[i];
      // 同一轨道内的元素按时间从左到右排序（开始时间升序）
      const sortedElements = [...elements].sort(
        (a, b) => a.timeFrame.start - b.timeFrame.start
      );
      result.push(...sortedElements);
    }

    return result;
  }

  // ==================== 重叠检测和处理 ====================

  /**
   * 检查两个元素是否在时间线上重叠
   */
  areElementsOverlapping(
    element1: EditorElement,
    element2: EditorElement
  ): boolean {
    return (
      (element1.timeFrame.start <= element2.timeFrame.start &&
        element2.timeFrame.start < element1.timeFrame.end) ||
      (element2.timeFrame.start <= element1.timeFrame.start &&
        element1.timeFrame.start < element2.timeFrame.end)
    );
  }

  /**
   * 检查两个元素是否在同一轨道
   */
  areElementsInSameTrack(elementId1: string, elementId2: string): boolean {
    const track = this.getTrackByElementId(elementId1);
    return track ? track.elementIds.includes(elementId2) : false;
  }

  /**
   * 检查元素在轨道中是否与其他元素重叠，并提供调整建议
   */
  checkElementOverlap(
    element: EditorElement,
    trackId: string
  ): { startTime: number; endTime: number; hasOverlap: boolean } {
    const track = this.validateTrack(trackId);
    if (!track) {
      return {
        startTime: element.timeFrame.start,
        endTime: element.timeFrame.end,
        hasOverlap: false,
      };
    }

    const trackElements = this.getSortedElementsByTrackId(trackId).filter(
      (el) => el.id !== element.id
    );
    if (trackElements.length === 0) {
      return {
        startTime: element.timeFrame.start,
        endTime: element.timeFrame.end,
        hasOverlap: false,
      };
    }

    return this.calculateOverlapAdjustment(element, trackElements);
  }

  /**
   * 计算重叠调整方案
   */
  private calculateOverlapAdjustment(
    element: EditorElement,
    trackElements: EditorElement[]
  ) {
    let adjustedStartTime = element.timeFrame.start;
    let adjustedEndTime = element.timeFrame.end;
    let hasOverlap = false;
    const duration = element.timeFrame.end - element.timeFrame.start;

    // 处理从0开始的特殊情况
    if (element.timeFrame.start === 0 && trackElements.length > 0) {
      const firstElement = trackElements[0];
      const availableSpace = firstElement.timeFrame.start;

      if (availableSpace < duration) {
        this.log(
          `检测到从0开始的元素需要 ${duration}ms 空间，但只有 ${availableSpace}ms 可用，将向右平推所有元素`
        );
        const pushDistance = duration - availableSpace;
        this.pushElementsRight(trackElements[0].trackId!, pushDistance);
        return {
          startTime: element.timeFrame.start,
          endTime: element.timeFrame.end,
          hasOverlap: false,
        };
      }
    }

    // 检查重叠元素
    const overlappingElements = trackElements.filter((otherElement) =>
      this.areElementsOverlapping(element, otherElement)
    );

    if (overlappingElements.length > 0) {
      hasOverlap = true;
      // 使用改进的位置查找算法，确保不与任何元素重叠
      const adjustment = this.findNonOverlappingPosition(
        element,
        trackElements,
        duration
      );
      adjustedStartTime = adjustment.startTime;
      adjustedEndTime = adjustment.endTime;
    }

    return {
      startTime: adjustedStartTime,
      endTime: adjustedEndTime,
      hasOverlap,
    };
  }

  /**
   * 找到一个不与任何元素重叠的位置
   */
  private findNonOverlappingPosition(
    element: EditorElement,
    trackElements: EditorElement[],
    duration: number
  ) {
    // 按时间顺序排序所有元素
    const sortedElements = [...trackElements].sort(
      (a, b) => a.timeFrame.start - b.timeFrame.start
    );

    // 尝试在每个间隙中放置元素
    let bestPosition = {
      startTime: element.timeFrame.start,
      endTime: element.timeFrame.end,
    };

    // 首先尝试在开头放置（从0开始）
    if (
      sortedElements.length === 0 ||
      sortedElements[0].timeFrame.start >= duration
    ) {
      return { startTime: 0, endTime: duration };
    }

    // 尝试在元素之间的间隙中放置
    for (let i = 0; i < sortedElements.length - 1; i++) {
      const currentElement = sortedElements[i];
      const nextElement = sortedElements[i + 1];

      const gapStart = currentElement.timeFrame.end + ELEMENT_SPACING_MS;
      const gapEnd = nextElement.timeFrame.start;
      const availableSpace = gapEnd - gapStart;

      if (availableSpace >= duration) {
        // 找到了足够的空间
        return {
          startTime: gapStart,
          endTime: gapStart + duration,
        };
      }
    }

    // 如果没有找到合适的间隙，放在最后一个元素之后
    const lastElement = sortedElements[sortedElements.length - 1];
    const newStartTime = lastElement.timeFrame.end + ELEMENT_SPACING_MS;

    return {
      startTime: newStartTime,
      endTime: newStartTime + duration,
    };
  }

  /**
   * 找到最佳调整位置（保留原方法作为备用）
   */
  private findBestAdjustmentPosition(
    element: EditorElement,
    overlappingElement: EditorElement,
    duration: number
  ) {
    const distanceToEnd = Math.abs(
      element.timeFrame.start - overlappingElement.timeFrame.end
    );
    const distanceToStart = Math.abs(
      element.timeFrame.end - overlappingElement.timeFrame.start
    );

    let startTime: number, endTime: number;

    if (distanceToEnd <= distanceToStart) {
      startTime = overlappingElement.timeFrame.end + ELEMENT_SPACING_MS;
      endTime = startTime + duration;
    } else {
      endTime = overlappingElement.timeFrame.start - ELEMENT_SPACING_MS;
      startTime = endTime - duration;
    }

    if (startTime < 0) {
      startTime = 0;
      endTime = duration;
    }

    return { startTime, endTime };
  }

  /**
   * 向右平推轨道中的所有元素
   */
  pushElementsRight(trackId: string, distance: number) {
    const elements = this.getSortedElementsByTrackId(trackId);
    if (elements.length === 0) return;

    this.log(
      `向右平推轨道 ${trackId} 中的 ${elements.length} 个元素，距离 ${distance}ms`
    );
    this.moveElementsInTime(elements, distance, "forward");
  }

  /**
   * 修复轨道中所有元素的重叠问题
   */
  fixTrackElementsOverlap(trackId: string) {
    const elements = this.getSortedElementsByTrackId(trackId);
    if (elements.length <= 1) return;

    let hasOverlap = false;

    // 检查并修复所有重叠
    for (let i = 1; i < elements.length; i++) {
      const currentElement = elements[i];
      const previousElement = elements[i - 1];

      if (this.areElementsOverlapping(currentElement, previousElement)) {
        hasOverlap = true;
        const currentDuration =
          currentElement.timeFrame.end - currentElement.timeFrame.start;
        const newStartTime = previousElement.timeFrame.end + ELEMENT_SPACING_MS;
        const newEndTime = newStartTime + currentDuration;

        this.log(
          `修复重叠: 元素 ${currentElement.id} 从 ${currentElement.timeFrame.start}-${currentElement.timeFrame.end} 调整到 ${newStartTime}-${newEndTime}`
        );

        this.updateElementTimeFrame(currentElement, {
          start: newStartTime,
          end: newEndTime,
        });
      }
    }

    // 如果有重叠被修复，递归检查是否还有其他重叠
    if (hasOverlap) {
      this.fixTrackElementsOverlap(trackId);
    }
  }

  // ==================== 新元素处理 ====================

  /**
   * 获取目标轨道ID（基于选中元素或默认轨道）
   */
  private getTargetTrackId(elementType: string): string {
    const trackType = this.getTrackTypeFromElementType(elementType);

    if (this.store.selectedElement) {
      return this.getTargetTrackIdFromSelection(elementType, trackType);
    } else {
      return this.getTargetTrackIdFromDefault(trackType);
    }
  }

  /**
   * 基于选中元素获取目标轨道ID
   */
  private getTargetTrackIdFromSelection(
    elementType: string,
    trackType: TrackType
  ): string {
    const selectedElementTrack = this.getTrackByElementId(
      this.store.selectedElement.id
    );
    const selectedElementType = this.getTrackTypeFromElementType(
      this.store.selectedElement.type
    );

    if (trackType === selectedElementType && selectedElementTrack) {
      return selectedElementTrack.id;
    } else {
      const hasDefaultTrack = !!this.defaultTracks[trackType];
      const track = this.createTrack(trackType, undefined, !hasDefaultTrack);
      return track.id;
    }
  }

  /**
   * 基于默认轨道获取目标轨道ID
   */
  private getTargetTrackIdFromDefault(trackType: TrackType): string {
    const defaultTrackId = this.defaultTracks[trackType];
    const defaultTrackExists =
      defaultTrackId &&
      this.tracks.some((track) => track.id === defaultTrackId);

    if (defaultTrackExists) {
      return defaultTrackId;
    } else {
      this.log(`${trackType} 类型的默认轨道不存在，创建新的默认轨道`);
      const track = this.createTrack(trackType, undefined, true);
      return track.id;
    }
  }

  /**
   * 当添加新元素时，将其添加到适当的轨道，并进行智能碰撞检测
   */
  handleNewElement(element: EditorElement) {
    const targetTrackId = this.getTargetTrackId(element.type);
    const originalTimeFrame = { ...element.timeFrame };

    const { startTime, endTime, hasOverlap } = this.checkElementOverlap(
      element,
      targetTrackId
    );

    if (hasOverlap) {
      this.log(
        `检测到时间碰撞，调整元素时间从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${startTime}-${endTime}`
      );
      element.timeFrame = { start: startTime, end: endTime };
      this.store.updateMaxTime();
    }

    this.addElementToTrack(targetTrackId, element.id);

    return {
      targetTrackId,
      hasOverlap,
      originalTime: originalTimeFrame,
      adjustedTime: { start: startTime, end: endTime },
    };
  }

  /**
   * 当删除元素时，从轨道中移除
   */
  handleElementDeleted(elementId: string) {
    this.log(`处理元素删除: ${elementId}`);
    this.removeElementFromAllTracks(elementId);
    this.removeEmptyTracks();
  }

  // ==================== 时间间隙处理 ====================

  /**
   * 检测轨道中元素之间的时间间隙
   */
  detectTimeGaps(trackId: string): TimeGap[] {
    const elements = this.getSortedElementsByTrackId(trackId);
    const gaps: TimeGap[] = [];

    if (elements.length === 0) return gaps;

    // 检查第一个元素之前的间隙
    const firstElement = elements[0];
    if (firstElement.timeFrame.start > 0) {
      gaps.push({
        id: `gap-start-${trackId}`,
        startTime: 0,
        endTime: firstElement.timeFrame.start,
        duration: firstElement.timeFrame.start,
        trackId,
        afterElementId: firstElement.id,
      });
    }

    // 检查相邻元素之间的间隙
    for (let i = 0; i < elements.length - 1; i++) {
      const currentElement = elements[i];
      const nextElement = elements[i + 1];

      if (currentElement.timeFrame.end < nextElement.timeFrame.start) {
        const gapDuration =
          nextElement.timeFrame.start - currentElement.timeFrame.end;
        gaps.push({
          id: `gap-${currentElement.id}-${nextElement.id}`,
          startTime: currentElement.timeFrame.end,
          endTime: nextElement.timeFrame.start,
          duration: gapDuration,
          trackId,
          beforeElementId: currentElement.id,
          afterElementId: nextElement.id,
        });
      }
    }

    return gaps;
  }

  /**
   * 删除时间间隙，将后续元素向前移动
   */
  deleteTimeGap(gap: TimeGap) {
    this.log(`删除间隙: ${gap.id}, 时长: ${gap.duration}ms`);

    this.store.startHistoryGroup("删除间隙");

    try {
      const elements = this.getSortedElementsByTrackId(gap.trackId);
      const elementsToMove = elements.filter(
        (element) => element.timeFrame.start >= gap.endTime
      );

      this.log(`需要向前移动 ${elementsToMove.length} 个元素`);
      this.moveElementsInTime(elementsToMove, gap.duration, "backward");

      this.store.updateMaxTime();
      this.store.saveChange("删除间隙");
    } finally {
      setTimeout(() => this.store.endHistoryGroup(), 100);
    }
  }

  // ==================== 其他方法 ====================

  /**
   * 在指定位置创建新轨道
   */
  createTrackAtPosition(
    type: TrackType,
    position: number = -1,
    name?: string
  ): Track {
    const track = this.createTrack(type, name);

    if (position >= 0 && position < this.tracks.length) {
      const tracks = [...this.tracks];
      const newTrack = tracks.shift()!;
      tracks.splice(position, 0, newTrack);
      this.tracks = tracks;
      this.store.updateCanvasOrderByTrackOrder();
    }

    return track;
  }

  /**
   * 清理轨道中无效的元素ID
   */
  cleanupInvalidElementIds(): number {
    let removedCount = 0;

    this.tracks.forEach((track) => {
      const invalidElementIds = track.elementIds.filter(
        (elementId) => !this.findElementById(elementId)
      );

      if (invalidElementIds.length > 0) {
        this.log(
          `从轨道 "${track.name}" 中移除 ${invalidElementIds.length} 个无效的元素ID`
        );
        track.elementIds = track.elementIds.filter(
          (elementId) => !invalidElementIds.includes(elementId)
        );
        removedCount += invalidElementIds.length;
      }
    });

    if (removedCount > 0) {
      this.removeEmptyTracks();
    }

    return removedCount;
  }
}
