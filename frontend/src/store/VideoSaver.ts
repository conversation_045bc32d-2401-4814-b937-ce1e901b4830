import { EditorElement, AudioEditorElement } from "../types";
import { isEditorAudioElement } from "../utils";
import { CONSTANTS } from "./constants";
import { audioManager } from "./AudioManager";

export class VideoSaver {
  private canvas: HTMLCanvasElement;
  private editorElements: EditorElement[];
  private maxTime: number;
  private selectedVideoFormat: string;

  constructor(
    canvas: HTMLCanvasElement,
    editorElements: EditorElement[],
    maxTime: number,
    selectedVideoFormat: string
  ) {
    this.canvas = canvas;
    this.editorElements = editorElements;
    this.maxTime = maxTime;
    this.selectedVideoFormat = selectedVideoFormat;
  }

  saveCanvasToVideoWithAudio() {
    this.saveCanvasToVideoWithAudioWebmMp4();
  }

  private saveCanvasToVideoWithAudioWebmMp4() {
    console.log("modified");
    let mp4 = this.selectedVideoFormat === "mp4";
    const stream = this.canvas.captureStream(CONSTANTS.MEDIA.CAPTURE_FPS);
    const audioElements = this.editorElements.filter(isEditorAudioElement);
    const audioStreams: MediaStream[] = [];
    const audioContexts: AudioContext[] = [];

    audioElements.forEach((audio) => {
      const audioElement = document.getElementById(
        audio.properties.elementId
      ) as HTMLAudioElement;

      if (!audioElement) return;

      // 使用AudioManager创建连接，避免重复创建和冲突
      const audioStream = audioManager.createAudioConnection(
        audioElement,
        audio.properties.elementId
      );

      if (audioStream) {
        audioStreams.push(audioStream);
      }
    });

    audioStreams.forEach((audioStream) => {
      const audioTracks = audioStream.getAudioTracks();
      if (audioTracks.length > 0) {
        stream.addTrack(audioTracks[0]);
      }
    });
    const video = document.createElement("video");
    video.srcObject = stream;
    video.height = CONSTANTS.MEDIA.DEFAULT_VIDEO_HEIGHT_RECORD;
    video.width = CONSTANTS.MEDIA.DEFAULT_VIDEO_WIDTH;
    video.play().then(() => {
      const mediaRecorder = new MediaRecorder(stream);
      const chunks: Blob[] = [];
      mediaRecorder.ondataavailable = function (e) {
        chunks.push(e.data);
        console.log("data available");
      };
      mediaRecorder.onstop = async function (e) {
        const blob = new Blob(chunks, { type: "video/webm" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "video.webm";
        a.click();
      };
      mediaRecorder.start();
      setTimeout(() => {
        mediaRecorder.stop();
      }, this.maxTime);
      video.remove();
    });
  }
}
