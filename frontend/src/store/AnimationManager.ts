import anime from "animejs";
import { Store } from "./Store";
import { FabricUitls } from "../utils/fabric-utils";
import { Animation, EditorElement, TextEditorElement } from "../types";
import { fabric } from "fabric";

function getTextObjectsPartitionedByCharacters(
  textObject: fabric.Text,
  element: TextEditorElement
): fabric.Text[] {
  const copyCharsObjects: fabric.Text[] = [];
  const text = textObject.text ?? "";
  const charObjects = textObject.__charBounds;
  if (!charObjects || text.length === 0) return copyCharsObjects;

  const scaleX = textObject.scaleX ?? 1;
  const scaleY = textObject.scaleY ?? 1;
  const fontSize = textObject.fontSize;
  const fontWeight = textObject.fontWeight;
  const fill = textObject.fill ?? "#fff";
  const lineHeight = textObject.getHeightOfLine(0);

  let charIndex = 0;
  const textObjectPool: fabric.Text[] = [];

  for (let lineIndex = 0; lineIndex < charObjects.length; lineIndex++) {
    const line = charObjects[lineIndex];
    for (let i = 0; i < line.length - 1; i++) {
      const char = text[charIndex];
      if (char === "\n") {
        charIndex++;
        continue;
      }

      const charObject = line[i];
      let charTextObject: fabric.Text;

      if (textObjectPool.length > 0) {
        charTextObject = textObjectPool.pop()!;
        charTextObject.set({
          text: char,
          left: charObject.left * scaleX + element.placement.x,
          top: lineIndex * lineHeight * scaleY + element.placement.y,
        });
      } else {
        charTextObject = new fabric.Text(char, {
          left: charObject.left * scaleX + element.placement.x,
          top: lineIndex * lineHeight * scaleY + element.placement.y,
          scaleX: scaleX,
          scaleY: scaleY,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fill: fill,
        });
      }

      copyCharsObjects.push(charTextObject);
      charIndex++;
    }
  }

  return copyCharsObjects;
}

export class AnimationManager {
  private store: Store;

  constructor(store: Store) {
    this.store = store;
  }

  /**
   * 刷新所有动画。
   * 该方法会清除当前的动画时间轴，重新创建一个新的时间轴，
   * 并根据存储中的动画配置重新添加所有动画效果。
   */
  refreshAnimations() {
    anime.remove(this.store.timelineManager.animationTimeLine);
    this.store.timelineManager.animationTimeLine = anime.timeline({
      duration: this.store.timelineManager.maxTime,
      autoplay: false,
    });

    const elementMap = new Map(
      this.store.editorElements.map((el) => [el.id, el])
    );

    // 首先重置所有元素的fabric对象属性到初始状态
    this.store.editorElements.forEach((element) => {
      const fabricObject = element.fabricObject;
      if (!fabricObject) return;

      // 重置可能被动画修改的属性到元素的实际状态，保持用户设置的所有属性
      const resetProperties: any = {
        opacity: element.opacity || 1,
        scaleX: element.placement.scaleX || 1,
        scaleY: element.placement.scaleY || 1,
        width: element.placement.width,
        height: element.placement.height,
        left: element.placement.x,
        top: element.placement.y,
        angle: element.placement.rotation || 0,
        flipX: element.placement.flipX || false,
        flipY: element.placement.flipY || false,
        clipPath: undefined,
        // 保持锁定状态相关属性
        selectable: !element.locked,
        lockMovementX: element.locked,
        lockMovementY: element.locked,
        lockRotation: element.locked,
        lockScalingX: element.locked,
        lockScalingY: element.locked,
      };

      // 对于媒体元素，还需要重置滤镜和边框属性
      if (
        element.type === "image" ||
        element.type === "video" ||
        element.type === "gif"
      ) {
        const mediaElement = element as any;
        if (mediaElement.properties) {
          // 重置滤镜属性
          if (mediaElement.properties.filters) {
            resetProperties.brightness =
              mediaElement.properties.filters.brightness || 0;
            resetProperties.contrast =
              mediaElement.properties.filters.contrast || 0;
            resetProperties.saturation =
              mediaElement.properties.filters.saturation || 0;
            resetProperties.hue = mediaElement.properties.filters.hue || 0;
            resetProperties.blur = mediaElement.properties.filters.blur || 0;
          }
          // 重置边框属性
          if (mediaElement.properties.border) {
            resetProperties.imageBorderColor =
              mediaElement.properties.border.color || "transparent";
            resetProperties.borderWidth =
              mediaElement.properties.border.width || 0;
            resetProperties.borderStyle =
              mediaElement.properties.border.style || "solid";
            resetProperties.borderRadius =
              mediaElement.properties.border.borderRadius || 0;
          }
          // 重置效果属性
          if (mediaElement.properties.effect) {
            resetProperties.customFilter =
              mediaElement.properties.effect.type || "none";
          }
          // 重置裁剪属性
          if (element.placement.cropX !== undefined)
            resetProperties.cropX = element.placement.cropX;
          if (element.placement.cropY !== undefined)
            resetProperties.cropY = element.placement.cropY;
          if (element.placement.cropWidth !== undefined)
            resetProperties.cropWidth = element.placement.cropWidth;
          if (element.placement.cropHeight !== undefined)
            resetProperties.cropHeight = element.placement.cropHeight;
        }
      }

      // 对于文本元素，重置文本相关属性
      if (element.type === "text") {
        const textElement = element as any;
        if (textElement.properties) {
          resetProperties.text = textElement.properties.text || "";
          resetProperties.fontSize = textElement.properties.fontSize || 100;
          resetProperties.fontFamily =
            textElement.properties.fontFamily || "Arial";
          resetProperties.textAlign =
            textElement.properties.textAlign || "left";
          resetProperties.lineHeight = textElement.properties.lineHeight || 1;
          resetProperties.charSpacing = textElement.properties.charSpacing || 0;
          resetProperties.fontWeight =
            textElement.properties.fontWeight || "normal";
          resetProperties.fill = textElement.properties.fontColor || "#000000";
          resetProperties.strokeWidth = textElement.properties.strokeWidth || 0;
          resetProperties.stroke =
            textElement.properties.strokeColor || "#000000";
          resetProperties.backgroundColor =
            textElement.properties.backgroundColor || "";

          // 处理样式数组
          const styles = textElement.properties.styles || [];
          resetProperties.fontStyle = styles.includes("italic")
            ? "italic"
            : "normal";
          resetProperties.underline = styles.includes("underlined");
          resetProperties.linethrough = styles.includes("strikethrough");
        }
      }

      fabricObject.set(resetProperties);

      // 清理文本分割对象
      if (element.type === "text" && element.properties?.splittedTexts) {
        element.properties.splittedTexts.forEach((textObject) => {
          this.store.canvas?.remove(textObject);
        });
        element.properties.splittedTexts = [];
      }
    });

    this.store.animations.forEach((animation) => {
      const editorElement = elementMap.get(animation.targetId);
      const fabricObject = editorElement?.fabricObject;
      if (!editorElement || !fabricObject) return;

      const animationHandlers = {
        fadeIn: () => this.handleFadeIn(animation, editorElement, fabricObject),
        fadeOut: () =>
          this.handleFadeOut(animation, editorElement, fabricObject),
        slideIn: () =>
          this.handleSlideIn(animation, editorElement, fabricObject),
        slideOut: () =>
          this.handleSlideOut(animation, editorElement, fabricObject),
        breathe: () =>
          this.handleBreathe(animation, editorElement, fabricObject),
        rotate: () => this.handleRotate(animation, editorElement, fabricObject),
        bounce: () => this.handleBounce(animation, editorElement, fabricObject),
        shake: () => this.handleShake(animation, editorElement, fabricObject),
        flash: () => this.handleFlash(animation, editorElement, fabricObject),
        zoom: () => this.handleZoom(animation, editorElement, fabricObject),
        zoomIn: () => this.handleZoomIn(animation, editorElement, fabricObject),
        zoomOut: () =>
          this.handleZoomOut(animation, editorElement, fabricObject),
      };

      const handler = animationHandlers[animation.type];
      if (handler) handler();
    });

    // 重新设置动画时间线到当前时间，确保元素状态正确
    this.store.timelineManager.animationTimeLine.seek(
      this.store.currentTimeInMs
    );
  }

  /**
   * 处理淡入动画效果。
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric.js对象
   */
  private handleFadeIn(animation, editorElement, fabricObject) {
    // 使用更平滑的缓动函数
    const easing = "easeInOutQuad";

    // 添加轻微的缩放和上移效果增强视觉体验
    this.store.timelineManager.animationTimeLine.add(
      {
        opacity: [0, 1],
        scale: [0.95, 1], // 从稍小到正常大小
        // translateY: [10, 0], // 轻微上移
        duration: animation.duration,
        targets: fabricObject,
        easing: easing,
      },
      editorElement.timeFrame.start
    );
  }

  /**
   * Handle fade out animation effect.
   * @param animation Animation configuration object
   * @param editorElement Editor element object
   * @param fabricObject fabric.js object
   */
  private handleFadeOut(animation, editorElement, fabricObject) {
    // 使用更平滑的缓动函数
    const easing = "easeOutQuad";

    // 添加scale缩小效果增强视觉体验
    this.store.timelineManager.animationTimeLine.add(
      {
        opacity: [1, 0],
        scale: [1, 0.95], // 轻微缩小增加渐隐效果
        duration: animation.duration,
        targets: fabricObject,
        easing: easing,
      },
      editorElement.timeFrame.end - animation.duration
    );
  }

  /**
   * 处理滑入动画效果。
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric.js对象
   */
  private handleSlideIn(animation, editorElement, fabricObject) {
    const properties = animation.properties || {};
    const { direction, useClipPath, textType } = properties;
    const { placement } = editorElement;
    const targetPosition = { left: placement.x, top: placement.y };
    const startPosition = this.calculateStartPosition(
      direction,
      placement,
      this.store.canvas
    );

    if (useClipPath) {
      fabricObject.set(
        "clipPath",
        FabricUitls.getClipMaskRect(editorElement, 50)
      );
    }

    if (editorElement.type === "text" && textType === "character") {
      this.handleTextSlideIn(
        animation,
        editorElement,
        fabricObject,
        startPosition,
        targetPosition
      );
    } else {
      // 添加缩放和透明度效果增强视觉体验
      this.store.timelineManager.animationTimeLine.add(
        {
          left: [startPosition.left, targetPosition.left],
          top: [startPosition.top, targetPosition.top],
          opacity: [0, 1], // 从半透明到不透明
          scale: [0.95, 1], // 从稍小到正常大小
          duration: animation.duration,
          targets: fabricObject,
          easing: "easeOutQuad", // 使用更平滑的缓动函数
        },
        editorElement.timeFrame.start
      );
    }
  }

  /**
   * 处理滑出动画效果。
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric.js对象
   */
  private handleSlideOut(animation, editorElement, fabricObject) {
    const properties = animation.properties || {};
    const { direction, useClipPath, textType } = properties;
    const { placement } = editorElement;
    const startPosition = { left: placement.x, top: placement.y };
    const targetPosition = this.calculateTargetPosition(
      direction,
      placement,
      this.store.canvas
    );

    if (useClipPath) {
      fabricObject.set(
        "clipPath",
        FabricUitls.getClipMaskRect(editorElement, 50)
      );
    }

    if (editorElement.type === "text" && textType === "character") {
      this.handleTextSlideOut(
        animation,
        editorElement,
        fabricObject,
        startPosition,
        targetPosition
      );
    } else {
      this.store.timelineManager.animationTimeLine.add(
        {
          left: [startPosition.left, targetPosition.left],
          top: [startPosition.top, targetPosition.top],
          opacity: [1, 0],
          duration: animation.duration,
          targets: fabricObject,
          easing: "easeInOutQuad",
        },
        editorElement.timeFrame.end - animation.duration
      );
    }
  }

  /**
   * 处理呼吸动画效果。
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric.js对象
   */
  private handleBreathe(animation, editorElement, fabricObject) {
    const { timeFrame } = editorElement;
    const itsSlideInAnimation = this.store.animations.find(
      (a) => a.targetId === animation.targetId && a.type === "slideIn"
    );
    const itsSlideOutAnimation = this.store.animations.find(
      (a) => a.targetId === animation.targetId && a.type === "slideOut"
    );

    const timeEndOfSlideIn = itsSlideInAnimation
      ? timeFrame.start + itsSlideInAnimation.duration
      : timeFrame.start;
    const timeStartOfSlideOut = itsSlideOutAnimation
      ? timeFrame.end - itsSlideOutAnimation.duration
      : timeFrame.end;

    if (timeEndOfSlideIn > timeStartOfSlideOut) return;

    const duration = timeStartOfSlideOut - timeEndOfSlideIn;
    const easeFactor = 4;
    const suitableTimeForHeartbeat = ((1000 * 60) / 72) * easeFactor;
    const totalHeartbeats = Math.floor(duration / suitableTimeForHeartbeat);

    if (totalHeartbeats < 1) return;

    const upScale = 1.05;
    const currentScaleX = fabricObject.scaleX ?? 1;
    const currentScaleY = fabricObject.scaleY ?? 1;
    const finalScaleX = currentScaleX * upScale;
    const finalScaleY = currentScaleY * upScale;

    const keyframes = Array(totalHeartbeats)
      .fill(null)
      .flatMap(() => [
        { scaleX: finalScaleX, scaleY: finalScaleY },
        { scaleX: currentScaleX, scaleY: currentScaleY },
      ]);

    this.store.timelineManager.animationTimeLine.add(
      {
        duration: duration,
        targets: fabricObject,
        keyframes,
        easing: "linear",
        loop: true,
      },
      timeEndOfSlideIn
    );
  }

  /**
   * 计算起始位置。
   * @param direction 方向
   * @param placement 位置
   * @param canvas 画布
   */
  private calculateStartPosition(direction, placement, canvas) {
    const canvasWidth = canvas?.width || 1920; // 默认宽度
    const canvasHeight = canvas?.height || 1080; // 默认高度

    return {
      left:
        direction === "left"
          ? -placement.width
          : direction === "right"
          ? canvasWidth
          : placement.x,
      top:
        direction === "top"
          ? -placement.height
          : direction === "bottom"
          ? canvasHeight
          : placement.y,
    };
  }

  /**
   * 计算目标位置。
   * @param direction 方向
   * @param placement 位置
   * @param canvas 画布
   */
  private calculateTargetPosition(direction, placement, canvas) {
    const canvasWidth = canvas?.width || 1920; // 默认宽度
    const canvasHeight = canvas?.height || 1080; // 默认高度

    return {
      left:
        direction === "left"
          ? -placement.width
          : direction === "right"
          ? canvasWidth
          : placement.x,
      top:
        direction === "top"
          ? -100 - placement.height
          : direction === "bottom"
          ? canvasHeight
          : placement.y,
    };
  }

  /**
   * 处理文本滑入动画。
   * @param animation 动画配置
   * @param editorElement 编辑元素
   * @param fabricObject fabric对象
   * @param startPosition 起始位置
   * @param targetPosition 目标位置
   */
  private handleTextSlideIn(
    animation,
    editorElement,
    fabricObject,
    startPosition,
    targetPosition
  ) {
    // 安全清理之前的分割文本
    if (editorElement.properties?.splittedTexts?.length > 0) {
      editorElement.properties.splittedTexts.forEach((textObj) => {
        this.store.canvas?.remove(textObj);
      });
      editorElement.properties.splittedTexts = [];
    }

    if (fabricObject instanceof fabric.Text) {
      if (!editorElement.properties) {
        editorElement.properties = {};
      }
      editorElement.properties.splittedTexts =
        getTextObjectsPartitionedByCharacters(fabricObject, editorElement);
    } else {
      console.warn(
        "editorElement.fabricObject is not an instance of fabric.Text"
      );
      if (!editorElement.properties) {
        editorElement.properties = {};
      }
      editorElement.properties.splittedTexts = [];
      return; // 如果不是文本对象，直接返回
    }

    editorElement.properties.splittedTexts.forEach((textObject) =>
      this.store.canvas!.add(textObject)
    );

    const duration = animation.duration / 2;
    const delay = duration / editorElement.properties.splittedTexts.length;

    editorElement.properties.splittedTexts.forEach((splittedText, i) => {
      const offset = {
        left: splittedText.left! - editorElement.placement.x,
        top: splittedText.top! - editorElement.placement.y,
      };

      this.store.timelineManager.animationTimeLine.add(
        {
          left: [
            startPosition.left! + offset.left,
            targetPosition.left + offset.left,
          ],
          top: [
            startPosition.top! + offset.top,
            targetPosition.top + offset.top,
          ],
          delay: i * delay,
          duration: duration,
          targets: splittedText,
        },
        editorElement.timeFrame.start
      );
    });

    this.handleTextOpacity(animation, editorElement, fabricObject);
  }

  /**
   * 处理文本滑出动画。
   * @param animation 动画配置
   * @param editorElement 编辑元素
   * @param fabricObject fabric对象
   * @param startPosition 起始位置
   * @param targetPosition 目标位置
   */
  private handleTextSlideOut(
    animation,
    editorElement,
    fabricObject,
    startPosition,
    targetPosition
  ) {
    // 安全清理之前的分割文本
    if (editorElement.properties?.splittedTexts?.length > 0) {
      editorElement.properties.splittedTexts.forEach((textObj) => {
        this.store.canvas?.remove(textObj);
      });
      editorElement.properties.splittedTexts = [];
    }

    if (fabricObject instanceof fabric.Text) {
      if (!editorElement.properties) {
        editorElement.properties = {};
      }
      editorElement.properties.splittedTexts =
        getTextObjectsPartitionedByCharacters(fabricObject, editorElement);
    } else {
      console.warn(
        "editorElement.fabricObject is not an instance of fabric.Text"
      );
      if (!editorElement.properties) {
        editorElement.properties = {};
      }
      editorElement.properties.splittedTexts = [];
      return; // 如果不是文本对象，直接返回
    }

    editorElement.properties.splittedTexts.forEach((textObject) =>
      this.store.canvas!.add(textObject)
    );

    const duration = animation.duration / 2;
    const delay = duration / editorElement.properties.splittedTexts.length;

    editorElement.properties.splittedTexts.forEach((splittedText, i) => {
      const offset = {
        left: splittedText.left! - editorElement.placement.x,
        top: splittedText.top! - editorElement.placement.y,
      };

      this.store.timelineManager.animationTimeLine.add(
        {
          left: [
            startPosition.left + offset.left,
            targetPosition.left + offset.left,
          ],
          top: [
            startPosition.top + offset.top,
            targetPosition.top + offset.top,
          ],
          delay: i * delay,
          duration: duration,
          targets: splittedText,
        },
        editorElement.timeFrame.end - animation.duration
      );
    });

    this.handleTextOpacity(animation, editorElement, fabricObject);
  }

  /**
   * 处理文本透明度动画
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric文本对象
   */
  private handleTextOpacity(animation, editorElement, fabricObject) {
    const opacityAnimations = [
      {
        opacity: [1, 0],
        targets: fabricObject,
        start: editorElement.timeFrame.start,
      },
      {
        opacity: [0, 1],
        targets: fabricObject,
        start: editorElement.timeFrame.start + animation.duration,
      },
      {
        opacity: [0, 1],
        targets: editorElement.properties.splittedTexts,
        start: editorElement.timeFrame.start,
      },
      {
        opacity: [1, 0],
        targets: editorElement.properties.splittedTexts,
        start: editorElement.timeFrame.start + animation.duration,
      },
    ];

    opacityAnimations.forEach(({ opacity, targets, start }) => {
      this.store.timelineManager.animationTimeLine.add(
        {
          opacity,
          duration: 100, // 增加到100ms，使透明度变化更平滑
          targets,
          easing: "linear",
        },
        start
      );
    });
  }

  /**
   * 处理旋转动画
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric对象
   */
  private handleRotate(
    animation: Animation,
    editorElement: EditorElement,
    fabricObject: fabric.Object
  ) {
    const properties = animation.properties || {};
    const { rotationDegrees = 360, loops = 1 } = properties as {
      rotationDegrees?: number;
      loops?: number;
    };

    this.store.timelineManager.animationTimeLine.add(
      {
        angle: [0, rotationDegrees],
        duration: animation.duration,
        targets: fabricObject,
        easing: "easeInOutQuad",
        loop: loops,
      },
      editorElement.timeFrame.start
    );
  }

  /**
   * 处理弹跳动画
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric对象
   */
  private handleBounce(
    animation: Animation,
    editorElement: EditorElement,
    fabricObject: fabric.Object
  ) {
    const originalTop = editorElement.placement.y;
    const properties = animation.properties || {};
    const bounceHeight = (properties as { height?: number })?.height || 30;

    this.store.timelineManager.animationTimeLine.add(
      {
        top: [originalTop, originalTop - bounceHeight, originalTop],
        duration: animation.duration,
        targets: fabricObject,
        easing: "easeOutElastic(1, .5)",
        loop: true,
      },
      editorElement.timeFrame.start
    );
  }

  /**
   * 处理抖动动画
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric对象
   */
  private handleShake(
    animation: Animation,
    editorElement: EditorElement,
    fabricObject: fabric.Object
  ) {
    const originalLeft = editorElement.placement.x;
    const duration = animation.duration; // 使用动画配置的时长

    // 根据对象大小计算抖动强度
    const objectWidth = fabricObject.getScaledWidth();
    const properties = animation.properties || {};
    const baseIntensity =
      (properties as { intensity?: number })?.intensity || 30;
    const shakeIntensity = Math.min(baseIntensity, objectWidth * 0.2); // 限制最大抖动幅度为对象宽度的20%

    // 根据动画时长计算合适的抖动次数
    const minShakeDuration = 500; // 每次抖动最少需要500ms
    const maxShakeCount = 10; // 最大抖动次数
    const shakeCount = Math.min(maxShakeCount, Math.max(1, duration / 200)); // 每200ms一次抖动

    // 生成抖动关键帧
    let keyframes = [];
    const shakePattern = [0.8, -0.6, 0.4, -0.2, 0]; // 抖动衰减模式

    if (animation.duration <= 500) {
      // 短时间动画使用简单的左右抖动
      keyframes = [
        { left: originalLeft },
        { left: originalLeft - shakeIntensity * 0.6 },
        { left: originalLeft + shakeIntensity * 0.4 },
        { left: originalLeft },
      ];
    } else {
      // 较长时间动画使用更复杂的抖动模式
      keyframes = [{ left: originalLeft }];
      for (let i = 0; i < shakeCount; i++) {
        const dampingFactor = 1 - i / shakeCount; // 随时间衰减
        shakePattern.forEach((factor) => {
          keyframes.push({
            left: originalLeft + shakeIntensity * factor * dampingFactor,
          });
        });
      }
    }

    // 选择合适的缓动函数
    const easing = animation.duration <= 500 ? "linear" : "easeInOutQuad";

    this.store.timelineManager.animationTimeLine.add(
      {
        targets: fabricObject,
        keyframes,
        duration: animation.duration, // 使用动画配置的时长
        easing,
        loop: true,
      },
      editorElement.timeFrame.start
    );
  }

  /**
   * 处理闪烁动画
   * @param animation 动画配置对象
   * @param editorElement 编辑元素对象
   * @param fabricObject fabric对象
   */
  private handleFlash(
    animation: Animation,
    editorElement: EditorElement,
    fabricObject: fabric.Object
  ) {
    const duration = animation.duration; // 使用动画配置的时长
    // 根据动画时长调整闪烁次数和效果
    const flashCount = Math.max(2, Math.floor(duration / 500)); // 每500ms一次闪烁，最少2次
    const keyframes = [];

    // 生成闪烁关键帧
    for (let i = 0; i < flashCount; i++) {
      keyframes.push({ opacity: 0 }, { opacity: 1 });
    }

    this.store.timelineManager.animationTimeLine.add(
      {
        targets: fabricObject,
        keyframes,
        duration: duration,
        easing: duration < 1000 ? "linear" : "easeInOutQuad", // 统一使用duration
        loop: true,
      },
      editorElement.timeFrame.start
    );
  }

  /**
   * 处理缩放动画
   */
  private handleZoom(
    animation: Animation,
    editorElement: EditorElement,
    fabricObject: fabric.Object
  ) {
    const properties = animation.properties || {};
    const { scale = 1.5 } = properties as {
      scale?: number;
    };

    const originalScale = fabricObject.scaleX || 1;
    const keyframes = [
      { scaleX: originalScale, scaleY: originalScale },
      { scaleX: originalScale * scale, scaleY: originalScale * scale },
      { scaleX: originalScale, scaleY: originalScale },
    ];

    this.store.timelineManager.animationTimeLine.add(
      {
        targets: fabricObject,
        keyframes,
        duration: animation.duration, // 移除不必要的翻倍
        easing: "easeInOutQuad",
        loop: true,
      },
      editorElement.timeFrame.start
    );
  }

  /**
   * 处理放大动画效果
   */
  private handleZoomIn(
    animation: Animation,
    editorElement: EditorElement,
    fabricObject: fabric.Object
  ) {
    const properties = animation.properties || {};
    const { scale = 1.5 } = properties as { scale?: number };
    // 存储原始比例，如果没有设置则使用1
    const baseScale = 1;

    this.store.timelineManager.animationTimeLine.add(
      {
        scaleX: [baseScale, baseScale * scale],
        scaleY: [baseScale, baseScale * scale],
        opacity: [0, 1],
        duration: animation.duration,
        targets: fabricObject,
        easing: "easeOutQuad",
      },
      editorElement.timeFrame.start
    );
  }

  /**
   * 处理缩小动画效果
   */
  private handleZoomOut(
    animation: Animation,
    editorElement: EditorElement,
    fabricObject: fabric.Object
  ) {
    const properties = animation.properties || {};
    const { scale = 0.1 } = properties as { scale?: number };
    const originalScale = fabricObject.scaleX || 1;

    this.store.timelineManager.animationTimeLine.add(
      {
        scaleX: [originalScale, originalScale * scale],
        scaleY: [originalScale, originalScale * scale],
        opacity: [1, 0],
        duration: animation.duration,
        targets: fabricObject,
        easing: "easeInQuad",
      },
      editorElement.timeFrame.end - animation.duration
    );
  }

  destroy() {
    // Remove all animations
    anime.remove(this.store.timelineManager.animationTimeLine);
    // Note: animationTimeLine cleanup is handled by TimelineManager

    // Remove all split text objects from the canvas
    this.store.editorElements.forEach((element) => {
      if (element.type === "text" && element.properties?.splittedTexts) {
        element.properties.splittedTexts.forEach((textObject) => {
          this.store.canvas?.remove(textObject);
        });
        element.properties.splittedTexts = [];
      }
    });
    // Clear the animations array
    this.store.animations = [];
  }
}
