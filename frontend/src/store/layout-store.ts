import { makeAutoObservable } from "mobx";

export class LayoutStore {
  activeMenuItem: string | null = null;
  showMenuItem: boolean = false;
  showControlItem: boolean = false;
  showToolboxItem: boolean = false;
  activeToolboxItem: string | null = null;
  controlsVisible: boolean = false;

  constructor() {
    makeAutoObservable(this);
  }

  setActiveMenuItem = (showMenu: string | null) => {
    this.activeMenuItem = showMenu;
  };

  setShowMenuItem = (showMenuItem: boolean) => {
    this.showMenuItem = showMenuItem;
  };

  setShowControlItem = (showControlItem: boolean) => {
    this.showControlItem = showControlItem;
  };

  setShowToolboxItem = (showToolboxItem: boolean) => {
    this.showToolboxItem = showToolboxItem;
  };

  setActiveToolboxItem = (activeToolboxItem: string | null) => {
    this.activeToolboxItem = activeToolboxItem;
  };

  setControlsVisible = (visible: boolean) => {
    this.controlsVisible = visible;
  };
}

export const layoutStore = new LayoutStore();
