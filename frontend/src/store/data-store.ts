import { makeAutoObservable } from "mobx";
import { IFont, ICompactFont } from "../interfaces/editor";

export class DataStore {
  fonts: IFont[] = [];
  compactFonts: ICompactFont[] = [];

  constructor() {
    makeAutoObservable(this);
  }

  setFonts = (fonts: IFont[]) => {
    this.fonts = fonts;
  };

  setCompactFonts = (compactFonts: ICompactFont[]) => {
    this.compactFonts = compactFonts;
  };
}

export const dataStore = new DataStore();
