# 在线视频编辑器国际化集成指南

本指南介绍如何为在线视频编辑器的组件添加国际化(i18n)支持。

## 架构概述

国际化系统基于 React Context API 实现，主要组件有：

1. `LanguageContext.tsx` - 定义了语言上下文和 `useLanguage` hook
2. `locales/zh.ts` - 中文翻译文件
3. `locales/en.ts` - 英文翻译文件

## 集成步骤

要为组件添加国际化支持，请按照以下步骤操作：

### 1. 导入 useLanguage hook

```tsx
import { useLanguage } from "../../i18n/LanguageContext";
```

### 2. 在组件中使用 hook

```tsx
const MyComponent = () => {
  const { t, currentLanguage, setLanguage } = useLanguage();

  // t - 翻译函数
  // currentLanguage - 当前语言代码 ('zh' 或 'en')
  // setLanguage - 切换语言的函数

  return (
    <div>
      <h1>{t("my_title")}</h1>
      <p>{t("my_description")}</p>
    </div>
  );
};
```

### 3. 添加翻译键

在对应的语言文件中添加翻译键值对：

**zh.ts**:

```ts
{
  // ...其他翻译
  my_title: "我的标题",
  my_description: "这是描述文本"
}
```

**en.ts**:

```ts
{
  // ...其他翻译
  my_title: "My Title",
  my_description: "This is a description text"
}
```

### 4. 处理包含参数的文本

对于包含动态参数的文本，可以使用替换模式：

```tsx
// 翻译键定义：
// "greeting": "你好，{0}！今天是{1}"

// 使用方式：
t("greeting").replace("{0}", username).replace("{1}", today);
```

## 最佳实践

1. **组织翻译键** - 在翻译文件中按功能或组件对翻译键进行分组，使用注释明确分组
2. **保持一致性** - 为相似功能使用一致的命名约定
3. **使用有意义的键名** - 让键名反映文本内容，而不只是位置
4. **避免硬编码字符串** - 所有显示给用户的文本都应使用 `t()` 函数
5. **提取公共片段** - 对于在多处使用的相同文本，使用同一个翻译键

## 组件注意事项

### 按钮和标签

确保所有按钮文本和标签都被翻译：

```tsx
<Button onClick={handleClick}>{t("save")}</Button>
<Typography>{t("no_items_found")}</Typography>
```

### 错误信息

所有错误信息都应该被翻译：

```tsx
setError(t("loading_failed"));
```

### 输入提示

表单占位符文本也需要翻译：

```tsx
<TextField placeholder={t("search_placeholder")} />
```

### 动态内容

包含动态内容的翻译：

```tsx
// 翻译键："items_count": "显示 {0} 个项目，共 {1} 个"
const message = t("items_count")
  .replace("{0}", displayed.toString())
  .replace("{1}", total.toString());
```

## 测试国际化

添加国际化后，请通过切换语言进行测试，确保：

1. 所有文本都正确翻译
2. 布局在不同语言下正常显示（尤其是当翻译文本长度差异较大时）
3. 动态替换内容正确显示

## 常见问题

1. **丢失翻译键** - 如果看到原始键名显示在界面上，检查翻译文件中是否存在该键
2. **布局问题** - 如果文本过长导致布局问题，考虑调整 UI 组件或缩短翻译文本
3. **性能问题** - 避免在渲染循环中过度调用 `t()` 函数，可以在组件顶部预先翻译

## 添加新语言

要添加新语言支持：

1. 在 `locales` 目录下创建新的语言文件 (如 `jp.ts`)
2. 在 `LanguageContext.tsx` 中导入并添加到 `translations` 对象
3. 更新语言选择器组件以包含新语言选项
