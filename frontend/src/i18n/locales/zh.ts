// 中文翻译
const zhTranslations = {
  // 通用
  theme: "主题",
  language: "语言",
  light_mode: "浅色模式",
  dark_mode: "深色模式",
  settings: "设置",
  chinese: "中文",
  english: "英文",
  text: "文本",

  // 导航栏
  undo: "撤销",
  redo: "重做",
  export: "导出",
  shortcuts: "快捷键",
  keyboard_shortcuts: "键盘快捷键",
  video_editor_logo: "视频编辑器Logo",

  // 编辑模式
  move_mode: "移动模式",
  hand_tool: "手型工具",
  edit_mode: "编辑模式",

  // 缩放控制
  zoom_in: "放大",
  zoom_out: "缩小",
  fit_to_screen: "适应屏幕",

  // 项目
  untitled_project: "未命名项目",
  save_project: "保存项目",
  project_name: "项目名称",
  untitled_video: "未命名视频",

  // 导出
  export_video: "导出视频",
  export_format: "导出格式",
  export_quality: "导出质量",
  export_settings: "导出设置",
  start_export: "开始导出",
  preparing_export: "准备导出...",
  cancel: "取消",
  download_video: "下载视频",
  retry: "重试",
  restart: "重新开始",

  // 视频处理状态
  generating_video: "生成视频中...",
  generation_complete: "生成完成",
  generation_failed: "生成失败",
  cancelled: "已取消",

  // 视频处理阶段
  initializing: "初始化",
  detecting_audio: "检测音频",
  detecting_audio_elements: "分析音频元素",
  processing_elements: "处理元素",
  processing_audio_elements: "处理音频",
  processing_visual_elements: "处理视觉元素",
  processing_captions: "处理字幕",
  building_command: "构建命令",
  generating_command: "生成命令",
  rendering: "渲染中",
  finalizing: "完成中",
  completed: "已完成",
  failed: "失败",

  // 加载
  loading: "加载中...",
  processing: "处理中...",

  // 快捷键分类
  category_edit: "编辑",
  category_tools: "工具",
  category_view: "视图",
  category_project: "项目",
  category_help: "帮助",
  category_playback: "播放",

  // 快捷键描述
  shortcut_undo: "撤销",
  shortcut_redo: "重做",
  shortcut_move_mode: "移动模式",
  shortcut_hand_tool: "手型工具",
  shortcut_zoom_in: "放大",
  shortcut_zoom_out: "缩小",
  shortcut_fit_to_screen: "适应大小",
  shortcut_save_project: "保存项目",
  shortcut_export_video: "导出视频",
  shortcut_show_shortcuts: "显示快捷键",
  shortcut_delete_selected: "删除选中元素",
  shortcut_delete_element: "删除元素",
  shortcut_duplicate_selected: "复制选中元素",
  shortcut_play_pause: "播放/暂停",
  shortcut_seek_forward: "快进",
  shortcut_seek_backward: "快退",
  shortcut_fit_timeline: "适应时间线",

  // 视频格式
  format_mp4: "MP4 视频",
  format_gif: "GIF 动图",
  format_mov: "MOV 视频",
  format_mp3: "MP3 音频",

  // 导出质量
  quality_high: "高质量",
  quality_medium: "中等质量",
  quality_low: "低质量",
  quality_high_desc: "最佳质量，文件较大",
  quality_medium_desc: "质量与文件大小平衡",
  quality_low_desc: "文件较小，质量较低",

  // 视频尺寸比例
  ratio: "比例",
  ratio_selection_description: "选择适合您内容的视频比例和平台",
  original: "原始",
  aspect_ratio_16_9: "16:9",
  aspect_ratio_4_3: "4:3",
  aspect_ratio_2_1: "2:1",
  aspect_ratio_9_16: "9:16",
  aspect_ratio_1_1: "1:1",
  aspect_ratio_3_4: "3:4",

  // 视频背景
  background: "背景",
  recents: "最近使用",
  recommended: "推荐",
  transparent: "透明",
  no_background: "无背景",

  // 基础设置
  no_element_selected: "未选择元素",
  alignment: "对齐",
  position: "位置",
  lock: "锁定",
  unlock: "解锁",
  opacity: "不透明度",
  clone: "复制",
  delete: "删除",
  fullscreen: "全屏",
  flip_horizontal: "水平翻转",
  flip_vertical: "垂直翻转",
  align_left: "左对齐",
  align_center: "居中对齐",
  align_right: "右对齐",
  justify: "两端对齐",
  align_top: "顶部对齐",
  align_middle: "垂直居中",
  align_bottom: "底部对齐",
  position_x: "位置 X",
  position_y: "位置 Y",
  width: "宽度",
  height: "高度",
  rotation: "旋转",

  // 画布设置
  canvas_settings: "画布设置",
  basic: "基础",
  advanced: "高级",
  random_color: "随机颜色",
  gradient: "渐变",
  duration: "时长",
  seconds: "秒",
  import: "导入",

  // 文字设置
  font_setting: "文字设置",
  font_family: "字体",
  font_size: "字体大小",
  font_color: "字体颜色",
  text_align: "文本对齐",
  styles: "样式",
  char_spacing: "字间距",
  line_height: "行高",
  bold: "粗体",
  italic: "斜体",
  underline: "下划线",
  strikethrough: "删除线",
  stroke_width: "描边宽度",
  stroke_color: "描边颜色",
  shadow_color: "阴影颜色",
  shadow_blur: "阴影模糊",
  shadow_offset_x: "阴影X偏移",
  shadow_offset_y: "阴影Y偏移",
  use_gradient: "使用渐变",
  gradient_start: "渐变起始",
  gradient_end: "渐变结束",
  text_style: "文本样式",
  subtitle: "字幕",
  position_settings_hint: "位置设置（可在画布中直接拖拽字幕调整位置）",
  horizontal_position: "水平位置",
  vertical_position: "垂直位置",
  position_hint: "提示：正值向右/向下移动，负值向左/向上移动",
  debug_position: "调试字幕位置",
  debug_position_hint:
    "点击此按钮在控制台查看字幕位置信息，验证前端和后端位置是否一致",

  // 图像设置
  image_settings: "图像",
  crop_image: "裁剪图像",
  apply_crop: "应用裁剪",
  cancel_crop: "取消裁剪",
  show_settings: "显示设置",
  hide_settings: "隐藏设置",
  border_width: "边框宽度",

  // GIF设置
  gif_settings: "GIF设置",
  select_gif_element: "请选择一个GIF元素",
  crop_gif: "裁剪GIF",
  gif_info: "GIF信息",
  animated: "动画",
  frame_count: "帧数",
  frame_rate: "帧率",
  yes: "是",
  no: "否",
  enable_border: "启用边框",
  border_color: "边框颜色",
  border_style: "边框样式",
  border_radius: "圆角半径",
  solid: "实线",
  dashed: "虚线",
  dotted: "点线",
  effects: "效果",
  brightness: "亮度",
  contrast: "对比度",
  saturation: "饱和度",
  hue: "色相",
  blur: "模糊",
  reset_filters: "重置滤镜",

  // 视频设置
  video_settings: "视频设置",
  crop_video: "裁剪视频",
  playback_speed: "播放速度",
  volume: "音量",
  mute: "静音",
  video: "视频",

  // 特效类型
  none: "无",
  black_and_white: "黑白",
  saturate: "饱和",
  sepia: "复古",
  invert: "反转",

  // 形状设置
  shape_properties: "图形属性",
  shape_type: "图形类型",

  // 图形相关
  shapes: "图形",

  // 图形分类
  shape_category_all: "全部",
  shape_category_basic: "基础形状",
  shape_category_polygons: "多边形",
  shape_category_arrows: "箭头",
  shape_category_stars: "星形",
  shape_category_symbols: "符号",
  shape_category_special: "特殊形状",

  // 图形名称
  shape_rect: "矩形",
  shape_rounded_rect: "正方形",
  shape_circle: "圆形",
  shape_ellipse: "椭圆",
  shape_triangle: "三角形",
  shape_line: "直线",
  shape_diamond: "菱形",
  shape_pentagon: "五边形",
  shape_hexagon: "六边形",
  shape_octagon: "八边形",
  shape_right_arrow: "右箭头",
  shape_up_arrow: "上箭头",
  shape_down_arrow: "下箭头",
  shape_cross: "叉号",
  shape_star: "星形",
  shape_four_point_star: "四角星",
  shape_six_point_star: "六角星",
  shape_eight_point_star: "八角星",
  shape_sun_burst: "太阳形",
  shape_semicircle: "半圆",
  shape_quarter_circle: "四分之一圆",
  shape_ring: "环形",
  shape_half_ring: "半环",
  shape_plus: "加号",
  shape_arch: "拱形",
  shape_parallelogram: "平行四边形",
  rectangle: "矩形",
  rounded_rectangle: "圆角矩形",
  circle: "圆形",
  ellipse: "椭圆",
  triangle: "三角形",
  line: "直线",
  pentagon: "五边形",
  hexagon: "六边形",
  octagon: "八边形",
  parallelogram: "平行四边形",
  arch: "拱形",
  fill_color: "填充颜色",
  background_color: "背景颜色",

  // 滤镜设置
  filters: "滤镜",
  border_settings: "边框设置",
  color: "颜色",
  style: "样式",
  radius: "半径",
  effect_type: "效果类型",
  presets: "预设",
  default: "默认",
  warm: "暖色",
  cool: "冷色",
  vintage: "复古",
  sharp: "锐化",
  soft: "柔和",
  custom: "自定义",
  adjust: "调整",

  // 动画设置
  animations: "动画",
  animation_in: "入场",
  animation_out: "出场",
  fade: "淡入淡出",
  slide_down: "向下滑动",
  slide_up: "向上滑动",
  slide_left: "向左滑动",
  slide_right: "向右滑动",
  wipe_down: "向下擦除",
  wipe_up: "向上擦除",
  wipe_left: "向左擦除",
  wipe_right: "向右擦除",
  breathe: "呼吸效果",
  direction: "方向",
  left: "左",
  right: "右",
  top: "上",
  bottom: "下",
  use_mask: "使用遮罩",

  // 菜单项
  uploads: "上传",
  layers: "图层",
  Video: "视频",
  Audio: "音频",
  Image: "图片",
  Gif: "GIF",
  Text: "文本",
  Caption: "字幕",
  Shape: "形状",

  // 元素面板
  move_up: "上移",
  move_down: "下移",
  move_to_top: "移至顶层",
  move_to_bottom: "移至底层",

  // 上传
  your_media: "你的媒体",
  project: "项目",
  workspace: "工作区",
  upload_media: "上传媒体",
  upload_media_description: "上传视频、图片或音频文件",
  upload_to_workspace: "上传到工作区",
  upload_workspace_description: "拖放文件到这里或点击浏览",
  workspace_assets_available: "工作区资源将在所有项目中可用",

  // 文本资源
  text_title: "标题",
  text_subtitle: "副标题",
  text_body: "正文",
  text_caption: "标注",

  // 文字样式类别
  text_category_title: "标题样式",
  text_category_subtitle: "副标题样式",
  text_category_body: "正文样式",
  text_category_annotation: "标注样式",
  text_category_social: "社交媒体",
  text_category_creative: "创意样式",

  // 文字样式描述
  text_desc_main_title: "大标题",
  text_desc_subtitle: "副标题",
  text_desc_body: "正文",
  text_desc_caption: "说明文字",
  text_desc_creative_title: "漫画风格",
  text_desc_elegant_title: "手写风格",
  text_desc_tech: "等宽字体",
  text_desc_classic_serif: "经典衬线",

  // 文字界面
  text_search_placeholder: "搜索文字样式...",
  text_no_results: "未找到匹配的文字样式",
  text_try_different_keywords: "尝试使用不同的关键词搜索",
  text_category_effects: "特效样式",

  // 图片库
  image_library: "图片库",
  search_images: "搜索图片...",
  no_images_found: "未找到图片，请尝试其他搜索词。",
  loading_images_failed: "从{0}加载图片失败，请检查网络连接或稍后再试",
  no_images_found_try_another: "未找到任何图片，请尝试其他搜索词或标签",
  displayed_results: "已显示 {0} / {1} 条结果",
  unknown: "未知",

  // GIF库
  gif_library: "GIF库",
  search_gifs: "搜索GIF...",
  no_gifs_found: "未找到GIF，请尝试其他搜索词。",
  load_gifs_error: "加载GIF失败，请检查网络连接或稍后再试",
  add_gif_error: "添加GIF失败，请重试",
  adding: "添加中...",
  add_to_canvas: "添加到画布",
  trending: "热门",
  search: "搜索",
  random: "随机",
  categories: "分类",
  load_more_random: "加载更多随机GIF",
  no_more_gifs: "没有更多GIF了",

  // 视频库
  // 视频库相关
  video_library: "视频库",
  search_videos: "搜索视频...",
  no_videos_found: "没有找到视频。请尝试不同的搜索词。",
  loading_videos_failed: "从{0}加载视频失败，请检查网络连接或稍后再试",
  no_videos_found_try_another: "未找到视频，请尝试其他搜索词或标签",
  displayed_all_results: "已显示全部 {0} 条结果",
  video_loading_timeout: "视频加载超时",
  video_loading_failed: "加载视频失败",
  video_preview: "视频预览",
  adding_video: "添加中...",
  video_added_successfully: "视频添加成功！",
  video_add_failed: "视频添加失败，请重试",

  // 视频标签分类
  video_tag_nature: "自然",
  video_tag_people: "人物",
  video_tag_business: "商务",
  video_tag_technology: "科技",
  video_tag_food: "美食",
  video_tag_travel: "旅行",
  video_tag_sports: "运动",
  video_tag_education: "教育",
  video_tag_backgrounds: "背景",
  video_tag_animation: "动画",
  video_tag_fashion: "时尚",
  video_tag_science: "科学",
  video_tag_health: "健康",
  video_tag_music: "音乐",
  video_tag_places: "地点",
  video_tag_animals: "动物",
  video_tag_aerial: "航拍",
  video_tag_time_lapse: "延时摄影",
  video_tag_slow_motion: "慢动作",
  video_tag_3d: "3D",
  video_tag_abstract: "抽象",
  video_tag_urban: "城市",
  video_tag_vintage: "复古",
  video_tag_cinematic: "电影感",

  // 视频源
  video_source_pixabay: "Pixabay",
  video_source_pexels: "Pexels",

  // 视频状态
  video_loading: "加载中",
  video_error: "加载错误",
  video_ready: "就绪",

  // 时长格式
  minutes: "分钟",
  minute: "分钟",
  seconds_short: "秒",

  // 音频库
  audio_library: "音频库",
  local_audio: "本地音频",
  search_placeholder: "搜索...",
  no_local_audio: "本地音频库为空。",
  no_music_found: "未找到音乐。请尝试搜索或选择不同标签。",
  audio_loading_failed: "音频加载失败，请尝试其他音频",
  all_results_displayed: "已显示全部 {0} 条结果",
  loading_popular_failed: "未能加载热门音乐，请稍后再试。",
  loading_jamendo_failed: "加载Jamendo音乐失败，请检查网络连接或API密钥。",
  no_tag_music_found: "当前选定标签下没有找到音乐。",
  tag_loading_failed: "按标签加载音乐失败，请稍后再试。",
  search_failed: "搜索Jamendo音乐失败，请稍后再试。",
  no_search_result: "没有找到与搜索词匹配的音乐。",
  audio_playback_error: "音频播放失败，请检查音频文件或网络连接。",

  // 字幕
  captions: "字幕",
  add_caption: "添加字幕",
  clear_all_captions: "清除所有字幕",
  clear_confirm_title: "清除所有字幕",
  clear_confirm_message: "确定要删除所有字幕吗？此操作无法撤销。",
  clear_all: "清除全部",
  no_text: "无文本",
  upload_srt: "上传SRT文件",
  export_captions: "导出字幕",
  insert_caption: "插入字幕",
  merge_captions: "合并字幕",
  play_segment: "播放此片段",
  delete_caption: "删除字幕",
  format_should_be: "格式应为 HH:MM:SS",
  invalid_time: "时间值无效",
  start_time_less: "开始时间必须小于结束时间",
  end_time_greater: "结束时间必须大于开始时间",
  time_overlaps: "时间与相邻字幕重叠",
  suggested_time: "建议使用 {0} 作为{1}时间",
  start_time: "开始",
  end_time: "结束",

  // 时间线组件
  timeline_edit: "编辑",
  timeline_copy: "复制",
  timeline_cut: "剪切",
  timeline_delete: "删除",
  timeline_preview: "预览",
  timeline_adjust_style: "调整样式",
  timeline_auto_translate: "自动翻译",
  timeline_start: "开始",
  timeline_end: "结束",

  // SeekPlayer组件
  seekplayer_delete: "删除",
  seekplayer_split_element: "分割元素",
  seekplayer_seek_backward: "向后跳转",
  seekplayer_seek_forward: "向前跳转",
  seekplayer_play: "播放",
  seekplayer_pause: "暂停",
  seekplayer_play_pause: "播放/暂停",
  seekplayer_zoom_in: "放大",
  seekplayer_zoom_out: "缩小",
  seekplayer_fit_view: "适应视图",

  // AI助手
  ai_assistant: "AI助手",
  ai_welcome_message: "你好！我是你的AI助手，有什么可以帮助你的吗？",
  ai_sample_response: "感谢你的提问！我会尽力帮助你解决视频编辑相关的问题。",
  ai_quick_actions: "建议操作",
  ai_quick_action_help: "如何使用视频编辑器？",
  ai_quick_action_suggestions: "给我一些设计建议",
  ai_quick_action_export: "如何导出视频？",
  ai_typing: "AI正在处理请求......",
  ai_input_placeholder: "在这里输入你的问题...",

  // 平台适用性
  platforms: "适用平台",
  format_description: "格式描述",
  platform_youtube: "YouTube",
  platform_tiktok: "TikTok",
  platform_instagram: "Instagram",
  platform_facebook: "Facebook",
  platform_twitter: "Twitter",
  platform_linkedin: "LinkedIn",
  platform_snapchat: "Snapchat",
  platform_pinterest: "Pinterest",
  platform_wechat: "微信",
  platform_weibo: "微博",
  platform_douyin: "抖音",
  platform_xiaohongshu: "小红书",

  // 格式描述
  format_short_video: "适合短视频制作",
  format_story: "适合故事类内容",
  format_post: "适合社交媒体发布",
  format_ad: "适合广告投放",
  format_presentation: "适合演示文稿",
  format_tutorial: "适合教程制作",
  format_intro: "适合开场介绍",
  format_outro: "适合结尾片段",
  format_logo_animation: "适合Logo动画",
  format_product_showcase: "适合产品展示",
  format_business_intro: "适合企业介绍",
  format_course_content: "适合课程内容",
};

export default zhTranslations;
