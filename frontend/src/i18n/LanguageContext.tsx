import React, {
  createContext,
  useState,
  useMemo,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import translations from "./locales";

// 存储语言偏好的key
const LANGUAGE_STORAGE_KEY = "language-preference";

// 支持的语言
export type Language = "zh" | "en";

// 语言上下文类型
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

// 创建上下文
const LanguageContext = createContext<LanguageContextType>({
  language: "zh",
  setLanguage: () => {},
  t: (key) => key,
});

// 语言上下文提供者
export const LanguageProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // 从localStorage读取语言设置，默认为中文
  const [language, setLanguageState] = useState<Language>(() => {
    const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY);
    return savedLanguage === "en" ? "en" : "zh";
  });

  // 当语言变化时保存到localStorage
  useEffect(() => {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, language);
  }, [language]);

  // 设置语言函数
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
  };

  // 翻译函数
  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  // 提供上下文值
  const contextValue = useMemo(
    () => ({
      language,
      setLanguage,
      t,
    }),
    [language]
  );

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

// 使用语言上下文的Hook
export const useLanguage = () => useContext(LanguageContext);
