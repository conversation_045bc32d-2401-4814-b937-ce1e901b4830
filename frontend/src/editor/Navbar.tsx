import { observer } from "mobx-react-lite";
import React, { useCallback, useState, useEffect } from "react";

import { Box, IconButton, Divider, alpha, Tooltip } from "@mui/material";

import KeyboardIcon from "@mui/icons-material/Keyboard";
import HomeIcon from "@mui/icons-material/Home";
import { useNavigate } from "react-router-dom";
import { StoreContext } from "../store";
import { DownloadPopover, ResizeVideo, BackgroundSelector } from "./components";
import {
  TitleEditor,
  EditModeToggle,
  ZoomControls,
  HistoryButtons,
  ShortcutsDialog,
  AiAssistantPopover,
  EditMode,
  ShortcutKey,
} from "./components/navbar";
import { ThemeToggle } from "../theme/ThemeToggle";
import { useShortcuts } from "../hooks/useShortcuts";
import { useLanguage } from "../i18n/LanguageContext";

// Common styles
const COMMON_STYLES = {
  navBarButton: {
    color: "text.secondary",
    "&:hover": {
      backgroundColor: "action.hover",
    },
  },
  buttonGroup: {
    display: "flex",
    alignItems: "center",
    gap: 0.5,
    height: "100%",
    px: 1,
  },
  divider: {
    height: 24,
    mx: 1,
  },
};

export const Navbar = observer(() => {
  const store = React.useContext(StoreContext);
  const navigate = useNavigate();
  const { t } = useLanguage();
  const [shortcutsDialogOpen, setShortcutsDialogOpen] = useState(false);

  // 使用统一的快捷键管理器
  const { shortcutKeys } = useShortcuts(store);

  // 获取撤销/重做操作的类型，用于提示
  const undoActionType = store.getUndoActionType?.();
  const redoActionType = store.getRedoActionType?.();

  const handleUndo = useCallback(() => {
    store.undo?.();
  }, [store]);

  const handleRedo = useCallback(() => {
    store.redo?.();
  }, [store]);

  // 处理画布缩放
  const handleZoomIn = useCallback(() => {
    const newValue = store.zoomIn();
    // 触发CanvasContainer中的MapInteractionCSS组件更新
    window.dispatchEvent(
      new CustomEvent("canvas-zoom-change", { detail: newValue })
    );
  }, [store]);

  const handleZoomOut = useCallback(() => {
    const newValue = store.zoomOut();
    window.dispatchEvent(
      new CustomEvent("canvas-zoom-change", { detail: newValue })
    );
  }, [store]);

  const handleResetZoom = useCallback(() => {
    const newValue = store.resetZoom();
    window.dispatchEvent(
      new CustomEvent("canvas-zoom-change", { detail: newValue })
    );
  }, [store]);

  // 处理编辑模式切换
  const handleEditModeChange = useCallback(
    (newMode: EditMode) => {
      store.setEditMode(newMode);
    },
    [store]
  );

  // 处理标题保存
  const handleSaveTitle = useCallback(
    (title: string) => {
      store.setProjectName?.(title);
    },
    [store]
  );

  // 返回仪表板
  const handleBackToDashboard = useCallback(() => {
    // 保存当前项目状态到本地存储
    store.projectManager.saveToLocalStorage();
    // 保存到最近项目列表
    store.projectManager.saveToRecentProjects();
    // 导航到仪表板
    navigate("/");
  }, [store, navigate]);

  // 处理快捷键对话框
  const handleOpenShortcutsDialog = useCallback(() => {
    setShortcutsDialogOpen(true);
  }, []);

  const handleCloseShortcutsDialog = useCallback(() => {
    setShortcutsDialogOpen(false);
  }, []);

  // 监听快捷键对话框显示事件
  useEffect(() => {
    const handleShowShortcuts = () => {
      handleOpenShortcutsDialog();
    };

    window.addEventListener("show-shortcuts", handleShowShortcuts);
    return () => {
      window.removeEventListener("show-shortcuts", handleShowShortcuts);
    };
  }, [handleOpenShortcutsDialog]);

  return (
    <>
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "62px",
          zIndex: 205,
          display: "flex",
          justifyContent: "space-between",
          maxWidth: "98%",
          margin: "0 auto", // Center the navbar
          borderRadius: "0 0 8px 8px", // 圆角只在底部
          backdropFilter: "blur(8px)", // 毛玻璃效果
          backgroundColor: (theme) =>
            theme.palette.mode === "dark"
              ? alpha(theme.palette.background.paper, 0.85)
              : alpha(theme.palette.background.paper, 0.95),
        }}
      >
        {/* 左侧区域：Logo、项目标题、撤销/重做、导出 */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            height: "100%",
          }}
        >
          {/* Logo */}
          <Box
            sx={{
              height: 48,
              width: 48,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              borderRadius: 1,
              ml: 1,
            }}
          >
            <img
              src="/assets/logo.png"
              alt={t("video_editor_logo")}
              style={{ height: 32, width: 32 }}
            />
          </Box>

          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 返回仪表板按钮 */}
          <Box sx={COMMON_STYLES.buttonGroup}>
            <Tooltip title="返回仪表板" arrow>
              <IconButton
                size="small"
                sx={COMMON_STYLES.navBarButton}
                onClick={handleBackToDashboard}
              >
                <HomeIcon sx={{ fontSize: 20 }} />
              </IconButton>
            </Tooltip>
          </Box>

          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 项目标题 */}
          <TitleEditor
            initialTitle={store.projectName || ""}
            onSaveTitle={handleSaveTitle}
          />

          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 撤销/重做按钮组 */}
          <HistoryButtons
            onUndo={handleUndo}
            onRedo={handleRedo}
            undoActionType={undoActionType}
            redoActionType={redoActionType}
            buttonStyle={COMMON_STYLES.navBarButton}
          />

          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 导出按钮 */}
          <Box sx={COMMON_STYLES.buttonGroup}>
            <DownloadPopover />
          </Box>
        </Box>

        {/* 中间区域：编辑模式切换、缩放控制 */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            flex: 1,
          }}
        >
          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 编辑模式切换按钮组 */}
          <EditModeToggle
            currentMode={store.editMode as EditMode}
            onModeChange={handleEditModeChange}
          />

          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          {/* 缩放控制按钮组 */}
          <ZoomControls
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
            onResetZoom={handleResetZoom}
            buttonStyle={COMMON_STYLES.navBarButton}
          />

          {/* 视频尺寸和背景选择 */}
          <Divider orientation="vertical" sx={COMMON_STYLES.divider} />

          <Box sx={COMMON_STYLES.buttonGroup}>
            <ResizeVideo />
            <BackgroundSelector />
          </Box>
        </Box>

        {/* 右侧区域：主题切换、快捷键、AI助手 */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            height: "100%",
          }}
        >
          {/* 主题切换、快捷键、AI助手 */}
          <Box sx={{ ...COMMON_STYLES.buttonGroup, mr: 0 }}>
            <AiAssistantPopover />
            <ThemeToggle size="small" />
            <Tooltip title={`${t("shortcuts")} (Ctrl+K)`} arrow>
              <IconButton
                size="small"
                sx={COMMON_STYLES.navBarButton}
                onClick={handleOpenShortcutsDialog}
              >
                <KeyboardIcon sx={{ fontSize: 20 }} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      {/* 快捷键对话框 */}
      <ShortcutsDialog
        open={shortcutsDialogOpen}
        onClose={handleCloseShortcutsDialog}
        shortcuts={shortcutKeys}
      />
    </>
  );
});
