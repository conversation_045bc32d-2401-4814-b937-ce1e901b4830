import React, { useState } from "react";
import {
  Button,
  Box,
  Popover,
  Typography,
  Slider,
  IconButton,
} from "@mui/material";
import { ColorPicker } from "./GradientColorPicker";
import SwapHorizIcon from "@mui/icons-material/SwapHoriz";
interface GradientPickerProps {
  colors: string[];
  onChange: (colors: string[]) => void;
}

export const GradientPicker: React.FC<GradientPickerProps> = ({
  colors,
  onChange,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [localColors, setLocalColors] = useState<string[]>(colors);
  const [angle, setAngle] = useState<number>(0);
  const [previewColors, setPreviewColors] = useState<string[]>(colors);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    onChange(localColors);
  };

  const handleColorChange = (index: number, color: string) => {
    const newColors = [...localColors];
    newColors[index] = color;
    setLocalColors(newColors);
  };

  const handleAngleChange = (event: Event, newValue: number | number[]) => {
    setAngle(newValue as number);
  };

  const handleSwapColors = () => {
    setLocalColors([localColors[1], localColors[0]]);
    setPreviewColors([localColors[1], localColors[0]]);
  };

  const handleApply = () => {
    onChange(localColors);
    setPreviewColors(localColors);
    setAnchorEl(null);
  };

  const handleCancel = () => {
    setLocalColors(previewColors);
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <Box
        onClick={handleClick}
        sx={{
          width: 25,
          height: 25,
          borderRadius: "50%",
          cursor: "pointer",
          transition: "all 0.3s ease",
          "&:hover": {
            boxShadow: "0 0 10px rgba(0,0,0,0.2)",
            transform: "scale(1.05)",
          },
          background: `linear-gradient(${angle}deg, ${localColors[0]}, ${localColors[1]})`,
          border: "2px solid #e0e0e0",

          boxShadow: "0 0 5px rgba(0,0,0,0.1)",
        }}
      />
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <Box sx={{ p: 1, m: 1, width: 200 }}>
          <Typography variant="subtitle1" gutterBottom>
            Gradient Editor
          </Typography>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <ColorPicker
              color={localColors[0]}
              onChange={(color) => handleColorChange(0, color)}
            />
            <IconButton onClick={handleSwapColors} size="small">
              <SwapHorizIcon />
            </IconButton>
            <ColorPicker
              color={localColors[1]}
              onChange={(color) => handleColorChange(1, color)}
            />
          </Box>
          <Typography variant="subtitle2" gutterBottom>
            Angle: {angle}°
          </Typography>
          <Slider
            value={angle}
            onChange={handleAngleChange}
            min={0}
            max={360}
            step={1}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />
          <Box
            sx={{
              width: "100%",
              height: 60,
              mb: 2,
              background: `linear-gradient(${angle}deg, ${localColors[0]}, ${localColors[1]})`,
              borderRadius: 1,
              boxShadow: "inset 0 0 5px rgba(0,0,0,0.1)",
            }}
          />
          <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1 }}>
            <Button variant="outlined" onClick={handleCancel} size="small">
              Cancel
            </Button>
            <Button variant="contained" onClick={handleApply} size="small">
              OK
            </Button>
          </Box>
        </Box>
      </Popover>
    </>
  );
};
