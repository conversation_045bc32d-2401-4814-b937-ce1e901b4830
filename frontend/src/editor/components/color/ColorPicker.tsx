// DEPRECATED: This component has been replaced by UnifiedColorPicker
// Please use UnifiedColorPicker from '@/components/common' instead
// This file is kept for backward compatibility

import { UnifiedColorPicker } from "../../../components/common";

interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
}

const ColorPicker: React.FC<ColorPickerProps> = ({ color, onChange }) => {
  return (
    <UnifiedColorPicker
      color={color}
      onChange={(newColor) => onChange(newColor as string)}
      mode="simple"
      size="small"
      pickerType="sketch"
    />
  );
};

export default ColorPicker;
