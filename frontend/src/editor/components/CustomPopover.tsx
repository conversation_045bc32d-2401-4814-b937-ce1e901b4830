import React, { ReactNode, useState } from "react";
import { Box, Popover, Typography, PopoverProps } from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";

interface CustomPopoverProps {
  buttonContent?: ReactNode;
  buttonIcon?: ReactNode;
  title?: string;
  children: ReactNode;
  minWidth?: number | string;
  maxWidth?: number | string;
  customTrigger?: ReactNode;
  onOpen?: () => void;
  onClose?: () => void;
  buttonProps?: {
    sx?: any;
    variant?: "text" | "outlined" | "contained";
  };
  popoverProps?: Partial<PopoverProps>;
}

export const CustomPopover: React.FC<CustomPopoverProps> = ({
  buttonContent,
  buttonIcon,
  title,
  children,
  minWidth = 100,
  maxWidth,
  customTrigger,
  onOpen,
  onClose,
  buttonProps = {},
  popoverProps = {},
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const open = Boolean(anchorEl);
  const id = open ? "custom-popover" : undefined;

  const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    onOpen?.();
  };

  const handleClose = () => {
    setAnchorEl(null);
    onClose?.();
  };

  // 构建按钮的默认样式
  const defaultButtonStyle = {
    display: "flex",
    alignItems: "center",
    gap: 0.5,
    cursor: "pointer",
    borderRadius: 1.5,
    px: 1.5,
    py: 0.75,
    "&:hover": {
      backgroundColor: "action.hover",
    },
    ...(buttonProps.sx || {}),
  };

  return (
    <>
      {customTrigger ? (
        <Box onClick={handleOpen} sx={{ cursor: "pointer" }}>
          {customTrigger}
        </Box>
      ) : (
        <Box onClick={handleOpen} sx={defaultButtonStyle}>
          {buttonIcon}
          {buttonContent && (
            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                display: "flex",
                alignItems: "center",
              }}
            >
              {buttonContent}
              <KeyboardArrowDownIcon
                sx={{ ml: 0.5, fontSize: 18, opacity: 0.7 }}
              />
            </Typography>
          )}
        </Box>
      )}

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          "& .MuiPaper-root": {
            borderRadius: 2,
            boxShadow: 3,
            minWidth,
            maxWidth,
            overflow: "visible",
            mt: 1,
          },
        }}
        {...popoverProps}
      >
        {title && (
          <Box
            sx={{
              padding: 2,
              paddingBottom: 1.5,
              borderBottom: "1px solid",
              borderColor: "divider",
            }}
          >
            <Typography variant="subtitle2" fontWeight={600}>
              {title}
            </Typography>
          </Box>
        )}
        <Box>{children}</Box>
      </Popover>
    </>
  );
};
