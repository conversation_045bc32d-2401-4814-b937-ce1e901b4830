import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  useCallback,
  useMemo,
} from "react";
import {
  Box,
  TextField,
  IconButton,
  Typography,
  Avatar,
  Divider,
  Paper,
  Chip,
  CircularProgress,
} from "@mui/material";
import {
  Send as SendIcon,
  SmartToy as AiIcon,
  Person as PersonIcon,
  Refresh as RefreshIcon,
  DeleteOutline as DeleteIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Stop as StopIcon,
} from "@mui/icons-material";
import { useLanguage } from "../../../i18n/LanguageContext";
import { CustomPopover } from "../CustomPopover";
import { StoreContext } from "../../../store";
import AutoAwesomeOutlinedIcon from "@mui/icons-material/AutoAwesomeOutlined";

// 常量定义
const CONSTANTS = {
  API_URL: "http://localhost:8080/api/ai/chat",
  SCROLL_AMOUNT: 120,
  MAX_WIDTH: 400,
  MIN_WIDTH: 320,
  MAX_HEIGHT: 520,
  MARGIN: 40,
  AVATAR_SIZE: 28,
  BUTTON_SIZE: 40,
  ANIMATION_DURATION: "0.2s",
} as const;

const STYLES = {
  gradients: {
    primary: (theme: any) =>
      `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    user: (theme: any) =>
      `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.error.main} 100%)`,
    ai: (theme: any) =>
      `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.info.main} 100%)`,
    stop: (theme: any) =>
      `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.warning.main} 100%)`,
  },
  shadows: {
    hover: (theme: any) => `0 4px 12px ${theme.palette.primary.main}30`,
    paper: (theme: any) => `0 2px 12px ${theme.palette.grey[500]}20`,
    avatar: (theme: any) => `0 2px 8px ${theme.palette.grey[500]}30`,
  },
} as const;

// 类型定义
interface Message {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface WindowSize {
  width: number;
  height: number;
}

interface ScrollState {
  canScrollLeft: boolean;
  canScrollRight: boolean;
}

interface AiApiResponse {
  response: string;
  canvasState?: any;
}

interface PopoverDimensions {
  minWidth: number;
  maxWidth: number;
  maxHeight: number;
}

// 自定义Hook：窗口大小监听
const useWindowSize = (): WindowSize => {
  const [windowSize, setWindowSize] = useState<WindowSize>({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return windowSize;
};

// 自定义Hook：滚动状态管理
const useScrollState = (scrollRef: React.RefObject<HTMLDivElement>) => {
  const [scrollState, setScrollState] = useState<ScrollState>({
    canScrollLeft: false,
    canScrollRight: false,
  });

  const checkScrollButtons = useCallback(() => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setScrollState({
        canScrollLeft: scrollLeft > 0,
        canScrollRight: scrollLeft < scrollWidth - clientWidth,
      });
    }
  }, [scrollRef]);

  const scrollActions = useCallback(
    (direction: "left" | "right") => {
      if (scrollRef.current) {
        const newScrollLeft =
          direction === "left"
            ? scrollRef.current.scrollLeft - CONSTANTS.SCROLL_AMOUNT
            : scrollRef.current.scrollLeft + CONSTANTS.SCROLL_AMOUNT;

        scrollRef.current.scrollTo({
          left: newScrollLeft,
          behavior: "smooth",
        });
      }
    },
    [scrollRef]
  );

  return { scrollState, checkScrollButtons, scrollActions };
};

export const AiAssistantPopover: React.FC = () => {
  const { t } = useLanguage();
  const store = useContext(StoreContext);
  const windowSize = useWindowSize();

  // 状态定义
  const [messages, setMessages] = useState<Message[]>(() => [
    {
      id: "1",
      content: t("ai_welcome_message"),
      sender: "ai",
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [lastInput, setLastInput] = useState("");

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const quickActionsRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 自定义hooks
  const { scrollState, checkScrollButtons, scrollActions } =
    useScrollState(quickActionsRef);

  // 快捷操作数据
  const quickActions = useMemo(
    () => [
      t("ai_quick_action_help"),
      t("ai_quick_action_suggestions"),
      t("ai_quick_action_export"),
    ],
    [t]
  );

  // 计算弹窗尺寸
  const popoverDimensions = useMemo((): PopoverDimensions => {
    const { width: windowWidth, height: windowHeight } = windowSize;
    const maxWidth = Math.min(
      CONSTANTS.MAX_WIDTH,
      Math.max(CONSTANTS.MIN_WIDTH, windowWidth - CONSTANTS.MARGIN)
    );
    const minWidth = Math.min(CONSTANTS.MIN_WIDTH, windowWidth - 60);
    const maxHeight = Math.min(CONSTANTS.MAX_HEIGHT, windowHeight - 120);

    return { minWidth, maxWidth, maxHeight };
  }, [windowSize]);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  // API调用函数
  const callAiApi = useCallback(
    async (
      userInput: string,
      canvasState: string,
      abortController: AbortController
    ): Promise<AiApiResponse> => {
      try {
        const response = await fetch(CONSTANTS.API_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            userInput,
            canvasState: JSON.parse(canvasState),
          }),
          signal: abortController.signal,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "AI API调用失败");
        }

        const result = await response.json();
        const responseText = result.response || "抱歉，我无法处理您的请求。";
        let parsedCanvasState = null;

        // 解析canvas state
        if (result.canvasState) {
          parsedCanvasState = result.canvasState;
          console.log("后端直接返回了canvas state:", parsedCanvasState);
        } else {
          try {
            const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch) {
              parsedCanvasState = JSON.parse(jsonMatch[1]);
              console.log(
                "从AI响应文本中解析到canvas state:",
                parsedCanvasState
              );
            } else {
              const parsed = JSON.parse(responseText);
              if (
                parsed &&
                typeof parsed === "object" &&
                (parsed.elements || parsed.width || parsed.height)
              ) {
                parsedCanvasState = parsed;
                console.log("AI响应本身就是canvas state:", parsedCanvasState);
              }
            }
          } catch (parseError) {
            console.log("AI响应不包含canvas state，作为普通文本处理");
          }
        }

        return {
          response: responseText,
          canvasState: parsedCanvasState,
        };
      } catch (error) {
        console.error("AI API调用错误:", error);
        throw error;
      }
    },
    []
  );

  // 处理消息发送
  const handleSendMessage = useCallback(async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      sender: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    const currentInput = inputValue.trim();
    setLastInput(currentInput);
    setInputValue("");
    setIsLoading(true);

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    try {
      const canvasState = store.exportCanvasState();
      if (!canvasState) {
        throw new Error("无法获取画布状态");
      }

      const aiResult = await callAiApi(
        currentInput,
        canvasState,
        abortController
      );

      if (aiResult.canvasState) {
        try {
          const canvasStateJson = JSON.stringify(aiResult.canvasState);
          const importSuccess = store.importCanvasState(canvasStateJson, false);

          const successMessage: Message = {
            id: (Date.now() + 1).toString(),
            content: importSuccess
              ? `${aiResult.response}\n\n✅ 已成功应用画布状态更改`
              : `${aiResult.response}\n\n⚠️ 应用画布状态时出错`,
            sender: "ai",
            timestamp: new Date(),
          };
          setMessages((prev) => [...prev, successMessage]);
        } catch (importError) {
          console.error("应用canvas state失败:", importError);
          const errorMessage: Message = {
            id: (Date.now() + 1).toString(),
            content: `${aiResult.response}\n\n⚠️ 应用画布状态时出错：${
              importError instanceof Error ? importError.message : "未知错误"
            }`,
            sender: "ai",
            timestamp: new Date(),
          };
          setMessages((prev) => [...prev, errorMessage]);
        }
      } else {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: aiResult.response,
          sender: "ai",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error("发送消息失败:", error);

      const isAbortError =
        error instanceof Error && error.name === "AbortError";
      const message: Message = {
        id: (Date.now() + 1).toString(),
        content: isAbortError
          ? "✋ 已停止生成回复"
          : `抱歉，发生了错误：${
              error instanceof Error ? error.message : "未知错误"
            }`,
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, message]);
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [inputValue, isLoading, store, callAiApi]);

  // 停止生成
  const handleStopGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // 键盘事件处理
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();
        handleSendMessage();
      }
    },
    [handleSendMessage]
  );

  // 重试功能
  const handleRetry = useCallback(() => {
    if (lastInput) {
      setInputValue(lastInput);
    }
  }, [lastInput]);

  // 清除历史
  const handleClearHistory = useCallback(() => {
    setMessages([
      {
        id: "1",
        content: t("ai_welcome_message"),
        sender: "ai",
        timestamp: new Date(),
      },
    ]);
    setLastInput("");
  }, [t]);

  // 快捷操作点击
  const handleQuickActionClick = useCallback((action: string) => {
    setInputValue(action);
  }, []);

  // 效果处理
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  useEffect(() => {
    checkScrollButtons();
  }, [quickActions, windowSize, checkScrollButtons]);

  useEffect(() => {
    const quickActionsElement = quickActionsRef.current;
    if (quickActionsElement) {
      quickActionsElement.addEventListener("scroll", checkScrollButtons);
      return () => {
        quickActionsElement.removeEventListener("scroll", checkScrollButtons);
      };
    }
  }, [checkScrollButtons]);

  // 按钮内容
  const buttonContent = useMemo(
    () => (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 1,
          px: 1.5,
          py: 0.75,
          borderRadius: 2,
          background: STYLES.gradients.primary,
          color: "white",
          "&:hover": {
            transform: "translateY(-1px)",
            boxShadow: STYLES.shadows.hover,
          },
          transition: `all ${CONSTANTS.ANIMATION_DURATION} ease-in-out`,
        }}
      >
        <AutoAwesomeOutlinedIcon sx={{ fontSize: 18 }} />
        <Typography variant="body2" sx={{ fontWeight: 600, color: "white" }}>
          AI Agent
        </Typography>
      </Box>
    ),
    []
  );

  const { minWidth, maxWidth, maxHeight } = popoverDimensions;

  return (
    <CustomPopover
      customTrigger={buttonContent}
      title={t("ai_assistant")}
      minWidth={minWidth}
      maxWidth={maxWidth}
      popoverProps={{
        anchorOrigin: {
          vertical: "bottom",
          horizontal: "right",
        },
        transformOrigin: {
          vertical: "top",
          horizontal: "right",
        },
        sx: {
          "& .MuiPaper-root": {
            borderRadius: 3,
            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
            border: "1px solid",
            borderColor: "divider",
            backdropFilter: "blur(8px)",
            background: (theme) => theme.palette.background.paper,
            maxWidth: `${CONSTANTS.MAX_WIDTH}px !important`,
          },
        },
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: maxHeight,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        {/* 快捷操作 */}
        <Box sx={{ p: 2.5, pb: 1.5 }}>
          <Typography
            variant="body2"
            sx={{
              mb: 1.5,
              color: "text.secondary",
              fontWeight: 500,
              fontSize: "0.8rem",
              textTransform: "uppercase",
              letterSpacing: "0.5px",
            }}
          >
            {t("ai_quick_actions")}
          </Typography>
          <Box
            sx={{
              position: "relative",
              display: "flex",
              alignItems: "center",
              "& .MuiIconButton-root": {
                backgroundColor: (theme) => theme.palette.background.paper,
                border: "1px solid",
                borderColor: "divider",
                boxShadow: STYLES.shadows.paper,
                "&:hover": {
                  backgroundColor: (theme) =>
                    theme.palette.mode === "dark"
                      ? theme.palette.grey[700]
                      : theme.palette.grey[100],
                },
              },
            }}
          >
            {/* 左滚动按钮 */}
            {scrollState.canScrollLeft && (
              <IconButton
                onClick={() => scrollActions("left")}
                size="small"
                sx={{
                  position: "absolute",
                  left: -8,
                  zIndex: 2,
                  width: 24,
                  height: 24,
                }}
              >
                <ChevronLeftIcon sx={{ fontSize: 16 }} />
              </IconButton>
            )}

            {/* 滚动容器 */}
            <Box
              ref={quickActionsRef}
              sx={{
                display: "flex",
                gap: 1,
                overflowX: "auto",
                scrollBehavior: "smooth",
                px: scrollState.canScrollLeft ? 2 : 0,
                pr: scrollState.canScrollRight ? 2 : 0,
                "&::-webkit-scrollbar": {
                  display: "none",
                },
                scrollbarWidth: "none",
                msOverflowStyle: "none",
              }}
            >
              {quickActions.map((action, index) => (
                <Chip
                  key={index}
                  label={action}
                  size="small"
                  variant="outlined"
                  onClick={() => handleQuickActionClick(action)}
                  sx={{
                    cursor: "pointer",
                  }}
                />
              ))}
            </Box>

            {/* 右滚动按钮 */}
            {scrollState.canScrollRight && (
              <IconButton
                onClick={() => scrollActions("right")}
                size="small"
                sx={{
                  position: "absolute",
                  right: -8,
                  zIndex: 2,
                  width: 24,
                  height: 24,
                }}
              >
                <ChevronRightIcon sx={{ fontSize: 16 }} />
              </IconButton>
            )}
          </Box>
        </Box>

        <Divider />

        {/* 消息列表 */}
        <Box
          sx={{
            flex: 1,
            overflowY: "auto",
            p: 2.5,
            pt: 1.5,
            display: "flex",
            flexDirection: "column",
            gap: 2,
            "&::-webkit-scrollbar": {
              width: "6px",
            },
            "&::-webkit-scrollbar-track": {
              background: "transparent",
            },
            "&::-webkit-scrollbar-thumb": {
              background: (theme) =>
                theme.palette.mode === "dark"
                  ? "rgba(255,255,255,0.2)"
                  : "rgba(0,0,0,0.2)",
              borderRadius: "3px",
            },
            "&::-webkit-scrollbar-thumb:hover": {
              background: (theme) =>
                theme.palette.mode === "dark"
                  ? "rgba(255,255,255,0.3)"
                  : "rgba(0,0,0,0.3)",
            },
          }}
        >
          {messages.map((message) => (
            <Box
              key={message.id}
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1.5,
                flexDirection:
                  message.sender === "user" ? "row-reverse" : "row",
                animation: "fadeIn 0.3s ease-in-out",
                "@keyframes fadeIn": {
                  from: { opacity: 0, transform: "translateY(10px)" },
                  to: { opacity: 1, transform: "translateY(0)" },
                },
              }}
            >
              <Avatar
                sx={{
                  width: CONSTANTS.AVATAR_SIZE,
                  height: CONSTANTS.AVATAR_SIZE,
                  background:
                    message.sender === "user"
                      ? STYLES.gradients.user
                      : STYLES.gradients.ai,
                  boxShadow: STYLES.shadows.avatar,
                }}
              >
                {message.sender === "user" ? (
                  <PersonIcon sx={{ fontSize: 16, color: "white" }} />
                ) : (
                  <AiIcon sx={{ fontSize: 16, color: "white" }} />
                )}
              </Avatar>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  maxWidth: windowSize.width < 480 ? "85%" : "80%",
                  background: (theme) =>
                    message.sender === "user"
                      ? theme.palette.grey[300]
                      : theme.palette.grey[200],
                  borderRadius:
                    message.sender === "user"
                      ? "18px 18px 4px 18px"
                      : "18px 18px 18px 4px",
                  border: "1px solid",
                  borderColor:
                    message.sender === "user" ? "transparent" : "divider",
                  backdropFilter: "blur(8px)",
                  boxShadow: STYLES.shadows.paper,
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    fontSize: "0.875rem",
                    lineHeight: 1.5,
                    color: (theme) => theme.palette.text.secondary,
                  }}
                >
                  {message.content}
                </Typography>
              </Paper>
            </Box>
          ))}
          {isLoading && (
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1.5,
                animation: "fadeIn 0.3s ease-in-out",
              }}
            >
              <Avatar
                sx={{
                  width: CONSTANTS.AVATAR_SIZE,
                  height: CONSTANTS.AVATAR_SIZE,
                  background: STYLES.gradients.ai,
                  boxShadow: STYLES.shadows.avatar,
                }}
              >
                <AiIcon sx={{ fontSize: 16, color: "white" }} />
              </Avatar>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  background: (theme) => theme.palette.grey[100],
                  borderRadius: "18px 18px 18px 4px",
                  border: "1px solid",
                  borderColor: "divider",
                  boxShadow: STYLES.shadows.paper,
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    gap: 0.5,
                    "& > div": {
                      width: 4,
                      height: 4,
                      borderRadius: "50%",
                      backgroundColor: "primary.main",
                      animation: "bounce 1.5s infinite ease-in-out",
                    },
                    "& > div:nth-of-type(1)": { animationDelay: "0s" },
                    "& > div:nth-of-type(2)": { animationDelay: "0.1s" },
                    "& > div:nth-of-type(3)": { animationDelay: "0.2s" },
                    "@keyframes bounce": {
                      "0%, 80%, 100%": { transform: "scale(0)" },
                      "40%": { transform: "scale(1)" },
                    },
                  }}
                >
                  <div />
                  <div />
                  <div />
                  <div />
                </Box>
              </Paper>
            </Box>
          )}
          <div ref={messagesEndRef} />
        </Box>

        {/* 功能按钮区域 */}
        <Box
          sx={{
            px: 2.5,
            pb: 1,
            display: "flex",
            justifyContent: "flex-end",
            gap: 1,
            borderTop: "1px solid",
            borderColor: "divider",
            background: (theme) => theme.palette.background.paper,
            backdropFilter: "blur(8px)",
          }}
        >
          <IconButton
            onClick={handleRetry}
            disabled={!lastInput || isLoading}
            size="small"
            sx={{
              width: 28,
              height: 28,
              color:
                lastInput && !isLoading ? "text.secondary" : "text.disabled",
              "&:hover": {
                backgroundColor: "action.hover",
                color: "primary.main",
              },
              transition: `all ${CONSTANTS.ANIMATION_DURATION} ease-in-out`,
            }}
          >
            <RefreshIcon sx={{ fontSize: 16 }} />
          </IconButton>
          <IconButton
            onClick={handleClearHistory}
            disabled={messages.length <= 1 || isLoading}
            size="small"
            sx={{
              width: 28,
              height: 28,
              color:
                messages.length > 1 && !isLoading
                  ? "text.secondary"
                  : "text.disabled",
              "&:hover": {
                backgroundColor: "action.hover",
                color: "error.main",
              },
              transition: `all ${CONSTANTS.ANIMATION_DURATION} ease-in-out`,
            }}
          >
            <DeleteIcon sx={{ fontSize: 16 }} />
          </IconButton>
        </Box>

        {/* 输入区域 */}
        <Box
          sx={{
            p: 2.5,
            pt: 1.5,
            display: "flex",
            gap: 1.5,
            alignItems: "flex-end",
            justifyContent: "center",
            background: (theme) => theme.palette.background.paper,
            backdropFilter: "blur(8px)",
          }}
        >
          <TextField
            fullWidth
            placeholder={t("ai_input_placeholder")}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
            variant="outlined"
            size="small"
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 3,
                fontSize: "0.875rem",
                border: "1px solid",
                borderColor: "divider",
                backdropFilter: "blur(8px)",
                "&:hover": {
                  borderColor: "primary.main",
                },
                "&.Mui-focused": {
                  borderColor: "primary.main",
                  boxShadow: (theme) =>
                    `0 0 0 2px ${theme.palette.primary.main}20`,
                },
              },
              "& .MuiOutlinedInput-input": {
                padding: "12px 14px",
              },
            }}
          />
          <IconButton
            onClick={isLoading ? handleStopGeneration : handleSendMessage}
            disabled={!inputValue.trim() && !isLoading}
            sx={{
              height: CONSTANTS.BUTTON_SIZE,
              width: CONSTANTS.BUTTON_SIZE,
              background: (theme) =>
                inputValue.trim() || isLoading
                  ? isLoading
                    ? STYLES.gradients.stop(theme)
                    : STYLES.gradients.ai(theme)
                  : "transparent",
              color: (theme) =>
                inputValue.trim() || isLoading
                  ? theme.palette.primary.contrastText
                  : theme.palette.text.secondary,
              border: "1px solid",
              borderColor: (theme) =>
                inputValue.trim() || isLoading
                  ? "transparent"
                  : theme.palette.divider,
              "&:hover": {
                background: (theme) =>
                  inputValue.trim() || isLoading
                    ? isLoading
                      ? `linear-gradient(135deg, ${theme.palette.error.dark} 0%, ${theme.palette.warning.dark} 100%)`
                      : `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.info.dark} 100%)`
                    : theme.palette.action.hover,
                transform: "scale(1.05)",
              },
              "&:disabled": {
                background: "transparent",
                color: (theme) => theme.palette.text.disabled,
                borderColor: (theme) => theme.palette.divider,
                transform: "none",
              },
              transition: `all ${CONSTANTS.ANIMATION_DURATION} ease-in-out`,
            }}
          >
            {isLoading ? (
              <StopIcon fontSize="small" />
            ) : (
              <AutoAwesomeOutlinedIcon fontSize="small" />
            )}
          </IconButton>
        </Box>
      </Box>
    </CustomPopover>
  );
};
