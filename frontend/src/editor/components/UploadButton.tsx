import React from "react";
import { Button } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";

export const UploadButton = (props: any) => {
  return (
    <Button
      variant="contained"
      component="label"
      startIcon={<CloudUploadIcon />}
      className={props.className}
      sx={props.sx}
      disabled={props.disabled}
      onClick={props.onClick}
      onMouseOver={props.onMouseOver}
      onMouseOut={props.onMouseOut}
    >
      Upload
      <input
        type="file"
        hidden
        accept={props.accept}
        onChange={props.onChange}
      />
    </Button>
  );
};
