import { fabric } from "fabric";

// @ts-ignore fabric controlsUtils
const controlsUtils = fabric.controlsUtils;

function isTransformCentered(transform) {
  return transform.originX === "center" && transform.originY === "center";
}
export const getLocalPoint = (transform, originX, originY, x, y) => {
  var target = transform.target,
    control = target.controls[transform.corner],
    zoom = target.canvas.getZoom(),
    padding = target.padding / zoom,
    localPoint = target.toLocalPoint(new fabric.Point(x, y), originX, originY);
  if (localPoint.x >= padding) {
    localPoint.x -= padding;
  }
  if (localPoint.x <= -padding) {
    localPoint.x += padding;
  }
  if (localPoint.y >= padding) {
    localPoint.y -= padding;
  }
  if (localPoint.y <= padding) {
    localPoint.y += padding;
  }
  localPoint.x -= control.offsetX;
  localPoint.y -= control.offsetY;
  return localPoint;
};

const _changeHeight = (eventData, transform, x, y) => {
  const target = transform.target,
    localPoint = getLocalPoint(
      transform,
      transform.originX,
      transform.originY,
      x,
      y
    ),
    strokePadding =
      target.strokeWidth / (target.strokeUniform ? target.scaleX : 1),
    multiplier = isTransformCentered(transform) ? 2 : 1,
    oldHeight = target.height,
    newHeight =
      Math.abs((localPoint.y * multiplier) / target.scaleY) - strokePadding;
  target.set("height", Math.max(newHeight, 0));
  return oldHeight !== newHeight;
};

export const initRectControl = () => {
  const objectControls = fabric.Object.prototype.controls;

  if (fabric.Rect) {
    const rectControls: any = (fabric.Rect.prototype.controls = {});
    rectControls.tr = objectControls.tr;
    rectControls.br = objectControls.br;
    rectControls.tl = objectControls.tl;
    rectControls.bl = objectControls.bl;
    rectControls.mt = objectControls.mt;
    rectControls.mb = objectControls.mb;
    rectControls.mtr = objectControls.mtr;
    rectControls.copy = objectControls.copy;
    rectControls.del = objectControls.del;

    rectControls.ml = new fabric.Control({
      x: -0.5,
      y: 0,
      actionHandler: controlsUtils.changeWidth,
      cursorStyleHandler: objectControls.ml.cursorStyleHandler,
      actionName: "resizing",
      render: objectControls.ml.render,
    });

    rectControls.mr = new fabric.Control({
      x: 0.5,
      y: 0,
      actionHandler: controlsUtils.changeWidth,
      cursorStyleHandler: objectControls.mr.cursorStyleHandler,
      actionName: "resizing",
      render: objectControls.mr.render,
    });

    rectControls.mt = new fabric.Control({
      x: 0,
      y: -0.5,
      offsetY: -1,
      actionHandler: _changeHeight,
      cursorStyleHandler: objectControls.mt.cursorStyleHandler,
      actionName: "resizing",
      render: objectControls.mt.render,
    });

    rectControls.mb = new fabric.Control({
      x: 0,
      y: 0.5,
      offsetY: 1,
      actionHandler: _changeHeight,
      cursorStyleHandler: objectControls.mb.cursorStyleHandler,
      actionName: "resizing",
      render: objectControls.mb.render,
    });
  }
};
