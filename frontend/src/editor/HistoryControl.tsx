import RedoIcon from "@mui/icons-material/Redo";
import UndoIcon from "@mui/icons-material/Undo";
import { Box, IconButton, Tooltip } from "@mui/material";
import { observer } from "mobx-react-lite";
import React from "react";
import { StoreContext } from "../store";

export const Menu = observer(() => {
  const store = React.useContext(StoreContext);

  // 获取撤销/重做操作的类型，用于提示
  const undoActionType = store.getUndoActionType?.();
  const redoActionType = store.getRedoActionType?.();

  return (
    <Box sx={{ display: "flex", gap: 1 }}>
      <Tooltip title={`撤销${undoActionType ? ` - ${undoActionType}` : ""}`}>
        <span>
          <IconButton
            onClick={() => store.undo()}
            disabled={!undoActionType}
            size="small"
          >
            <UndoIcon fontSize="small" />
          </IconButton>
        </span>
      </Tooltip>

      <Tooltip title={`重做${redoActionType ? ` - ${redoActionType}` : ""}`}>
        <span>
          <IconButton
            onClick={() => store.redo()}
            disabled={!redoActionType}
            size="small"
          >
            <RedoIcon fontSize="small" />
          </IconButton>
        </span>
      </Tooltip>
    </Box>
  );
});
