import { Paper, IconButton, Tooltip, Box } from "@mui/material";
import { observer } from "mobx-react-lite";
import { useLayoutStore } from "../store/store-context";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import TextFieldsIcon from "@mui/icons-material/TextFields";
import VideocamIcon from "@mui/icons-material/Videocam";
import ImageIcon from "@mui/icons-material/Image";
import CategoryIcon from "@mui/icons-material/Category";
import AudiotrackIcon from "@mui/icons-material/Audiotrack";
import LayersIcon from "@mui/icons-material/Layers";
import React, { useState, useRef, useEffect, useCallback } from "react";
import { StoreContext } from "../store";
import ClosedCaptionIcon from "@mui/icons-material/ClosedCaption";
import DashboardCustomizeIcon from "@mui/icons-material/DashboardCustomize";
import GifBoxIcon from "@mui/icons-material/GifBox";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
const MenuList = observer(() => {
  const layoutStore = useLayoutStore();
  const store = React.useContext(StoreContext);

  // 滚动相关状态
  const [canScrollUp, setCanScrollUp] = useState(false);
  const [canScrollDown, setCanScrollDown] = useState(false);
  const [maxHeight, setMaxHeight] = useState(0);
  const [needsScrolling, setNeedsScrolling] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 定义菜单项配置，使代码更简洁和易维护
  const menuItems = [
    // { id: "layers", icon: <LayersIcon />, option: "layers" },
    { id: "templates", icon: <DashboardCustomizeIcon />, option: "Templates" },
    { id: "uploads", icon: <UploadFileIcon />, option: "uploads" },

    { id: "videos", icon: <VideocamIcon />, option: "Video" },
    {
      id: "images",
      icon: <ImageIcon />,
      option: "Image",
    },
    { id: "audios", icon: <AudiotrackIcon />, option: "Audio" },
    { id: "texts", icon: <TextFieldsIcon />, option: "Text" },
    {
      id: "gifs",
      icon: <GifBoxIcon />,
      option: "Gif",
    },
    { id: "shapes", icon: <CategoryIcon />, option: "Shape" },
    { id: "captions", icon: <ClosedCaptionIcon />, option: "Caption" },
  ];

  // 计算显示所有菜单项所需的高度
  const calculateRequiredHeight = useCallback(() => {
    const buttonHeight = 48; // 每个按钮的高度（包括margin）
    const containerPadding = 24; // 容器上下内边距 (py: 1.5 = 12px * 2)

    return menuItems.length * buttonHeight + containerPadding;
  }, [menuItems.length]);

  // 根据时间线高度动态计算菜单列表的最大高度
  const calculateMaxHeight = useCallback(() => {
    // 基础高度计算：视窗高度 - 顶部导航栏 - 时间线高度 - 边距
    const navbarHeight = 90; // 顶部导航栏高度
    const marginBottom = 20; // 底部边距
    const marginTop = 20; // 顶部边距

    // 动态计算最大高度，确保菜单不会与时间线重叠
    const calculatedMaxHeight =
      window.innerHeight -
      navbarHeight -
      store.timelineHeight -
      marginBottom -
      marginTop;

    // 计算显示所有菜单项所需的高度
    const requiredHeight = calculateRequiredHeight();

    // 如果计算出的最大高度足够显示所有菜单项，则使用所需高度
    // 否则使用计算出的最大高度，但不少于最小高度
    const minHeight = 200;

    if (calculatedMaxHeight >= requiredHeight) {
      return requiredHeight;
    } else {
      return Math.max(minHeight, calculatedMaxHeight);
    }
  }, [store.timelineHeight, calculateRequiredHeight]);

  // 更新最大高度和滚动状态
  const updateMaxHeight = useCallback(() => {
    const newMaxHeight = calculateMaxHeight();
    const requiredHeight = calculateRequiredHeight();

    setMaxHeight(newMaxHeight);
    // 只有当最大高度小于所需高度时才需要滚动
    setNeedsScrolling(newMaxHeight < requiredHeight);
  }, [calculateMaxHeight, calculateRequiredHeight]);

  // 监听时间线高度变化和窗口大小变化
  useEffect(() => {
    updateMaxHeight();
  }, [updateMaxHeight, store.timelineHeight]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      updateMaxHeight();
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [updateMaxHeight]);

  // 检查滚动状态
  const checkScrollState = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container && needsScrolling) {
      const { scrollTop, scrollHeight, clientHeight } = container;
      setCanScrollUp(scrollTop > 0);
      setCanScrollDown(scrollTop < scrollHeight - clientHeight - 1);
    } else {
      // 如果不需要滚动，则隐藏所有箭头
      setCanScrollUp(false);
      setCanScrollDown(false);
    }
  }, [needsScrolling]);

  // 滚动处理函数
  const handleScrollUp = () => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollBy({ top: -48, behavior: "smooth" }); // 每次滚动一个按钮的高度
    }
  };

  const handleScrollDown = () => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollBy({ top: 48, behavior: "smooth" }); // 每次滚动一个按钮的高度
    }
  };

  // 监听滚动事件和窗口大小变化
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      checkScrollState();
      container.addEventListener("scroll", checkScrollState);
      window.addEventListener("resize", checkScrollState);

      return () => {
        container.removeEventListener("scroll", checkScrollState);
        window.removeEventListener("resize", checkScrollState);
      };
    }
  }, [checkScrollState]);

  // 当最大高度或滚动需求变化时，重新检查滚动状态
  useEffect(() => {
    // 使用 setTimeout 确保 DOM 更新完成后再检查
    const timer = setTimeout(() => {
      checkScrollState();
    }, 100);

    return () => clearTimeout(timer);
  }, [maxHeight, needsScrolling, checkScrollState]);

  const handleMenuClick = (item: (typeof menuItems)[0]) => {
    if (item.option) {
      //@ts-ignore
      store.setSelectedMenuOption(item.option);
    }
    //@ts-ignore
    layoutStore.setActiveMenuItem(item.id);
    layoutStore.setShowMenuItem(true);
  };

  const getButtonStyle = (itemName: string) => {
    const isActive =
      layoutStore.showMenuItem && layoutStore.activeMenuItem === itemName;

    return {
      color: isActive ? "primary.main" : "text.secondary",
      bgcolor: isActive ? "action.selected" : "transparent",
      "&:hover": {
        bgcolor: "action.hover",
        color: isActive ? "primary.main" : "text.primary",
      },
      transition: "all 0.2s ease",
      my: 0.5,
    };
  };

  return (
    <Paper
      sx={{
        zIndex: 201,
        width: "60px",
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
        mt: 3,
        left: "16px",
        bgcolor: "grey.100",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        maxHeight: `${maxHeight}px`, // 使用动态计算的最大高度
        overflow: "hidden",
        transition: "max-height 0.3s ease", // 添加平滑过渡动画
      }}
    >
      {/* 向上滚动按钮 - 只有在需要滚动且可以向上滚动时才显示 */}
      {needsScrolling && canScrollUp && (
        <IconButton
          size="small"
          onClick={handleScrollUp}
          sx={{
            color: "text.secondary",
            "&:hover": {
              bgcolor: "action.hover",
              color: "text.primary",
            },
            transition: "all 0.2s ease",
            my: 0.25,
            minHeight: "32px",
            height: "32px",
          }}
        >
          <KeyboardArrowUpIcon fontSize="small" />
        </IconButton>
      )}

      {/* 可滚动的菜单项容器 */}
      <Box
        ref={scrollContainerRef}
        sx={{
          flex: 1,
          overflow: "auto",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          py: needsScrolling ? 0.5 : 1.5,
          "&::-webkit-scrollbar": {
            display: "none", // 隐藏滚动条
          },
          scrollbarWidth: "none", // Firefox
          msOverflowStyle: "none", // IE
        }}
        onScroll={checkScrollState}
      >
        {menuItems.map((item) => (
          <Tooltip key={item.id} title={item.option} placement="right" arrow>
            <IconButton
              size="medium"
              onClick={() => handleMenuClick(item)}
              sx={getButtonStyle(item.id)}
            >
              {item.icon}
            </IconButton>
          </Tooltip>
        ))}
      </Box>

      {/* 向下滚动按钮 - 只有在需要滚动且可以向下滚动时才显示 */}
      {needsScrolling && canScrollDown && (
        <IconButton
          size="small"
          onClick={handleScrollDown}
          sx={{
            color: "text.secondary",
            "&:hover": {
              bgcolor: "action.hover",
              color: "text.primary",
            },
            transition: "all 0.2s ease",
            my: 0.25,
            minHeight: "32px",
            height: "32px",
          }}
        >
          <KeyboardArrowDownIcon fontSize="small" />
        </IconButton>
      )}
    </Paper>
  );
});

export default MenuList;
