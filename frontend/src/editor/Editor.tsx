import { Box } from "@mui/material";

import { observer } from "mobx-react-lite";
import { Navbar } from "./Navbar";
import MenuList from "./MenuLists";
import { MenuItem } from "./menu-item";
import ControlList from "./ControlLists";
import { ControlItem } from "./control-item";

import { TimeLine } from "./timeline/TimeLinePanel";
import { CanvasContainer } from "./CanvasContainer";

const Editor = () => {
  return (
    <Box
      sx={{
        height: "100vh",
        width: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Navbar />

      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          minHeight: 0, // 确保flex子元素可以收缩
        }}
      >
        {/* 画布区域 */}
        <Box sx={{ flex: 1, position: "relative", overflow: "hidden" }}>
          <MenuList />
          <MenuItem />
          <ControlList />
          <ControlItem />
          <CanvasContainer />
        </Box>

        {/* 时间线区域 - 使用固定高度但允许调整 */}
        <Box sx={{ flexShrink: 0 }}>
          <TimeLine />
        </Box>
      </Box>
    </Box>
  );
};

export default Editor;
