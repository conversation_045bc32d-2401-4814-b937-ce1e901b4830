import React from "react";
import { Box, IconButton, Paper } from "@mui/material";
import { observer } from "mobx-react-lite";
import CloseIcon from "@mui/icons-material/Close";
import { useLayoutStore } from "../../store/store-context";
import { useEffect, useState } from "react";
import Presets from "./Presets";
import Animations from "./Animations";
import Smart from "./Smart";
import BasicText from "./BasicText";
import BasicImage from "./BasicImage";
import BasicVideo from "./BasicVideo";
import BasicAudio from "./BasicAudio";
import BasicShape from "./BasicShape";
import BasicGif from "./BasicGif";
import CaptionText from "./CaptionText";
import { StoreContext } from "../../store";
import { useLanguage } from "../../i18n/LanguageContext";

type ControlType =
  | "text"
  | "image"
  | "video"
  | "audio"
  | "shape"
  | "caption"
  | "gif";

interface ContainerProps {
  children: React.ReactNode;
}

const Container = observer(({ children }: ContainerProps) => {
  const layoutStore = useLayoutStore();
  const store = React.useContext(StoreContext);
  const [displayToolbox, setDisplayToolbox] = useState(false);

  useEffect(() => {
    setDisplayToolbox(!!layoutStore.activeToolboxItem);
  }, [layoutStore.activeToolboxItem]);

  // 检查是否有选中的元素或字幕
  const hasSelectedItem = store.selectedElement || store.getSelectedCaption();

  if (!layoutStore.controlsVisible || !hasSelectedItem) {
    return null;
  }

  return (
    <Box
      sx={{
        width: "280px",
        height: "calc(100% - 96px)",
        mt: 3,
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
        right: layoutStore.activeToolboxItem ? "75px" : "-100%",
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        visibility: "visible",
        zIndex: 200,
        borderRadius: 2,
        display: "flex",
      }}
    >
      <Paper
        data-testid="control-panel"
        onClick={(e) => {
          // 阻止点击事件冒泡到Canvas，防止取消选中
          e.stopPropagation();
        }}
        sx={{
          width: "100%",
          height: "100%",
          position: "relative",
          bgcolor: "grey.100",
          display: "flex",
        }}
      >
        <IconButton
          onClick={() => {
            setDisplayToolbox(false);
            layoutStore.setActiveToolboxItem(null);
          }}
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            width: 32,
            height: 32,
            color: "text.secondary",
          }}
        >
          <CloseIcon sx={{ fontSize: 16 }} />
        </IconButton>
        {children}
      </Paper>
    </Box>
  );
});

interface ActiveControlItemProps {
  activeToolboxItem?: string;
  controlType?: ControlType;
}

export const ControlItem = observer(() => {
  const store = React.useContext(StoreContext);
  const layoutStore = useLayoutStore();
  const { t } = useLanguage();
  const [controlType, setControlType] = useState<ControlType | null>(null);

  const componentMap = {
    text: {
      "basic-text": <BasicText />,
      "preset-text": <Presets element={store.selectedElement} />,
      animation: <Animations />,
      smart: <Smart />,
    },
    image: {
      "basic-image": <BasicImage />,
      animation: <Animations />,
      smart: <Smart />,
    },
    video: {
      "basic-video": <BasicVideo />,
      animation: <Animations />,
    },
    audio: {
      "basic-audio": <BasicAudio />,
      smart: <Smart />,
    },
    shape: {
      "basic-shape": <BasicShape />,
      animation: <Animations />,
    },
    gif: {
      "basic-gif": <BasicGif />,
      "preset-gif": <Presets element={store.selectedElement} />,
      animation: <Animations />,
      smart: <Smart />,
    },
    caption: {
      "caption-text": <CaptionText />,
      "preset-caption": <Presets element={null} isCaptionMode={true} />,
    },
  };

  const ActiveControlItem = observer(
    ({ activeToolboxItem, controlType }: ActiveControlItemProps) => {
      if (!activeToolboxItem || !controlType) return null;
      return componentMap[controlType]?.[activeToolboxItem] || null;
    }
  );

  useEffect(() => {
    const selectedCaption = store.getSelectedCaption();

    if (store.selectedElement) {
      const newControlType = store.selectedElement.type as ControlType;

      // 只有当 controlType 发生变化时，才重置 activeToolboxItem
      if (newControlType !== controlType) {
        setControlType(newControlType);
        const firstComponentKey = Object.keys(componentMap[newControlType])[0];
        layoutStore.setActiveToolboxItem(firstComponentKey);
        // 确保控制面板可见
        layoutStore.setControlsVisible(true);
      }
    } else if (selectedCaption) {
      // 如果选中了字幕
      const newControlType = "caption" as ControlType;

      if (newControlType !== controlType) {
        setControlType(newControlType);
        const firstComponentKey = Object.keys(componentMap[newControlType])[0];
        layoutStore.setActiveToolboxItem(firstComponentKey);
        // 确保控制面板可见
        layoutStore.setControlsVisible(true);
      }

      // 确保 Canvas 上的字幕对象被选中
      if (
        store.canvas &&
        store.captionManager &&
        store.captionManager.captionTextObject
      ) {
        store.canvas.setActiveObject(store.captionManager.captionTextObject);
        store.canvas.requestRenderAll();
      }
    } else {
      // 没有选中任何元素或字幕
      setControlType(null);
      layoutStore.setActiveToolboxItem(null);
      // 隐藏控制面板
      layoutStore.setControlsVisible(false);
    }
  }, [store.selectedElement?.type, store.captions, store.getSelectedCaption()]);

  return (
    <Container>
      <ActiveControlItem
        controlType={controlType}
        activeToolboxItem={layoutStore.activeToolboxItem}
      />
    </Container>
  );
});
