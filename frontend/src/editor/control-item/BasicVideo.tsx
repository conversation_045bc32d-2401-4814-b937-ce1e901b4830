import BorderAllIcon from "@mui/icons-material/BorderAll";
import CancelIcon from "@mui/icons-material/Cancel";
import CropIcon from "@mui/icons-material/Crop";
import {
  Box,
  Divider,
  IconButton,
  MenuItem,
  Select,
  Slider,
  Stack,
  Tab,
  Tabs,
  Tooltip,
  Typography,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { observer } from "mobx-react";
import React from "react";
import ColorPicker from "../components/color/ColorPicker";
import BaseSetting from "./BaseSetting";
import { StoreContext } from "../../store";
import { BorderStyle, EditorElement, VideoEditorElement } from "../../types";
import FilterControl from "./FilterControl";
import PlaybackSpeedControl from "./PlaybackSpeedControl";
import VolumeControl from "./VolumeControl";
import { useLanguage } from "../../i18n/LanguageContext";

const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.8rem",
  fontWeight: theme.typography.fontWeightRegular,

  "&:hover": {
    color: theme.palette.primary.main,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
  },
}));

const EFFECT_TYPE_TO_LABEL: Record<string, string> = {
  none: "None",
  blackAndWhite: "Black and White",
  saturate: "Saturate",
  sepia: "Sepia",
  invert: "Invert",
};

interface VideoSettingProps {
  element: EditorElement | null;
}

const VideoSetting = observer(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const element = store.selectedElement;
  // 将所有 hooks 移到最顶部，在任何条件检查之前
  const [isCropping, setIsCropping] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState(0);
  const [showBorderSettings, setShowBorderSettings] = React.useState(false);
  const [playbackSpeed, setPlaybackSpeed] = React.useState(1);
  const [volume, setVolume] = React.useState(1);

  // 使用 useEffect 来同步状态
  React.useEffect(() => {
    if (element?.type === "video") {
      const videoElement = element as VideoEditorElement;
      setPlaybackSpeed((videoElement as any).playbackSpeed || 1);
      setVolume((videoElement as any).volume || 1);
    }
  }, [element]);

  // 添加空值检查和类型检查
  if (!element || !element.properties) {
    return <></>;
  }

  // 检查是否为视频元素
  if (element.type !== "video") {
    return <></>;
  }

  // 现在我们知道 element 是 VideoEditorElement 类型
  const videoElement = element as VideoEditorElement;

  console.log((videoElement as any).playbackSpeed);

  const handleCrop = () => {
    if (!isCropping) {
      store.startCropMode(videoElement.id);
      setIsCropping(true);
    } else {
      store.applyCrop();
      setIsCropping(false);
    }
  };

  const handleCancelCrop = () => {
    store.cancelCrop();
    setIsCropping(false);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleBorderToggle = () => {
    setShowBorderSettings(!showBorderSettings);
  };

  const updateFilter = (filterType: string, value: number) => {
    store.setMediaFilter(
      videoElement.id,
      filterType as "brightness" | "contrast" | "saturation" | "hue" | "blur",
      value
    );
  };

  const handleResetFilters = () => {
    const defaultFilters = {
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0,
      blur: 0,
    };

    Object.entries(defaultFilters).forEach(([filterType, value]) => {
      updateFilter(filterType, value);
    });
  };

  const updateBorder = (
    property: keyof BorderStyle,
    value: string | number
  ) => {
    store.setMediaElementBorder(videoElement.id, property, value);
  };

  return (
    <Box
      sx={{
        width: "100%",
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          {t("video_settings")}
        </Typography>
      </Box>
      {/* <Divider /> */}

      <StyledTabs value={activeTab} onChange={handleTabChange}>
        <StyledTab label={t("basic")} />
        <StyledTab label={t("advanced")} />
      </StyledTabs>
      <Box
        sx={{
          m: 2,
          width: "250px",
          height: "100%",
          overflow: "auto",
          pr: 2,
          "&::-webkit-scrollbar": {
            width: "1px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(255, 255, 255, 0.2)",
            borderRadius: "1px",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.3)",
            },
          },
        }}
      >
        {activeTab === 0 && (
          <Box>
            <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
              <Tooltip title={isCropping ? t("apply_crop") : t("crop_video")}>
                <IconButton
                  onClick={handleCrop}
                  color={isCropping ? "secondary" : "primary"}
                >
                  <CropIcon />
                </IconButton>
              </Tooltip>
              {isCropping && (
                <Tooltip title={t("cancel_crop")}>
                  <IconButton onClick={handleCancelCrop}>
                    <CancelIcon />
                  </IconButton>
                </Tooltip>
              )}
              <Tooltip
                title={
                  showBorderSettings ? t("hide_settings") : t("show_settings")
                }
              >
                <IconButton
                  onClick={handleBorderToggle}
                  color={showBorderSettings ? "secondary" : "primary"}
                >
                  <BorderAllIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            {showBorderSettings && (
              <Box sx={{ mb: 3 }}>
                <Stack spacing={2}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px" }}
                    >
                      {t("border_width")}
                    </Typography>
                    <Slider
                      size="small"
                      value={videoElement.properties.border?.width ?? 0}
                      min={0}
                      max={20}
                      onChange={(e, newValue) =>
                        updateBorder("width", Number(newValue))
                      }
                      sx={{ flex: 1 }}
                    />
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px" }}
                    >
                      {t("border_color")}
                    </Typography>
                    <ColorPicker
                      color={videoElement.properties.border?.color ?? "#000000"}
                      onChange={(color) => updateBorder("color", color)}
                    />
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px" }}
                    >
                      {t("border_style")}
                    </Typography>
                    <Select
                      size="small"
                      value={videoElement.properties.border?.style ?? "solid"}
                      onChange={(e) => updateBorder("style", e.target.value)}
                      fullWidth
                      renderValue={(selected) => (
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: `2px ${selected} black`,
                            }}
                          />
                          <Typography variant="body1" sx={{ fontSize: "1rem" }}>
                            {t(selected)}
                          </Typography>
                        </Box>
                      )}
                    >
                      {["solid", "dashed", "dotted"].map((style) => (
                        <MenuItem key={style} value={style}>
                          <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                mr: 1,
                                border: `2px ${style} black`,
                              }}
                            />
                            <Typography
                              variant="body1"
                              sx={{ fontSize: "1rem" }}
                            >
                              {t(style)}
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px" }}
                    >
                      {t("border_radius")}
                    </Typography>
                    <Slider
                      size="small"
                      value={videoElement.properties.border?.borderRadius ?? 0}
                      min={0}
                      max={100}
                      onChange={(e, newValue) =>
                        updateBorder("borderRadius", Number(newValue))
                      }
                      sx={{ flex: 1 }}
                    />
                  </Box>
                </Stack>
              </Box>
            )}
            <BaseSetting />
            <Box sx={{ mb: 2 }}>
              <PlaybackSpeedControl element={videoElement} />
            </Box>
            <Box sx={{ mb: 1 }}>
              <VolumeControl element={videoElement} />
            </Box>
          </Box>
        )}

        {activeTab === 1 && (
          <Box>
            <FilterControl element={videoElement} />
          </Box>
        )}
      </Box>
    </Box>
  );
});

export default VideoSetting;
