import { useState } from "react";
import { Box, Typography, <PERSON>lider, TextField, IconButton } from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";

const Opacity = () => {
  const [value, setValue] = useState<number>(10);

  const handleSliderChange = (event: Event, newValue: number | number[]) => {
    setValue(newValue as number);
  };

  return (
    <Box>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography variant="body2" color="text.secondary">
            Opacity
          </Typography>
        </Box>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 40px 24px",
            gap: 0.5,
            alignItems: "center",
          }}
        >
          <Slider
            value={value}
            onChange={handleSliderChange}
            max={1}
            step={0.1}
            aria-label="Opacity"
            sx={{
              "& .MuiSlider-thumb": {
                width: 12,
                height: 12,
              },
              "& .MuiSlider-rail": {
                opacity: 0.3,
              },
            }}
          />
          <TextField
            size="small"
            defaultValue={100}
            inputProps={{
              style: {
                padding: "4px",
                textAlign: "center",
                fontSize: "0.875rem",
              },
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: "divider",
                },
              },
            }}
          />
          <IconButton
            size="small"
            sx={{
              width: 24,
              height: 24,
              color: "text.secondary",
              "&:hover": {
                color: "text.primary",
                bgcolor: "action.hover",
              },
            }}
          >
            <RefreshIcon sx={{ fontSize: 14 }} />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
};

export default Opacity;
