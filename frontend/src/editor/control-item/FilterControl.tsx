import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  IconButton,
  Select,
  MenuItem,
  Typography,
  Stack,
  Divider,
  Paper,
  Tooltip,
  Chip,
  alpha,
  useTheme,
} from "@mui/material";
import SliderWithInput from "./SliderWithInput";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import TuneIcon from "@mui/icons-material/Tune";
import { StoreContext } from "../../store";
import {
  EffecType,
  ImageEditorElement,
  VideoEditorElement,
  GifEditorElement,
  EditorElement,
} from "../../types";
import { observer } from "mobx-react";
import { useLanguage } from "../../i18n/LanguageContext";
import { throttle } from "lodash";

const EFFECT_TYPE_TO_LABEL: Record<string, string> = {
  none: "None",
  blackAndWhite: "Black and White",
  saturate: "Saturate",
  sepia: "Sepia",
  invert: "Invert",
};

// 预设滤镜效果
const FILTER_PRESETS = [
  {
    name: "default",
    filters: { brightness: 0, contrast: 0, saturation: 0, hue: 0, blur: 0 },
  },
  {
    name: "warm",
    filters: { brightness: 5, contrast: 10, saturation: 15, hue: 5, blur: 0 },
  },
  {
    name: "cool",
    filters: { brightness: 0, contrast: 5, saturation: -10, hue: -10, blur: 0 },
  },
  {
    name: "vintage",
    filters: {
      brightness: -5,
      contrast: -10,
      saturation: -20,
      hue: 0,
      blur: 5,
    },
  },
  {
    name: "sharp",
    filters: { brightness: 10, contrast: 30, saturation: 10, hue: 0, blur: 0 },
  },
  {
    name: "soft",
    filters: { brightness: 5, contrast: -10, saturation: -5, hue: 0, blur: 15 },
  },
];

interface FilterControlProps {
  element: EditorElement | null;
}

const FilterControl = observer(({ element }: FilterControlProps) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const theme = useTheme();
  const [localFilters, setLocalFilters] = useState({
    brightness: 0,
    contrast: 0,
    saturation: 0,
    hue: 0,
    blur: 0,
  });

  // 添加激活滤镜预设状态
  const [activePreset, setActivePreset] = useState("default");

  if (!element || !element.properties) {
    return <></>;
  }

  if (
    element.type !== "image" &&
    element.type !== "video" &&
    element.type !== "gif"
  ) {
    return <></>;
  }

  // 支持image、video和gif元素
  const mediaElement = element as
    | ImageEditorElement
    | VideoEditorElement
    | GifEditorElement;

  useEffect(() => {
    if (mediaElement.properties.filters) {
      setLocalFilters({
        brightness: mediaElement.properties.filters.brightness ?? 0,
        contrast: mediaElement.properties.filters.contrast ?? 0,
        saturation: mediaElement.properties.filters.saturation ?? 0,
        hue: mediaElement.properties.filters.hue ?? 0,
        blur: mediaElement.properties.filters.blur ?? 0,
      });
    }
  }, [mediaElement.id, mediaElement.properties.filters]);

  const updateFilter = (filterType: string, value: number) => {
    store.setMediaFilter(
      mediaElement.id,
      filterType as "brightness" | "contrast" | "saturation" | "hue" | "blur",
      value
    );
  };

  // 使用节流函数优化拖拽过程中的更新
  const throttledUpdateFilter = useCallback(
    throttle((filterType: string, value: number) => {
      updateFilter(filterType, value);
    }, 100), // 100ms节流，可根据效果调整
    [mediaElement.id]
  );

  const handleFilterChange = (filterType: string, value: number) => {
    // 更新局部状态，立即反映在UI上
    setLocalFilters((prev) => ({
      ...prev,
      [filterType]: value,
    }));
    // 预设状态重置为自定义
    setActivePreset("custom");
    // 节流更新到store
    throttledUpdateFilter(filterType, value);
  };

  const handleResetFilters = () => {
    const defaultFilters = {
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0,
      blur: 0,
    };

    // 更新局部状态
    setLocalFilters(defaultFilters);
    setActivePreset("default");

    // 更新store
    Object.entries(defaultFilters).forEach(([filterType, value]) => {
      updateFilter(filterType, value);
    });
  };

  // 应用预设滤镜
  const applyPreset = (presetName: string) => {
    const preset = FILTER_PRESETS.find((p) => p.name === presetName);
    if (!preset) return;

    setLocalFilters(preset.filters);
    setActivePreset(presetName);

    // 更新store
    Object.entries(preset.filters).forEach(([filterType, value]) => {
      updateFilter(filterType, value as number);
    });
  };

  // 创建一个函数来获取翻译后的效果标签
  const getEffectLabel = (type: string): string => {
    switch (type) {
      case "none":
        return t("none");
      case "blackAndWhite":
        return t("black_and_white");
      case "saturate":
        return t("saturate");
      case "sepia":
        return t("sepia");
      case "invert":
        return t("invert");
      default:
        return type;
    }
  };

  // 创建一个函数来获取翻译后的滤镜名称
  const getFilterLabel = (filter: string): string => {
    switch (filter) {
      case "Brightness":
        return t("brightness");
      case "Contrast":
        return t("contrast");
      case "Saturation":
        return t("saturation");
      case "Hue":
        return t("hue");
      case "Blur":
        return t("blur");
      default:
        return filter;
    }
  };

  // 获取预设名称的翻译
  const getPresetLabel = (presetName: string): string => {
    switch (presetName) {
      case "default":
        return t("default");
      case "warm":
        return t("warm");
      case "cool":
        return t("cool");
      case "vintage":
        return t("vintage");
      case "sharp":
        return t("sharp");
      case "soft":
        return t("soft");
      case "custom":
        return t("custom");
      default:
        return presetName;
    }
  };

  return (
    <Box>
      {/* 标题栏 */}
      <Stack
        direction="row"
        spacing={1}
        sx={{
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Stack direction="row" spacing={1} alignItems="center">
          <TuneIcon fontSize="small" color="primary" />
          <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
            {t("filters")}
          </Typography>
        </Stack>
        <Tooltip title={t("reset_filters")}>
          <IconButton onClick={handleResetFilters} size="small" color="primary">
            <RestartAltIcon />
          </IconButton>
        </Tooltip>
      </Stack>

      {/* 效果选择器 */}
      <Paper
        elevation={0}
        sx={{
          mb: 2,
          backgroundColor: alpha(theme.palette.background.default, 0.5),
          borderRadius: 1,
        }}
      >
        <Typography
          variant="body2"
          sx={{
            mb: 1,
            fontWeight: "medium",
            color: theme.palette.text.secondary,
          }}
        >
          {t("effect_type")}
        </Typography>
        <Select
          size="small"
          value={mediaElement.properties.effect?.type}
          onChange={(e) => {
            const type = e.target.value;
            store.updateEffect(mediaElement.id, {
              type: type as EffecType,
            });
          }}
          sx={{
            width: "100%",
            borderRadius: 1,
            backgroundColor: alpha(theme.palette.background.paper, 0.7),
            boxShadow: `0 1px 3px ${alpha(theme.palette.common.black, 0.1)}`,
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              backgroundColor: alpha(theme.palette.background.paper, 0.9),
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: alpha(theme.palette.divider, 0.5),
              borderWidth: "1px",
            },
            "& .MuiSelect-select": {
              padding: "8px 14px",
              fontWeight: "medium",
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderColor: theme.palette.primary.main,
              borderWidth: "1px",
            },
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                borderRadius: 1,
                boxShadow: `0 4px 8px ${alpha(
                  theme.palette.common.black,
                  0.15
                )}`,
              },
            },
          }}
        >
          {Object.entries(EFFECT_TYPE_TO_LABEL).map(([type, _]) => (
            <MenuItem
              key={type}
              value={type}
              sx={{
                borderRadius: 0.5,
                margin: "2px 4px",
                "&.Mui-selected": {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  fontWeight: "medium",
                },
                "&:hover": {
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                },
              }}
            >
              {getEffectLabel(type)}
            </MenuItem>
          ))}
        </Select>
      </Paper>

      {/* 预设滤镜 */}
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="body2"
          sx={{
            mb: 1,
            fontWeight: "medium",
            color: theme.palette.text.secondary,
          }}
        >
          {t("presets")}
        </Typography>
        <Stack direction="row" spacing={1} sx={{ flexWrap: "wrap", gap: 1 }}>
          {FILTER_PRESETS.map((preset) => (
            <Chip
              key={preset.name}
              label={getPresetLabel(preset.name)}
              size="small"
              color={activePreset === preset.name ? "primary" : "default"}
              variant={activePreset === preset.name ? "filled" : "outlined"}
              onClick={() => applyPreset(preset.name)}
              sx={{ mb: 0.5 }}
            />
          ))}
          {activePreset === "custom" && (
            <Chip
              label={t("custom")}
              size="small"
              color="primary"
              variant="filled"
              sx={{ mb: 0.5 }}
            />
          )}
        </Stack>
      </Box>

      <Divider sx={{ mb: 2, opacity: 0.5 }} />

      {/* 滤镜调整滑块 */}
      <Typography
        variant="body2"
        sx={{
          mb: 1,
          fontWeight: "medium",
          color: theme.palette.text.secondary,
        }}
      >
        {t("adjust")}
      </Typography>
      <Box
        sx={{
          pr: 1,
        }}
      >
        {["Brightness", "Contrast", "Saturation", "Hue", "Blur"].map(
          (filter) => (
            <Box key={filter} sx={{ mb: 1 }}>
              <SliderWithInput
                label={getFilterLabel(filter)}
                value={
                  localFilters[
                    filter.toLowerCase() as keyof typeof localFilters
                  ]
                }
                min={filter === "Blur" ? 0 : -100}
                max={100}
                onChange={(newValue) => {
                  // 实时更新局部状态并节流更新到store
                  handleFilterChange(filter.toLowerCase(), newValue);
                }}
                onChangeCommitted={(newValue) => {
                  // 拖拽结束时确保最终值同步到store
                  updateFilter(filter.toLowerCase(), newValue);
                }}
              />
            </Box>
          )
        )}
      </Box>
    </Box>
  );
});

export default FilterControl;
