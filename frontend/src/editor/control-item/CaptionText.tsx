import React, {
  useState,
  useEffect,
  use<PERSON>ontext,
  useMemo,
  use<PERSON><PERSON>back,
} from "react";
import { Divider, SelectChangeEvent, Stack, Button } from "@mui/material";
import {
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatAlignLeft,
  FormatAlignCenter,
  FormatAlignRight,
  StrikethroughS,
} from "@mui/icons-material";
import { observer } from "mobx-react-lite";
import { StoreContext } from "../../store";

import {
  ControlPanel,
  ColorRow,
  ToggleButtonRow,
  SelectRow,
} from "../../components/common";
import SliderWithInput from "./SliderWithInput";
import BaseSetting from "./BaseSetting";
import { useLanguage } from "../../i18n/LanguageContext";

// 字体列表
const fontFamilies = [
  "Arial",
  "Helvetica",
  "Times New Roman",
  "Georgia",
  "Verdana",
  "Courier New",
  "Impact",
  "Comic Sans MS",
  "Trebuchet MS",
  "Arial Black",
  "Palatino",
  "Garamond",
  "Bookman",
  "Avant Garde",
  "Roboto",
  "Open Sans",
  "Lato",
  "Montserrat",
  "Source Sans Pro",
  "Oswald",
  "Raleway",
  "PT Sans",
  "Ubuntu",
  "Merriweather",
  "Playfair Display",
];

interface CaptionTextProps {
  // 不再需要传入特定的caption，因为我们使用全局样式
}

const CaptionText = observer(({}: CaptionTextProps) => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();

  // 获取全局字幕样式
  const globalStyle = store.getGlobalCaptionStyle();

  // 细粒度状态管理 - 参考文本元素的模式，为每个属性单独使用useState
  const [fontSize, setFontSize] = useState(globalStyle.fontSize);
  const [textAlign, setTextAlign] = useState(globalStyle.textAlign);
  const [styles, setStyles] = useState(globalStyle.styles);
  const [charSpacing, setCharSpacing] = useState(globalStyle.charSpacing);
  const [lineHeight, setLineHeight] = useState(globalStyle.lineHeight);
  const [fontColor, setFontColor] = useState(globalStyle.fontColor);
  const [fontFamily, setFontFamily] = useState(globalStyle.fontFamily);
  const [strokeWidth, setStrokeWidth] = useState(globalStyle.strokeWidth);
  const [strokeColor, setStrokeColor] = useState(globalStyle.strokeColor);
  const [shadowColor, setShadowColor] = useState(globalStyle.shadowColor);
  const [shadowBlur, setShadowBlur] = useState(globalStyle.shadowBlur);
  const [shadowOffsetX, setShadowOffsetX] = useState(globalStyle.shadowOffsetX);
  const [shadowOffsetY, setShadowOffsetY] = useState(globalStyle.shadowOffsetY);
  const [backgroundColor, setBackgroundColor] = useState(
    globalStyle.backgroundColor || "transparent"
  );
  const [gradientColors, setGradientColors] = useState(
    globalStyle.gradientColors || ["#ffffff", "#000000"]
  );
  const [useGradient, setUseGradient] = useState(
    globalStyle.useGradient || false
  );
  const [positionX, setPositionX] = useState(globalStyle.positionX || 0);
  const [positionY, setPositionY] = useState(globalStyle.positionY || 0);

  // 同步全局样式变化 - 使用更细粒度的依赖监听和状态更新
  useEffect(() => {
    const globalStyle = store.getGlobalCaptionStyle();
    setFontSize(globalStyle.fontSize);
    setTextAlign(globalStyle.textAlign);
    setStyles(globalStyle.styles);
    setCharSpacing(globalStyle.charSpacing);
    setLineHeight(globalStyle.lineHeight);
    setFontColor(globalStyle.fontColor);
    setFontFamily(globalStyle.fontFamily);
    setGradientColors(globalStyle.gradientColors || ["#ffffff", "#000000"]);
    setUseGradient(globalStyle.useGradient || false);
    setStrokeWidth(globalStyle.strokeWidth);
    setStrokeColor(globalStyle.strokeColor);
    setShadowColor(globalStyle.shadowColor);
    setShadowBlur(globalStyle.shadowBlur);
    setShadowOffsetX(globalStyle.shadowOffsetX);
    setShadowOffsetY(globalStyle.shadowOffsetY);
    setBackgroundColor(globalStyle.backgroundColor || "transparent");
    setPositionX(globalStyle.positionX || 0);
    setPositionY(globalStyle.positionY || 0);
  }, [
    store.captionManager.globalCaptionStyle,
    // 添加具体的属性作为依赖，确保属性变化时能触发更新
    store.captionManager.globalCaptionStyle.fontSize,
    store.captionManager.globalCaptionStyle.textAlign,
    store.captionManager.globalCaptionStyle.styles,
    store.captionManager.globalCaptionStyle.charSpacing,
    store.captionManager.globalCaptionStyle.lineHeight,
    store.captionManager.globalCaptionStyle.fontColor,
    store.captionManager.globalCaptionStyle.fontFamily,
    store.captionManager.globalCaptionStyle.strokeWidth,
    store.captionManager.globalCaptionStyle.strokeColor,
    store.captionManager.globalCaptionStyle.shadowColor,
    store.captionManager.globalCaptionStyle.shadowBlur,
    store.captionManager.globalCaptionStyle.shadowOffsetX,
    store.captionManager.globalCaptionStyle.shadowOffsetY,
    store.captionManager.globalCaptionStyle.backgroundColor,
    store.captionManager.globalCaptionStyle.positionX,
    store.captionManager.globalCaptionStyle.positionY,
    store.captionManager.globalCaptionStyle.originX,
    store.captionManager.globalCaptionStyle.originY,
  ]);

  // 事件处理函数

  const handleFontFamilyChange = useCallback(
    (event: any) => {
      const newFontFamily = event.target.value;
      setFontFamily(newFontFamily);
      store.updateGlobalCaptionStyle({ fontFamily: newFontFamily });
    },
    [store]
  );

  const handleAlignmentChange = useCallback(
    (_: any, newAlignment: string) => {
      if (newAlignment !== null) {
        const alignmentValue = newAlignment as "left" | "center" | "right";
        setTextAlign(alignmentValue);
        store.updateGlobalCaptionStyle({
          textAlign: alignmentValue,
        });
      }
    },
    [store]
  );

  const handleStyleChange = useCallback(
    (_: any, newStyles: string[]) => {
      setStyles(newStyles);
      const fontWeight = newStyles.includes("bold") ? 700 : 400;
      store.updateGlobalCaptionStyle({
        styles: newStyles,
        fontWeight: fontWeight,
      });
    },
    [store]
  );

  const handleColorChange = useCallback(
    (color: string) => {
      if (useGradient) {
        const newGradientColors = [...gradientColors];
        newGradientColors[0] = color;
        setGradientColors(newGradientColors);
        store.updateGlobalCaptionStyle({
          gradientColors: newGradientColors,
          useGradient: true,
        });
      } else {
        setFontColor(color);
        store.updateGlobalCaptionStyle({
          fontColor: color,
          useGradient: false,
        });
      }
    },
    [useGradient, gradientColors, store]
  );

  const handleGradientChange = useCallback(
    (newGradientColors: string[]) => {
      setGradientColors(newGradientColors);
      setUseGradient(true);
      store.updateGlobalCaptionStyle({
        gradientColors: newGradientColors,
        useGradient: true,
      });
    },
    [store]
  );

  // 滑块配置 - 基础选项
  const sliderConfigs = useMemo(
    () => [
      {
        key: "fontSize",
        label: t("font_size"),
        value: fontSize,
        min: 10,
        max: 200,
        step: 1,
      },
      // {
      //   key: "charSpacing",
      //   label: t("char_spacing"),
      //   value: charSpacing,
      //   min: -50,
      //   max: 100,
      //   step: 1,
      // },
      // {
      //   key: "lineHeight",
      //   label: t("line_height"),
      //   value: lineHeight,
      //   min: 0.5,
      //   max: 3,
      //   step: 0.1,
      // },
    ],
    [fontSize, charSpacing, lineHeight, t]
  );

  // 滑块配置 - 高级选项
  const advancedSliderConfigs = useMemo(
    () => [
      {
        key: "strokeWidth",
        label: t("stroke_width"),
        value: strokeWidth,
        min: 0,
        max: 10,
        step: 0.1,
      },
      {
        key: "shadowBlur",
        label: t("shadow_blur"),
        value: shadowBlur,
        min: 0,
        max: 10,
        step: 1,
      },
      {
        key: "shadowOffsetX",
        label: t("shadow_offset_x"),
        value: shadowOffsetX,
        min: -50,
        max: 50,
        step: 1,
      },
      {
        key: "shadowOffsetY",
        label: t("shadow_offset_y"),
        value: shadowOffsetY,
        min: -20,
        max: 20,
        step: 1,
      },
    ],
    [strokeWidth, shadowBlur, shadowOffsetX, shadowOffsetY, t]
  );

  // 基础选项卡内容
  const basicTabContent = (
    <>
      {/* 基础设置 */}
      {/* <BaseSetting /> */}
      {/* 滑块控件 */}
      {sliderConfigs.map((config) => (
        <SliderWithInput
          key={config.key}
          label={config.label}
          value={config.value}
          onChange={(newValue) => {
            // 立即更新对应的本地状态
            if (config.key === "fontSize") setFontSize(newValue);
            else if (config.key === "charSpacing") setCharSpacing(newValue);
            else if (config.key === "lineHeight") setLineHeight(newValue);
          }}
          onChangeCommitted={(newValue) =>
            store.updateGlobalCaptionStyle({ [config.key]: newValue })
          }
          min={config.min}
          max={config.max}
          step={config.step}
        />
      ))}
      {/* 字体选择 */}
      <SelectRow
        label={t("font_family")}
        value={fontFamily}
        onChange={handleFontFamilyChange}
        options={fontFamilies.map((font) => ({
          value: font,
          label: font,
          preview: font,
        }))}
        sx={{ width: "50%" }}
      />

      {/* 文本对齐 */}
      <ToggleButtonRow
        label={t("text_align")}
        value={textAlign}
        onChange={handleAlignmentChange}
        buttons={[
          {
            value: "left",
            icon: <FormatAlignLeft fontSize="small" />,
            label: "left aligned",
          },
          {
            value: "center",
            icon: <FormatAlignCenter fontSize="small" />,
            label: "centered",
          },
          {
            value: "right",
            icon: <FormatAlignRight fontSize="small" />,
            label: "right aligned",
          },
        ]}
      />

      <Divider />

      {/* 文本样式 */}
      <ToggleButtonRow
        label={t("styles")}
        value={styles}
        onChange={handleStyleChange}
        exclusive={false}
        buttons={[
          {
            value: "bold",
            icon: <FormatBold fontSize="small" />,
            label: "bold",
          },
          {
            value: "italic",
            icon: <FormatItalic fontSize="small" />,
            label: "italic",
          },
          {
            value: "underlined",
            icon: <FormatUnderlined fontSize="small" />,
            label: "underlined",
          },
          {
            value: "strikethrough",
            icon: <StrikethroughS fontSize="small" />,
            label: "strikethrough",
          },
        ]}
      />

      {/* 字体颜色 */}
      <ColorRow
        label={t("font_color")}
        color={useGradient ? gradientColors : fontColor}
        onChange={(color) => {
          if (Array.isArray(color)) {
            // 渐变颜色
            handleGradientChange(color);
          } else {
            // 单色
            handleColorChange(color);
          }
        }}
        isGradient={useGradient}
      />

      {/* 背景颜色 */}
      <ColorRow
        label={t("background")}
        color={backgroundColor}
        onChange={(color) => {
          setBackgroundColor(color as string);
          store.updateGlobalCaptionStyle({ backgroundColor: color as string });
        }}
      />

      <Divider />
    </>
  );

  // 高级选项卡内容
  const advancedTabContent = (
    <>
      {/* 高级滑块控件 */}
      {advancedSliderConfigs.map((config) => (
        <SliderWithInput
          key={config.key}
          label={config.label}
          value={config.value}
          onChange={(newValue) => {
            // 立即更新对应的本地状态
            if (config.key === "strokeWidth") setStrokeWidth(newValue);
            else if (config.key === "shadowBlur") setShadowBlur(newValue);
            else if (config.key === "shadowOffsetX") setShadowOffsetX(newValue);
            else if (config.key === "shadowOffsetY") setShadowOffsetY(newValue);
          }}
          onChangeCommitted={(newValue) =>
            store.updateGlobalCaptionStyle({ [config.key]: newValue })
          }
          min={config.min}
          max={config.max}
          step={config.step}
        />
      ))}

      {/* 描边颜色 */}
      <ColorRow
        label={t("stroke_color")}
        color={strokeColor}
        onChange={(color) => {
          setStrokeColor(color as string);
          store.updateGlobalCaptionStyle({ strokeColor: color as string });
        }}
      />

      <Divider />

      {/* 阴影颜色 */}
      <ColorRow
        label={t("shadow_color")}
        color={shadowColor}
        onChange={(color) => {
          setShadowColor(color as string);
          store.updateGlobalCaptionStyle({ shadowColor: color as string });
        }}
      />
    </>
  );

  return (
    <ControlPanel
      title={t("subtitle")}
      tabs={[
        { label: t("basic"), content: basicTabContent },
        { label: t("advanced"), content: advancedTabContent },
      ]}
    />
  );
});

export default CaptionText;
