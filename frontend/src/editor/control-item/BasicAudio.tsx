import { Box, Typography } from "@mui/material";
import PlaybackSpeedControl from "./PlaybackSpeedControl";
import VolumeControl from "./VolumeControl";
import { EditorElement, AudioEditorElement } from "../../types";
import { observer } from "mobx-react";
import { StoreContext } from "../../store";
import React from "react";

const BasicAudio = observer(() => {
  const store = React.useContext(StoreContext);
  const element = store.selectedElement;
  if (!element || !element.properties) {
    return <></>;
  }
  // 检查是否为音频元素
  if (element.type !== "audio") {
    return <></>;
  }

  // 现在我们知道 element 是 AudioEditorElement 类型
  const audioElement = element as AudioEditorElement;

  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          Audio
        </Typography>
      </Box>
      <Box sx={{ px: 1 }}>
        <VolumeControl element={audioElement} />
      </Box>
      <Box sx={{ px: 1 }}>
        <PlaybackSpeedControl element={audioElement} />
      </Box>
    </Box>
  );
});

export default BasicAudio;
