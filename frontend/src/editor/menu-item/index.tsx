export { MenuItem } from "./MenuItems";
export { Uploads } from "./Uploads";

export { Texts } from "./Texts";
export { Audios } from "./Audios";
export { Images } from "./Images";
export { Videos } from "./Videos";
export { default as Captions } from "./Captions";
export { Shapes } from "./Shapes";

// i18n integration notes:
// 1. Import useLanguage hook: import { useLanguage } from "../../i18n/LanguageContext";
// 2. Add const { t } = useLanguage(); in your component
// 3. Replace static text with t("key") where "key" is the translation key
