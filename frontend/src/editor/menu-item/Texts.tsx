"use client";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  <PERSON><PERSON>r,
  Stack,
  TextField,
  InputAdornment,
  IconButton,
  useTheme,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { observer } from "mobx-react";
import React, { useContext } from "react";
import { StoreContext } from "../../store";
import { useLanguage } from "../../i18n/LanguageContext";
import { getTextResources } from "./textTemplates";
import { useElementClickHandler } from "../../hooks/useElementClickHandler";

export const Texts = observer(() => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();
  const theme = useTheme();
  const TEXT_RESOURCES = getTextResources(t);
  const [searchTerm, setSearchTerm] = React.useState("");

  // 使用统一的点击处理Hook
  const { handleElementAdd } = useElementClickHandler(store);

  // 过滤和分组文字样式
  const groupedResources = React.useMemo(() => {
    const filteredResources = TEXT_RESOURCES.filter(
      (resource) =>
        resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const groups: { [key: string]: typeof TEXT_RESOURCES } = {};
    filteredResources.forEach((resource) => {
      if (!groups[resource.category]) {
        groups[resource.category] = [];
      }
      groups[resource.category].push(resource);
    });
    return groups;
  }, [TEXT_RESOURCES, searchTerm]);

  // 添加文字的处理函数
  const handleAddText = (resource: (typeof TEXT_RESOURCES)[0]) => {
    // 使用统一的处理逻辑
    const textData = {
      text: resource.name,
      fontSize: resource.fontSize,
      fontWeight: resource.fontWeight,
      fontFamily: resource.fontFamily,
      fontColor: resource.color,
      shadowBlur: resource.shadowBlur,
      shadowOffsetX: resource.shadowOffsetX,
      shadowOffsetY: resource.shadowOffsetY,
      shadowColor: resource.shadowColor,
      strokeWidth: resource.strokeWidth,
      strokeColor: resource.strokeColor,
      backgroundColor: resource.backgroundColor,
      textAlign: resource.textAlign,
      lineHeight: resource.lineHeight,
      charSpacing: resource.charSpacing,
    };

    handleElementAdd({
      type: "text",
      data: textData,
    });
  };

  // 渲染文字样式按钮
  const renderTextButton = (resource: (typeof TEXT_RESOURCES)[0]) => {
    const isLightColor = (color: string) => {
      // 检查是否为浅色，这些颜色在深色背景上需要阴影以提高可读性
      const lightColors = [
        "#ffffff",
        "#00ffff",
        "#ff9ff3",
        "#ffff00",
        "#ffffff",
      ];
      return (
        lightColors.includes(color.toLowerCase()) ||
        color.toLowerCase().includes("white") ||
        color.toLowerCase().includes("light")
      );
    };

    return (
      <Button
        draggable
        onDragStart={(e) => {
          const dragData = {
            type: "text",
            textData: {
              text: resource.name,
              fontSize: resource.fontSize,
              fontWeight: resource.fontWeight,
              fontFamily: resource.fontFamily,
              fontColor: resource.color,
              shadowBlur: resource.shadowBlur,
              shadowOffsetX: resource.shadowOffsetX,
              shadowOffsetY: resource.shadowOffsetY,
              shadowColor: resource.shadowColor,
              strokeWidth: resource.strokeWidth,
              strokeColor: resource.strokeColor,
              backgroundColor: resource.backgroundColor,
              textAlign: resource.textAlign,
              lineHeight: resource.lineHeight,
              charSpacing: resource.charSpacing,
            },
            metadata: {
              name: resource.name,
              category: resource.category,
              description: resource.description,
            },
          };
          e.dataTransfer.setData("application/json", JSON.stringify(dragData));
          e.dataTransfer.effectAllowed = "copy";
        }}
        key={resource.name}
        onClick={() => handleAddText(resource)}
        variant="outlined"
        fullWidth
        sx={{
          textTransform: "none",
          minHeight: 60,
          p: 1,
          border: "1px solid",
          borderColor: "divider",
          background: "grey.100",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: 2,
          overflow: "hidden",
          cursor: "grab",
          "&:active": {
            cursor: "grabbing",
          },
          "&:hover": {
            transform: "translateY(-2px)",
            boxShadow: (theme) =>
              theme.palette.mode === "dark"
                ? "0 4px 12px rgba(255,255,255,0.1)"
                : "0 4px 12px rgba(0,0,0,0.1)",
            borderColor: "primary.main",
            background: "background.paper",
          },
          transition: "all 0.2s ease-in-out",
        }}
      >
        <Typography
          sx={{
            // 使用固定的预览字体大小，确保所有字体都能清晰预览
            fontSize: 15,
            fontWeight: resource.fontWeight,
            fontFamily: resource.fontFamily,
            color: resource.color,
            // 使用适中的行高，确保预览效果
            lineHeight: 1.2,
            // 使用资源中定义的文本对齐方式
            textAlign: resource.textAlign,
            width: "100%",
            // 使用适中的字符间距，确保可读性
            letterSpacing:
              resource.charSpacing > 0
                ? `${Math.min(resource.charSpacing * 0.3, 2)}px`
                : "0px",
            // 应用适中的阴影效果，适配16px字体
            textShadow:
              resource.shadowOffsetX !== 0 || resource.shadowOffsetY !== 0
                ? `${Math.min(
                    Math.abs(resource.shadowOffsetX) * 0.15,
                    1
                  )}px ${Math.min(
                    Math.abs(resource.shadowOffsetY) * 0.15,
                    1
                  )}px 0px ${resource.shadowColor}`
                : isLightColor(resource.color)
                ? `0.5px 0.5px 0px ${theme.palette.text.primary}`
                : "none",
            // 描边效果适配16px字体
            WebkitTextStroke:
              resource.strokeWidth > 0
                ? `${Math.min(resource.strokeWidth * 0.15, 0.8)}px ${
                    resource.strokeColor
                  }`
                : "none",
            // 背景色效果，适配16px字体预览
            backgroundColor: resource.backgroundColor || "transparent",
            padding: resource.backgroundColor ? "2px 6px" : "0",
            borderRadius: resource.backgroundColor ? "3px" : "0",
            // 确保文字在容器中正确显示
            display: "block",
            overflow: "hidden",
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            // 改善文字渲染质量
            WebkitFontSmoothing: "antialiased",
            MozOsxFontSmoothing: "grayscale",
          }}
        >
          {resource.name}
        </Typography>
      </Button>
    );
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "grey.100",
        borderRadius: 1,
        boxShadow: 1,
      }}
    >
      {/* 标题栏 */}
      <Box
        sx={{
          minHeight: 48,
          display: "flex",
          flexDirection: "column",
          px: 2,
          py: 1.5,
          flexShrink: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography
          variant="h6"
          sx={{
            mb: 1,
            fontWeight: 700,
            fontSize: "1.1rem",
            color: "text.primary",
            letterSpacing: "0.5px",
          }}
        >
          {t("Text")}
        </Typography>

        {/* 搜索框 */}
        {/* <TextField
          size="small"
          placeholder={t("text_search_placeholder")}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => setSearchTerm("")}
                  edge="end"
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              bgcolor: "background.paper",
              fontSize: "0.9rem",
              fontWeight: 400,
              "& fieldset": {
                borderColor: "divider",
                borderRadius: "8px",
              },
              "&:hover fieldset": {
                borderColor: "primary.main",
              },
              "&.Mui-focused fieldset": {
                borderColor: "primary.main",
                borderWidth: "2px",
              },
            },
            "& .MuiInputBase-input": {
              fontSize: "0.9rem",
              fontWeight: 400,
              color: "text.primary",
            },
            "& .MuiInputBase-input::placeholder": {
              fontSize: "0.85rem",
              color: "text.secondary",
              opacity: 0.7,
            },
          }}
        /> */}
      </Box>
      {/* 内容区域 */}
      <Box
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          p: 2,
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            bgcolor: "grey.100",
          },
          "&::-webkit-scrollbar-thumb": {
            bgcolor: "grey.400",
            borderRadius: "4px",
          },
        }}
      >
        {Object.keys(groupedResources).length === 0 ? (
          // 空状态
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              py: 6,
              textAlign: "center",
            }}
          >
            <SearchIcon sx={{ fontSize: 48, color: "text.disabled", mb: 2 }} />
            <Typography
              variant="h6"
              sx={{
                color: "text.secondary",
                fontWeight: 600,
                fontSize: "1.1rem",
                mb: 1,
              }}
            >
              {t("text_no_results")}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "text.disabled",
                fontSize: "0.9rem",
                fontWeight: 400,
                lineHeight: 1.5,
              }}
            >
              {t("text_try_different_keywords")}
            </Typography>
          </Box>
        ) : (
          <Stack spacing={3}>
            {Object.entries(groupedResources).map(([category, resources]) => (
              <Box key={category}>
                {/* 类别标题 */}
                <Box sx={{ mb: 2 }}>
                  <Typography
                    variant="subtitle2"
                    sx={{ color: "text.secondary", fontWeight: 600 }}
                  >
                    {category === "title" && t("text_category_title")}
                    {category === "subtitle" && t("text_category_subtitle")}
                    {category === "body" && t("text_category_body")}
                    {category === "annotation" && t("text_category_annotation")}
                    {category === "social" && t("text_category_social")}
                    {category === "creative" && t("text_category_creative")}
                    {category === "effects" && t("text_category_effects")}
                  </Typography>
                </Box>

                {/* 文字样式网格 */}
                <Grid container spacing={1}>
                  {resources.map((resource) => (
                    <Grid
                      key={resource.name}
                      size={{
                        xs: 12,
                        sm: 6,
                      }}
                    >
                      {renderTextButton(resource)}
                    </Grid>
                  ))}
                </Grid>
              </Box>
            ))}
          </Stack>
        )}
      </Box>
    </Box>
  );
});
