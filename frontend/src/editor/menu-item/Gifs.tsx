import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useContext,
  useRef,
  memo,
} from "react";
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  Alert,
  CircularProgress,
  ImageList,
  ImageListItem,
  IconButton,
  Tooltip,
  Button,
  Stack,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Search as SearchIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from "@mui/icons-material";
import { observer } from "mobx-react";
import { StoreContext } from "../../store";
import { useLanguage } from "../../i18n/LanguageContext";
import { getUid } from "../../utils";
import { useElementClickHandler } from "../../hooks/useElementClickHandler";
import {
  searchContent,
  getTrendingContent,
  getContentCategories,
  getContentByCategory,
  GifFile,
  ContentType,
} from "../../services/giphyService";

// 加载状态接口
interface LoadingState {
  [key: string]: boolean;
}

// GIF批次接口
interface GifBatch {
  id: number;
  gifs: GifFile[];
}

// GIF项目组件属性
interface GifItemProps {
  gif: GifFile;
  onGifAdd: (gif: GifFile, index: number) => void;
  index: number;
  isLoading: boolean;
  t: (key: string) => string;
}

// 常量定义
const SKELETON_COUNT = 6;
const PER_PAGE = 20;

// 优化的 GifItem 组件
const GifItem = memo(({ gif, onGifAdd, index, isLoading, t }: GifItemProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // 优化点击处理
  const handleClick = useCallback(() => {
    if (!isLoading) {
      onGifAdd(gif, index);
    }
  }, [isLoading, onGifAdd, gif, index]);

  // 优化加载完成处理
  const handleLoadedData = useCallback(() => {
    setIsLoaded(true);
  }, []);

  // 处理拖拽开始
  const handleDragStart = (e: React.DragEvent) => {
    // 设置拖拽数据
    const dragData = {
      type: "gif",
      src: gif.url,
      gifId: gif.id,
      metadata: {
        name: gif.title || `GIF ${index + 1}`,
        width: gif.width,
        height: gif.height,
        isAnimated: true,
      },
    };

    e.dataTransfer.setData("application/json", JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = "copy";

    // 创建拖拽预览图像
    const dragImage = new Image();
    dragImage.src = gif.previewUrl;
    dragImage.onload = () => {
      // 设置拖拽图像
      e.dataTransfer.setDragImage(dragImage, 50, 50);
    };
  };

  return (
    <ImageListItem
      draggable
      onDragStart={handleDragStart}
      onClick={handleClick}
      sx={{
        cursor: isLoading ? "not-allowed" : "pointer",
        position: "relative",
        borderRadius: 2,
        overflow: "hidden",
        transition: "all 0.3s ease",
        "&:hover": {
          transform: "translateY(-4px)",
          boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        },
        "&:active": {
          cursor: "grabbing",
        },
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* GIF预览 */}
      <img
        src={gif.previewUrl}
        alt={gif.title}
        loading="lazy"
        onLoad={handleLoadedData}
        style={{
          width: "100%",
          height: "auto",
          display: "block",
          opacity: isLoaded && !isLoading ? 1 : 0.5,
          transition: "opacity 0.3s ease-in-out",
          borderRadius: 8,
          pointerEvents: "none", // 防止图片本身阻止拖拽事件
        }}
      />

      {/* 加载指示器 */}
      {(!isLoaded || isLoading) && (
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            zIndex: 1,
          }}
        >
          <CircularProgress size={24} />
        </Box>
      )}

      {/* 悬停时显示的信息 */}
      {isHovered && isLoaded && !isLoading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            p: 1,
            borderRadius: 2,
          }}
        >
          {/* 顶部信息 */}
          <Box>
            {gif.username && (
              <Typography
                variant="caption"
                sx={{
                  color: "white",
                  backgroundColor: "rgba(0, 0, 0, 0.7)",
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: "0.7rem",
                }}
              >
                @{gif.username}
              </Typography>
            )}
            {/* 显示内容类型标签 */}
            {gif.type && (
              <Typography
                variant="caption"
                sx={{
                  color: "white",
                  backgroundColor:
                    gif.type === "clips"
                      ? "rgba(255, 0, 0, 0.7)"
                      : gif.type === "stickers"
                      ? "rgba(0, 255, 0, 0.7)"
                      : "rgba(0, 0, 255, 0.7)",
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: "0.6rem",
                  ml: 1,
                  textTransform: "uppercase",
                }}
              >
                {gif.type}
              </Typography>
            )}
            {/* 如果是 clip，显示时长 */}
            {gif.duration && (
              <Typography
                variant="caption"
                sx={{
                  color: "white",
                  backgroundColor: "rgba(255, 165, 0, 0.7)",
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: "0.6rem",
                  ml: 1,
                }}
              >
                {parseFloat(gif.duration).toFixed(1)}s
              </Typography>
            )}
          </Box>

          {/* 底部尺寸信息 */}
          <Box sx={{ textAlign: "center" }}>
            <Typography
              variant="caption"
              sx={{
                color: "white",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                px: 1,
                py: 0.5,
                borderRadius: 1,
                fontSize: "0.7rem",
              }}
            >
              {gif.width} × {gif.height}
            </Typography>
          </Box>
        </Box>
      )}
    </ImageListItem>
  );
});

// 骨架屏组件
const SkeletonList = () => {
  // 创建不同高度的skeleton数组，更像真实瀑布流
  const skeletonHeights = [180, 220, 260, 300, 340, 380];

  return (
    <ImageList variant="masonry" cols={2} gap={16} sx={{ margin: 0 }}>
      {Array.from({ length: SKELETON_COUNT }).map((_, index) => (
        <ImageListItem key={`skeleton-${index}`}>
          <Box
            sx={{
              width: "100%",
              height: skeletonHeights[index % skeletonHeights.length],
              backgroundColor: "grey.300",
              borderRadius: 2,
              animation: "pulse 1.5s ease-in-out infinite",
              "@keyframes pulse": {
                "0%": { opacity: 1 },
                "50%": { opacity: 0.5 },
                "100%": { opacity: 1 },
              },
            }}
          />
        </ImageListItem>
      ))}
    </ImageList>
  );
};

export const Gifs = observer(() => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();

  // 使用统一的点击处理Hook
  const { handleElementAdd } = useElementClickHandler(store);

  // 状态管理
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingStates, setLoadingStates] = useState<LoadingState>({});
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // 内容类型状态
  const [contentType, setContentType] = useState<ContentType>("gifs");

  // 为每个内容类型维护独立的数据状态
  const [contentData, setContentData] = useState<{
    [key in ContentType]: {
      initialGifs: GifFile[];
      gifBatches: GifBatch[];
      batchCounter: number;
      hasNextPage: boolean;
      page: number;
      totalGifs: number;
    };
  }>({
    gifs: {
      initialGifs: [],
      gifBatches: [],
      batchCounter: 0,
      hasNextPage: true,
      page: 1,
      totalGifs: 0,
    },
    stickers: {
      initialGifs: [],
      gifBatches: [],
      batchCounter: 0,
      hasNextPage: true,
      page: 1,
      totalGifs: 0,
    },
    clips: {
      initialGifs: [],
      gifBatches: [],
      batchCounter: 0,
      hasNextPage: true,
      page: 1,
      totalGifs: 0,
    },
  });

  const [batchLoading, setBatchLoading] = useState<number | null>(null);

  // 获取当前内容类型的数据
  const currentData = contentData[contentType];

  // 分类标签和滚动相关
  const categories = useMemo(
    () => getContentCategories(contentType),
    [contentType]
  );
  const tagsContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // 滚动容器引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 防抖搜索
  const [debouncedQuery, setDebouncedQuery] = useState("");
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // 检查标签容器滚动状态
  const checkScroll = useCallback(() => {
    const container = tagsContainerRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  }, []);

  // 标签容器滚动处理
  const handleScrollLeft = () => {
    const container = tagsContainerRef.current;
    if (container) {
      container.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  const handleScrollRight = () => {
    const container = tagsContainerRef.current;
    if (container) {
      container.scrollBy({ left: 200, behavior: "smooth" });
    }
  };

  // 监听标签容器滚动
  useEffect(() => {
    const container = tagsContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener("scroll", checkScroll);
      window.addEventListener("resize", checkScroll);
      return () => {
        container.removeEventListener("scroll", checkScroll);
        window.removeEventListener("resize", checkScroll);
      };
    }
  }, [checkScroll]);

  // 添加GIF到画布
  const onGifAdd = useCallback(
    async (gif: GifFile, index: number) => {
      if (!store) return;

      const loadingKey = `gif-${index}`;
      setLoadingStates((prev) => ({ ...prev, [loadingKey]: true }));

      try {
        // 准备GIF元数据
        const gifMetadata = {
          name:
            gif.title ||
            `${gif.type?.toUpperCase() || "GIF"} ${
              store.editorElements.length + 1
            }`,
          width: gif.width,
          height: gif.height,
          isAnimated: true,
        };

        // 使用统一的处理逻辑
        const result = await handleElementAdd(
          {
            type: "gif",
            src: gif.url,
            metadata: gifMetadata,
            crossOrigin: true,
            timeout: 30000,
          },
          loadingKey
        );

        if (!result.success) {
          setErrorMessage(result.error || t("add_gif_error"));
        }
      } catch (error) {
        console.error("添加GIF失败:", error);
        setErrorMessage(t("add_gif_error"));
      } finally {
        setLoadingStates((prev) => ({ ...prev, [loadingKey]: false }));
      }
    },
    [store, t, handleElementAdd]
  );

  // 更新特定内容类型的数据
  const updateContentData = useCallback(
    (type: ContentType, updates: Partial<typeof currentData>) => {
      setContentData((prev) => ({
        ...prev,
        [type]: {
          ...prev[type],
          ...updates,
        },
      }));
    },
    [] // 移除不必要的依赖
  );

  // 加载状态引用，避免依赖循环
  const loadingRef = useRef(false);

  // 初始加载标志
  const initialLoadRef = useRef<{ [key in ContentType]: boolean }>({
    gifs: false,
    stickers: false,
    clips: false,
  });

  // 搜索防抖引用，避免频繁搜索
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 滚动加载更多
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      if (
        scrollPercentage > 0.8 &&
        currentData.hasNextPage &&
        !loadingRef.current &&
        !batchLoading
      ) {
        const query = debouncedQuery || selectedCategory;

        // 直接在这里实现分页加载逻辑，避免依赖循环
        const performPaginationLoad = async () => {
          if (loadingRef.current) return;

          loadingRef.current = true;
          setLoading(true);
          setBatchLoading((prev) => (prev || 0) + 1);

          try {
            let response;
            if (query.trim()) {
              response = await searchContent(
                query,
                contentType,
                currentData.page,
                PER_PAGE
              );
            } else {
              response = await getTrendingContent(
                contentType,
                currentData.page,
                PER_PAGE
              );
            }

            // 创建新的批次并添加到批次列表
            const newBatchId = currentData.batchCounter + 1;
            const newBatch: GifBatch = {
              id: newBatchId,
              gifs: response.gifs,
            };

            setContentData((prevContentData) => ({
              ...prevContentData,
              [contentType]: {
                ...prevContentData[contentType],
                gifBatches: [
                  ...prevContentData[contentType].gifBatches,
                  newBatch,
                ],
                batchCounter: newBatchId,
                hasNextPage: response.hasMore,
                page: prevContentData[contentType].page + 1,
              },
            }));
          } catch (error) {
            console.error(`分页加载${contentType}失败:`, error);
            setErrorMessage(t("load_gifs_error"));
          } finally {
            loadingRef.current = false;
            setLoading(false);
            setBatchLoading(null);
          }
        };

        performPaginationLoad();
      }
    },
    [
      currentData.hasNextPage,
      currentData.page,
      currentData.batchCounter,
      batchLoading,
      debouncedQuery,
      selectedCategory,
      contentType,
      t,
    ]
  );

  // 处理内容类型切换
  const handleContentTypeChange = useCallback(
    (newType: ContentType) => {
      setContentType(newType);
      setSearchQuery("");
      setSelectedCategory("");

      // 如果新类型还没有初始化，则加载数据
      if (!initialLoadRef.current[newType]) {
        initialLoadRef.current[newType] = true;

        // 直接在这里实现加载逻辑，避免依赖循环
        setTimeout(async () => {
          if (loadingRef.current) return;

          loadingRef.current = true;
          setLoading(true);
          setErrorMessage(null);

          try {
            const response = await getTrendingContent(newType, 1, PER_PAGE);

            setContentData((prevContentData) => ({
              ...prevContentData,
              [newType]: {
                ...prevContentData[newType],
                initialGifs: response.gifs,
                gifBatches: [],
                batchCounter: 0,
                totalGifs: response.total || 0,
                hasNextPage: response.hasMore,
                page: 2,
              },
            }));

            if (response.gifs.length === 0) {
              setErrorMessage(t("no_gifs_found"));
            }
          } catch (error) {
            console.error(`切换加载${newType}失败:`, error);
            setErrorMessage(t("load_gifs_error"));
          } finally {
            loadingRef.current = false;
            setLoading(false);
          }
        }, 0);
      }
    },
    [t] // 只保留必要的依赖
  );

  // 初始加载 - 使用稳定的依赖项
  useEffect(() => {
    if (!initialLoadRef.current[contentType]) {
      initialLoadRef.current[contentType] = true;

      // 在 useEffect 内部定义 loadContent 来避免循环依赖
      const performInitialLoad = async () => {
        if (loadingRef.current) return;

        loadingRef.current = true;
        setLoading(true);
        setErrorMessage(null);

        try {
          const response = await getTrendingContent(contentType, 1, PER_PAGE);

          setContentData((prevContentData) => ({
            ...prevContentData,
            [contentType]: {
              ...prevContentData[contentType],
              initialGifs: response.gifs,
              gifBatches: [],
              batchCounter: 0,
              totalGifs: response.total || 0,
              hasNextPage: response.hasMore,
              page: 2,
            },
          }));

          if (response.gifs.length === 0) {
            setErrorMessage(t("no_gifs_found"));
          }
        } catch (error) {
          console.error(`初始加载${contentType}失败:`, error);
          setErrorMessage(t("load_gifs_error"));
        } finally {
          loadingRef.current = false;
          setLoading(false);
        }
      };

      const timeoutId = setTimeout(performInitialLoad, 0);
      return () => clearTimeout(timeoutId);
    }
  }, [contentType, t]);

  // 搜索效果 - 使用稳定的依赖项
  useEffect(() => {
    // 跳过初始加载
    if (!initialLoadRef.current[contentType]) return;

    // 清除之前的搜索定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的搜索定时器
    searchTimeoutRef.current = setTimeout(async () => {
      if (loadingRef.current) return;

      const searchTerm = debouncedQuery || selectedCategory;

      loadingRef.current = true;
      setLoading(true);
      setErrorMessage(null);

      try {
        let response;
        if (searchTerm.trim()) {
          response = await searchContent(searchTerm, contentType, 1, PER_PAGE);
        } else {
          response = await getTrendingContent(contentType, 1, PER_PAGE);
        }

        setContentData((prevContentData) => ({
          ...prevContentData,
          [contentType]: {
            ...prevContentData[contentType],
            initialGifs: response.gifs,
            gifBatches: [],
            batchCounter: 0,
            totalGifs: response.total || 0,
            hasNextPage: response.hasMore,
            page: 2,
          },
        }));

        if (response.gifs.length === 0) {
          setErrorMessage(t("no_gifs_found"));
        }
      } catch (error) {
        console.error(`搜索${contentType}失败:`, error);
        setErrorMessage(t("load_gifs_error"));
      } finally {
        loadingRef.current = false;
        setLoading(false);
      }
    }, 100);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [debouncedQuery, selectedCategory, contentType, t]);

  // 获取当前分类标签
  const getCurrentCategoryTags = () => {
    return Object.values(categories).flat().slice(0, 20); // 限制标签数量
  };

  // 判断是否有内容
  const hasNoContent =
    !loading &&
    currentData.initialGifs.length === 0 &&
    currentData.gifBatches.length === 0;

  // 计算当前显示的内容总数
  const getCurrentDisplayedCount = () => {
    return (
      currentData.initialGifs.length +
      currentData.gifBatches.reduce((sum, batch) => sum + batch.gifs.length, 0)
    );
  };

  // 渲染分隔标题的辅助函数
  const renderBatchTitle = (batchId: number) => (
    <Box
      sx={{
        height: "1px",
        bgcolor: "divider",
        my: 3,
        position: "relative",
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: -10,
          left: "50%",
          transform: "translateX(-50%)",
          bgcolor: "grey.100",
          px: 2,
        }}
      ></Box>
    </Box>
  );

  // 获取内容类型的显示名称
  const getContentTypeDisplayName = (type: ContentType) => {
    switch (type) {
      case "gifs":
        return "GIFs";
      case "stickers":
        return "Stickers";
      case "clips":
        return "Clips";
      default:
        return "GIFs";
    }
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      }}
    >
      {/* 标题栏 */}
      <Box
        sx={{
          bgcolor: "grey.100",
          height: 56,
          display: "flex",
          alignItems: "center",
          px: 3,
          flexShrink: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
          {getContentTypeDisplayName(contentType)} {t("library") || "库"}
        </Typography>
      </Box>

      {/* 搜索框、Tab导航和标签区域 */}
      <Box sx={{ p: 2, bgcolor: "grey.100" }}>
        {/* 搜索框 */}
        <TextField
          fullWidth
          size="small"
          placeholder={`${t("search")} ${getContentTypeDisplayName(
            contentType
          )}`}
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setSelectedCategory("");
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              bgcolor: "background.paper",
              "&:hover": {
                "& > fieldset": { borderColor: "primary.main" },
              },
            },
          }}
        />

        {/* Tab导航 */}
        <Box sx={{ mt: 2, mb: 2 }}>
          <Tabs
            value={contentType}
            onChange={(_, newValue) => handleContentTypeChange(newValue)}
            variant="fullWidth"
            indicatorColor="primary"
            textColor="primary"
            sx={{
              bgcolor: "background.paper",
              borderRadius: 1,
              "& .MuiTab-root": {
                minHeight: 36,
                textTransform: "none",
                fontSize: "0.875rem",
                fontWeight: 500,
              },
              "& .MuiTabs-indicator": {
                backgroundColor: "primary.main",
              },
            }}
          >
            <Tab
              label="GIFs"
              value="gifs"
              sx={{
                color:
                  contentType === "gifs" ? "primary.main" : "text.secondary",
              }}
            />
            <Tab
              label="Stickers"
              value="stickers"
              sx={{
                color:
                  contentType === "stickers"
                    ? "primary.main"
                    : "text.secondary",
              }}
            />
            <Tab
              label="Clips"
              value="clips"
              sx={{
                color:
                  contentType === "clips" ? "primary.main" : "text.secondary",
              }}
            />
          </Tabs>
        </Box>

        {/* 分类标签 */}
        <Box
          sx={{
            width: 250,
            position: "relative",
            "&:hover .scroll-button": {
              display: "flex",
            },
          }}
        >
          <Box
            sx={{
              display: "flex",
              overflowX: "auto",
              scrollBehavior: "smooth",
              "&::-webkit-scrollbar": { display: "none" },
              msOverflowStyle: "none",
              scrollbarWidth: "none",
              px: 1,
              borderRadius: 2,
            }}
            ref={tagsContainerRef}
          >
            <Stack direction="row" spacing={1}>
              {getCurrentCategoryTags().map((tag) => (
                <Chip
                  key={tag}
                  label={tag}
                  onClick={() => {
                    if (selectedCategory === tag) {
                      setSelectedCategory("");
                    } else {
                      setSelectedCategory(tag);
                      setSearchQuery("");
                    }
                  }}
                  color={selectedCategory === tag ? "primary" : "default"}
                  sx={{ whiteSpace: "nowrap" }}
                />
              ))}
            </Stack>
          </Box>

          <IconButton
            className="scroll-button"
            sx={{
              position: "absolute",
              left: 0,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: "background.paper",
              opacity: 0.7,
              "&:hover": { bgcolor: "background.paper", opacity: 1 },
              display: "none",
            }}
            size="small"
            onClick={handleScrollLeft}
          >
            <ChevronLeftIcon fontSize="small" />
          </IconButton>

          <IconButton
            className="scroll-button"
            sx={{
              position: "absolute",
              right: 0,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: "background.paper",
              opacity: 0.7,
              "&:hover": { bgcolor: "background.paper", opacity: 1 },
              display: "none",
            }}
            size="small"
            onClick={handleScrollRight}
          >
            <ChevronRightIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      {/* 错误提示 */}
      {errorMessage && !loading && (
        <Alert severity="warning" sx={{ m: 1, mb: 0 }}>
          {errorMessage}
          <Button
            size="small"
            onClick={async () => {
              setErrorMessage(null);

              // 内联重试逻辑，避免依赖 loadContent
              if (loadingRef.current) return;

              loadingRef.current = true;
              setLoading(true);

              try {
                const query = debouncedQuery || selectedCategory;
                let response;

                if (query.trim()) {
                  response = await searchContent(
                    query,
                    contentType,
                    1,
                    PER_PAGE
                  );
                } else {
                  response = await getTrendingContent(contentType, 1, PER_PAGE);
                }

                setContentData((prevContentData) => ({
                  ...prevContentData,
                  [contentType]: {
                    ...prevContentData[contentType],
                    initialGifs: response.gifs,
                    gifBatches: [],
                    batchCounter: 0,
                    totalGifs: response.total || 0,
                    hasNextPage: response.hasMore,
                    page: 2,
                  },
                }));

                if (response.gifs.length === 0) {
                  setErrorMessage(t("no_gifs_found"));
                }
              } catch (error) {
                console.error(`重试加载${contentType}失败:`, error);
                setErrorMessage(t("load_gifs_error"));
              } finally {
                loadingRef.current = false;
                setLoading(false);
              }
            }}
            sx={{ ml: 1 }}
          >
            {t("retry")}
          </Button>
        </Alert>
      )}

      {/* 内容列表区域 */}
      <Box
        ref={scrollContainerRef}
        onScroll={handleScroll}
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          px: 2,
          "&::-webkit-scrollbar": {
            width: "5px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            borderRadius: "5px",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.3)",
            },
          },
        }}
      >
        {/* 无结果提示 */}
        {hasNoContent && !loading && !errorMessage && (
          <Alert severity="info" sx={{ mb: 2, mt: 2 }}>
            {t("no_gifs_found")}
          </Alert>
        )}

        {/* 初始加载的骨架屏 */}
        {loading && currentData.page === 1 && <SkeletonList />}

        {/* 初始内容列表 */}
        {currentData.initialGifs.length > 0 && (
          <ImageList
            variant="masonry"
            cols={2}
            gap={16}
            sx={{ margin: 0, mt: 2 }}
          >
            {currentData.initialGifs.map((gif, index) => (
              <GifItem
                key={gif.id}
                gif={gif}
                onGifAdd={onGifAdd}
                index={index}
                isLoading={loadingStates[`gif-${index}`] || false}
                t={t}
              />
            ))}
          </ImageList>
        )}

        {/* 批次内容列表 */}
        {currentData.gifBatches.map((batch) => (
          <Box key={`batch-${batch.id}`}>
            {renderBatchTitle(batch.id)}
            <ImageList variant="masonry" cols={2} gap={16} sx={{ margin: 0 }}>
              {batch.gifs.map((gif, index) => {
                const gifIndex =
                  index +
                  currentData.initialGifs.length +
                  (batch.id - 1) * PER_PAGE;
                return (
                  <GifItem
                    key={`${gif.id}-batch-${batch.id}`}
                    gif={gif}
                    onGifAdd={onGifAdd}
                    index={gifIndex}
                    isLoading={loadingStates[`gif-${gifIndex}`] || false}
                    t={t}
                  />
                );
              })}
            </ImageList>
          </Box>
        ))}

        {/* 显示当前正在加载的批次骨架屏 */}
        {batchLoading !== null && (
          <>
            {renderBatchTitle(batchLoading)}
            <SkeletonList />
          </>
        )}

        {/* 结束指示器（当没有更多加载时） */}
        {!currentData.hasNextPage &&
          !loading &&
          currentData.initialGifs.length > 0 && (
            <Box
              sx={{
                p: 2,
                textAlign: "center",
                borderTop: "1px solid",
                borderColor: "divider",
                mt: 2,
              }}
            >
              <Typography variant="caption" color="text.secondary">
                {t("displayed_results")
                  ? t("displayed_results")
                      .replace("{0}", getCurrentDisplayedCount().toString())
                      .replace(
                        "{1}",
                        currentData.totalGifs
                          ? currentData.totalGifs.toString()
                          : t("unknown")
                      )
                  : `已显示 ${getCurrentDisplayedCount()} 个结果`}
              </Typography>
            </Box>
          )}
      </Box>
    </Box>
  );
});

export default Gifs;
