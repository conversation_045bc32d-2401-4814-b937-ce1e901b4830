"use client";
import React from "react";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import { Element } from "../entity/Element";
import {
  Box,
  Typography,
  List,
  ListItem,
  IconButton,
  Paper,
} from "@mui/material";
import {
  ArrowUpward,
  ArrowDownward,
  VerticalAlignTop,
  VerticalAlignBottom,
} from "@mui/icons-material";
import { useLanguage } from "../../i18n/LanguageContext";

export const ElementsPanel = observer((_props: {}) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [selectedElement, setSelectedElement] = React.useState<any | null>(
    null
  );

  const moveElement = (
    element: any,
    direction: "up" | "down" | "top" | "bottom"
  ) => {
    store.moveElement(element, direction);
  };

  const isLastElement =
    selectedElement && store.editorElements.indexOf(selectedElement) === 0;
  const isFirstElement =
    selectedElement &&
    store.editorElements.indexOf(selectedElement) ===
      store.editorElements.length - 1;

  return (
    <Box
      sx={{
        height: "100%",
        position: "relative",
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
        my: 2,
      }}
    >
      <List>
        {store.editorElements.map((element) => (
          <ListItem
            key={element.id}
            disablePadding
            onClick={() => setSelectedElement(element)}
          >
            <Element element={element} />
          </ListItem>
        ))}
      </List>

      {selectedElement && (
        <Paper
          sx={{
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            p: 1,
            bgcolor: "background.paper",
            display: "flex",
            justifyContent: "space-around",
            borderTop: 1,
            borderColor: "divider",
          }}
        >
          <IconButton
            onClick={() => moveElement(selectedElement, "down")}
            size="small"
            disabled={
              store.selectedElement == null ||
              store.editorElements.length <= 1 ||
              isLastElement
            }
            title={t("move_up")}
          >
            <ArrowUpward />
          </IconButton>
          <IconButton
            onClick={() => moveElement(selectedElement, "up")}
            size="small"
            disabled={
              store.selectedElement == null ||
              store.editorElements.length <= 1 ||
              isFirstElement
            }
            title={t("move_down")}
          >
            <ArrowDownward />
          </IconButton>
          <IconButton
            onClick={() => moveElement(selectedElement, "top")}
            size="small"
            disabled={
              store.selectedElement == null ||
              store.editorElements.length <= 1 ||
              isLastElement
            }
            title={t("move_to_top")}
          >
            <VerticalAlignTop />
          </IconButton>
          <IconButton
            onClick={() => moveElement(selectedElement, "bottom")}
            size="small"
            disabled={
              store.selectedElement == null ||
              store.editorElements.length <= 1 ||
              isFirstElement
            }
            title={t("move_to_bottom")}
          >
            <VerticalAlignBottom />
          </IconButton>
        </Paper>
      )}
    </Box>
  );
});
