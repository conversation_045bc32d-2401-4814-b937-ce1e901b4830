import React from "react";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import { Delete as DeleteIcon } from "@mui/icons-material";
import { Box, Typography, IconButton, Paper, Slider } from "@mui/material";
import {
  TextField,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { useLanguage } from "../../i18n/LanguageContext";

import {
  Animation,
  FadeInAnimation,
  FadeOutAnimation,
  SlideDirection,
  SlideInAnimation,
  SlideOutAnimation,
  SlideTextType,
  RotateAnimation,
  BounceAnimation,
  ShakeAnimation,
  FlashAnimation,
  ZoomAnimation,
  ZoomInAnimation,
  ZoomOutAnimation,
} from "../../types";

const ANIMATION_TYPE_TO_LABEL: Record<string, string> = {
  fadeIn: "Fade In",
  fadeOut: "Fade Out",
  slideIn: "Slide In",
  slideOut: "Slide Out",
  breathe: "Breathe",
  rotate: "Rotate",
  bounce: "Bounce",
  shake: "Shake",
  flash: "Flash",
  zoom: "Zoom",
  zoomIn: "Zoom In",
  zoomOut: "Zoom Out",
};

export type AnimationResourceProps = {
  animation: Animation;
};

export const AnimationResource = (props: AnimationResourceProps) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();

  return (
    <Box
      sx={{
        m: 0.5,
        p: 0.5,
        display: "flex",
        flexDirection: "column",
        position: "relative",
      }}
    >
      {/* <Box
        sx={{ display: "flex", justifyContent: "space-between", width: "100%" }}
      >
        <Typography
          variant="body2"
          sx={{
            color: "text.secondary",
          }}
        >
          {ANIMATION_TYPE_TO_LABEL[props.animation.type]}
        </Typography>
  
      </Box> */}
      {(props.animation.type === "fadeIn" ||
        props.animation.type === "fadeOut") && (
        <FadeAnimation
          animation={props.animation as FadeInAnimation | FadeOutAnimation}
        />
      )}
      {(props.animation.type === "slideIn" ||
        props.animation.type === "slideOut") && (
        <SlideAnimation
          animation={props.animation as SlideInAnimation | SlideOutAnimation}
        />
      )}
      {(props.animation.type === "rotate" ||
        props.animation.type === "bounce" ||
        props.animation.type === "shake" ||
        props.animation.type === "flash" ||
        props.animation.type === "zoom" ||
        props.animation.type === "zoomIn" ||
        props.animation.type === "zoomOut" ||
        props.animation.type === "breathe") && (
        <GenericAnimation animation={props.animation} />
      )}
    </Box>
  );
};

export const FadeAnimation = observer(
  (props: { animation: FadeInAnimation | FadeOutAnimation }) => {
    const store = React.useContext(StoreContext);
    const { t } = useLanguage();
    const durationInSeconds = (props.animation.duration / 1000).toFixed(1);

    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          p: 0.5,
          borderRadius: 1,
          "&:hover": {
            backgroundColor: "rgba(0,0,0,0.04)",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 1,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              color: "text.secondary",
              fontWeight: 500,
              fontSize: "0.75rem",
            }}
          >
            {t("fade")}
          </Typography>
          <TextField
            size="small"
            value={durationInSeconds}
            onChange={(e) => {
              const duration = Number(e.target.value) * 1000;
              const isValidDuration = duration > 0;
              let newDuration = isValidDuration ? duration : 10;
              store.updateAnimation(props.animation.id, {
                ...props.animation,
                duration: newDuration,
              });
            }}
            sx={{
              width: "3.5rem",
              ml: 2,
              bgcolor: "grey.100",
              "& input": {
                textAlign: "center",
                fontSize: "0.7rem",
                padding: "4px 8px",
              },
              "& .MuiInputBase-root": {
                height: "1.75rem",
              },
            }}
            InputProps={{
              endAdornment: (
                <Typography variant="caption" sx={{ fontSize: "0.7rem" }}>
                  {t("seconds").charAt(0)}
                </Typography>
              ),
            }}
          />
        </Box>

        <Slider
          size="small"
          min={1}
          max={5}
          step={0.5}
          value={props.animation.duration / 1000}
          onChange={(_, value) => {
            const duration = Number(value) * 1000;
            const isValidDuration = duration > 0;
            let newDuration = isValidDuration ? duration : 10;
            store.updateAnimation(props.animation.id, {
              ...props.animation,
              duration: newDuration,
            });
          }}
          sx={{
            width: "100%",
            "& .MuiSlider-thumb": {
              width: 12,
              height: 12,
            },
            "& .MuiSlider-track": {
              height: 4,
            },
            "& .MuiSlider-rail": {
              height: 4,
            },
          }}
        />
      </Box>
    );
  }
);

export const GenericAnimation = observer((props: { animation: Animation }) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const durationInSeconds = (props.animation.duration / 1000).toFixed(1);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        p: 0.5,
        borderRadius: 1,
        "&:hover": {
          backgroundColor: "rgba(0,0,0,0.04)",
        },
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 1,
        }}
      >
        <Typography
          variant="caption"
          sx={{
            color: "text.secondary",
            fontWeight: 500,
            fontSize: "0.75rem",
          }}
        >
          {ANIMATION_TYPE_TO_LABEL[props.animation.type]}
        </Typography>
        <TextField
          size="small"
          value={durationInSeconds}
          onChange={(e) => {
            const duration = Number(e.target.value) * 1000;
            const isValidDuration = duration > 0;
            let newDuration = isValidDuration ? duration : 10;
            store.updateAnimation(props.animation.id, {
              ...props.animation,
              duration: newDuration,
            });
          }}
          sx={{
            width: "3.5rem",
            ml: 2,
            bgcolor: "grey.100",
            "& input": {
              textAlign: "center",
              fontSize: "0.7rem",
              padding: "4px 8px",
            },
            "& .MuiInputBase-root": {
              height: "1.75rem",
            },
          }}
          InputProps={{
            endAdornment: (
              <Typography variant="caption" sx={{ fontSize: "0.7rem" }}>
                {t("seconds").charAt(0)}
              </Typography>
            ),
          }}
        />
      </Box>

      <Slider
        size="small"
        min={0.1}
        max={10}
        step={0.1}
        value={props.animation.duration / 1000}
        onChange={(_, value) => {
          const duration = Number(value) * 1000;
          const isValidDuration = duration > 0;
          let newDuration = isValidDuration ? duration : 10;
          store.updateAnimation(props.animation.id, {
            ...props.animation,
            duration: newDuration,
          });
        }}
        sx={{
          width: "100%",
          "& .MuiSlider-thumb": {
            width: 12,
            height: 12,
          },
          "& .MuiSlider-track": {
            height: 4,
          },
          "& .MuiSlider-rail": {
            height: 4,
          },
        }}
      />
    </Box>
  );
});

export const SlideAnimation = observer(
  (props: { animation: SlideInAnimation | SlideOutAnimation }) => {
    const store = React.useContext(StoreContext);
    const { t } = useLanguage();
    return (
      <Box display="flex" flexDirection="column" width="100%">
        {/* duration */}
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          my={1}
        >
          <Typography variant="caption">
            {t("duration")}({t("seconds").charAt(0)})
          </Typography>
          <TextField
            size="small"
            type="number"
            value={props.animation.duration / 1000}
            onChange={(e) => {
              const duration = Number(e.target.value) * 1000;
              const isValidDuration = duration > 0;
              let newDuration = isValidDuration ? duration : 0;
              if (newDuration < 10) {
                newDuration = 10;
              }
              store.updateAnimation(props.animation.id, {
                ...props.animation,
                duration: newDuration,
              });
            }}
            sx={{ width: "6rem", ml: 2, bgcolor: "grey.100" }}
          />
        </Box>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          my={1}
        >
          <Typography variant="caption">{t("direction")}</Typography>
          <Select
            size="small"
            value={props.animation.properties.direction}
            onChange={(e) => {
              store.updateAnimation(props.animation.id, {
                ...props.animation,
                properties: {
                  ...props.animation.properties,
                  direction: e.target.value as SlideDirection,
                },
              });
            }}
            sx={{ width: "6rem", ml: 2, bgcolor: "grey.100" }}
          >
            <MenuItem value="left">{t("left")}</MenuItem>
            <MenuItem value="right">{t("right")}</MenuItem>
            <MenuItem value="top">{t("top")}</MenuItem>
            <MenuItem value="bottom">{t("bottom")}</MenuItem>
          </Select>
        </Box>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          my={1}
        >
          <Typography variant="caption">{t("use_mask")}</Typography>
          <FormControlLabel
            control={
              <Checkbox
                checked={props.animation.properties.useClipPath}
                onChange={(e) => {
                  store.updateAnimation(props.animation.id, {
                    ...props.animation,
                    properties: {
                      ...props.animation.properties,
                      useClipPath: e.target.checked,
                    },
                  });
                }}
                sx={{ mr: 0 }}
              />
            }
            label=""
            sx={{ mr: 0 }}
          />
        </Box>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          my={1}
        >
          <Typography variant="caption">Type</Typography>
          <Select
            size="small"
            value={props.animation.properties.textType}
            onChange={(e) => {
              store.updateAnimation(props.animation.id, {
                ...props.animation,
                properties: {
                  ...props.animation.properties,
                  textType: e.target.value as SlideTextType,
                },
              });
            }}
            sx={{ width: "6rem", ml: 2 }}
          >
            <MenuItem value="none">None</MenuItem>
            <MenuItem value="character">Character</MenuItem>
          </Select>
        </Box>
      </Box>
    );
  }
);
