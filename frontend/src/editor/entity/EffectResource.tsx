"use client";
import React from "react";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import { VideoEditorElement, ImageEditorElement, EffecType } from "../../types";
import {
  Box,
  Typography,
  Select,
  MenuItem,
  SelectChangeEvent,
} from "@mui/material";

const EFFECT_TYPE_TO_LABEL: Record<string, string> = {
  blackAndWhite: "Black and White",
  none: "None",
  saturate: "Saturate",
  sepia: "Sepia",
  invert: "Invert",
};

export type EffectResourceProps = {
  editorElement: VideoEditorElement | ImageEditorElement;
};

export const EffectResource = observer((props: EffectResourceProps) => {
  const store = React.useContext(StoreContext);

  return (
    <Box
      sx={{
        borderRadius: 2,
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",

        m: 2,
        position: "relative",
        minHeight: 100,
        p: 2,
      }}
    >
      <Box
        sx={{ display: "flex", justifyContent: "space-between", width: "100%" }}
      >
        <Typography
          variant="body1"
          sx={{ py: 1, textAlign: "left", width: "100%" }}
        >
          {EFFECT_TYPE_TO_LABEL[props.editorElement.properties.effect.type]}
        </Typography>
      </Box>
      <Select
        size="small"
        value={props.editorElement.properties.effect.type}
        onChange={(e: SelectChangeEvent<string>) => {
          const type = e.target.value;
          store.updateEffect(props.editorElement.id, {
            type: type as EffecType,
          });
        }}
        sx={{
          borderRadius: 1,
          px: 1,
          py: 0.5,
          ml: 1,
          width: 120,
          fontSize: 12,
        }}
      >
        {Object.entries(EFFECT_TYPE_TO_LABEL).map(([type, label]) => (
          <MenuItem key={type} value={type}>
            {label}
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
});
