"use client";
import React from "react";
import { StoreContext } from "../../store";
import { formatTimeToMinSec } from "../../utils";
import { observer } from "mobx-react";
import { Add as MdAdd } from "@mui/icons-material";
import { Box, Typography, IconButton } from "@mui/material";

export type AudioResourceProps = {
  audio: string;
  index: number;
};

export const AudioResource = observer(
  ({ audio, index }: AudioResourceProps) => {
    const store = React.useContext(StoreContext);
    const ref = React.useRef<HTMLAudioElement>(null);
    const [formatedAudioLength, setFormatedAudioLength] =
      React.useState("00:00");

    return (
      <Box
        sx={{
          borderRadius: 2,
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          minHeight: 100,
          m: 2,
          bgcolor: "grey.900",
        }}
      >
        <Typography
          sx={{
            bgcolor: "rgba(0,0,0,.25)",
            color: "white",
            py: 0.5,
            position: "absolute",
            top: 8,
            right: 8,
            fontSize: 16,
          }}
        >
          {formatedAudioLength}
        </Typography>
        <IconButton
          sx={{
            bgcolor: "rgba(0,0,0,.25)",
            color: "white",
            position: "absolute",
            bottom: 8,
            right: 8,
            zIndex: 10,
            "&:hover": {
              bgcolor: "#00a0f5",
            },
          }}
          onClick={() => store.addAudio(index.toString())}
        >
          <MdAdd fontSize="large" />
        </IconButton>
        <audio
          onLoadedData={() => {
            const audioLength = ref.current?.duration ?? 0;
            setFormatedAudioLength(formatTimeToMinSec(audioLength));
          }}
          ref={ref}
          style={{
            maxHeight: 100,
            maxWidth: 150,
            minHeight: 50,
            minWidth: 100,
          }}
          // controls
          src={audio}
          id={`audio-${index}`}
        ></audio>
      </Box>
    );
  }
);
