"use client";
import React from "react";
import { EditorElement } from "../../types";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import {
  MdOutlineTextFields,
  MdMovie,
  MdImage,
  MdAudiotrack,
} from "react-icons/md";
import { Box, Typography, Menu, MenuItem, IconButton } from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import DeleteIcon from "@mui/icons-material/Delete";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import VerticalAlignBottomIcon from "@mui/icons-material/VerticalAlignBottom";
import VerticalAlignTopIcon from "@mui/icons-material/VerticalAlignTop";
import Tooltip from "@mui/material/Tooltip";

export type ElementProps = {
  element: EditorElement;
};

export const Element = observer((props) => {
  const store = React.useContext(StoreContext);
  const { element } = props;
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const getIcon = () => {
    switch (element.type) {
      case "video":
        return MdMovie;
      case "image":
        return MdImage;
      case "audio":
        return MdAudiotrack;
      default:
        return MdOutlineTextFields;
    }
  };
  const Icon = getIcon();
  const isSelected = store.selectedElement?.id === element.id;

  const getColor = () => {
    switch (element.type) {
      case "video":
        return "#e57373";
      case "image":
        return "#81c784";
      case "audio":
        return "#64b5f6";
      default:
        return "#ffd54f";
    }
  };

  const moveElement = (
    element: any,
    direction: "up" | "down" | "top" | "bottom"
  ) => {
    store.moveElement(element, direction);
    handleClose();
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuAction = (action: string) => {
    switch (action) {
      case "delete":
        store.removeEditorElement(element.id);
        break;
      // case 'up':
      //   store.moveElementUp(element.id);
      //   break;
      // case 'down':
      //   store.moveElementDown(element.id);
      //   break;
      // case 'back':
      //   store.moveElementBack(element.id);
      //   break;
      // case 'bottom':
      //   store.moveElementToBottom(element.id);
      //   break;
      // case 'top':
      //   store.moveElementToTop(element.id);
      //   break;
    }
    store.refreshElements();
    handleClose();
  };

  return (
    <Box
      sx={{
        width: "100%",
        height: 35,
        p: 2,

        backgroundColor: isSelected
          ? "rgba(0, 160, 245, 0.1)"
          : "background.paper",
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-start",
        alignItems: "center",
        margin: "4px 8px",

        borderRadius: "8px",
        boxShadow: "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
        transition: "all 0.3s cubic-bezier(.25,.8,.25,1)",
        "&:hover": {
          boxShadow: "0 3px 3px rgba(0,0,0,0.25), 0 5px 5px rgba(0,0,0,0.22)",
          backgroundColor: "rgba(0, 160, 245, 0.05)",
        },
      }}
      onClick={() => {
        store.setSelectedElement(element);
      }}
    >
      <Icon size="30" color={getColor()} />
      <Typography
        variant="body2"
        sx={{
          marginLeft: 2,
          flexGrow: 1,
          fontWeight: 500,
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
        }}
      >
        {element.name}
      </Typography>

      <IconButton
        onClick={handleClick}
        sx={{
          cursor: "pointer",
          opacity: 0.7,
          "&:hover": {
            opacity: 1,
          },
        }}
      >
        <MoreVertIcon />
      </IconButton>

      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        <Tooltip title="Delete" placement="left-start">
          <MenuItem onClick={() => handleMenuAction("delete")}>
            <DeleteIcon color="primary" fontSize="small" />
          </MenuItem>
        </Tooltip>
        <Tooltip title="Move down" placement="left-start">
          <MenuItem onClick={() => moveElement(element, "down")}>
            <ArrowUpwardIcon color="primary" fontSize="small" />
          </MenuItem>
        </Tooltip>
        <Tooltip title="Move up" placement="left-start">
          <MenuItem onClick={() => moveElement(element, "up")}>
            <ArrowDownwardIcon color="primary" fontSize="small" />
          </MenuItem>
        </Tooltip>
        <Tooltip title="Move to bottom" placement="left-start">
          <MenuItem onClick={() => moveElement(element, "bottom")}>
            <VerticalAlignBottomIcon color="primary" fontSize="small" />
          </MenuItem>
        </Tooltip>
        <Tooltip title="Move to top" placement="left-start">
          <MenuItem onClick={() => moveElement(element, "top")}>
            <VerticalAlignTopIcon color="primary" fontSize="small" />
          </MenuItem>
        </Tooltip>
      </Menu>
    </Box>
  );
});
