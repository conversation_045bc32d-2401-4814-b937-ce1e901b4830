"use client";
import React from "react";
import { EditorElement } from "../../types";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import {
  MdOutlineTextFields,
  MdMovie,
  MdImage,
  MdAudiotrack,
} from "react-icons/md";
import { Box, Typography, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

export type ElementProps = {
  element: EditorElement;
};

export const SettingResource = observer((props) => {
  const store = React.useContext(StoreContext);
  const { element } = props;
  const getIcon = () => {
    switch (element.type) {
      case "video":
        return MdMovie;
      case "image":
        return MdImage;
      case "audio":
        return MdAudiotrack;
      default:
        return MdOutlineTextFields;
    }
  };
  const Icon = getIcon();

  return (
    <Box sx={{ display: "none" }}>
      {element.type === "video" && (
        <video
          style={{ opacity: 0, maxHeight: "1px" }}
          src={element.properties.src}
          onLoad={() => store.refreshElements()}
          onLoadedData={() => store.refreshElements()}
          id={element.properties.elementId}
        />
      )}
      {element.type === "image" && (
        <img
          style={{ opacity: 0, height: "1px" }}
          src={element.properties.src}
          onLoad={() => store.refreshElements()}
          onLoadedData={() => store.refreshElements()}
          id={element.properties.elementId}
        />
      )}
      {element.type === "audio" && (
        <audio
          style={{ opacity: 0, height: "1px" }}
          src={element.properties.src}
          onLoad={() => store.refreshElements()}
          onLoadedData={() => store.refreshElements()}
          id={element.properties.elementId}
        />
      )}
    </Box>
  );
});
