import { Box } from "@mui/material";
import React, { useMemo } from "react";
import { DurationLabelProps } from "./types/types";

const DurationLabelComponent = ({
  startTime,
  endTime,
  isVisible,
}: DurationLabelProps) => {
  // 优化样式对象的创建
  const labelStyles = useMemo(
    () => ({
      position: "absolute",
      bottom: "-18px",
      fontSize: "0.6rem",
      color: "text.secondary",
    }),
    []
  );

  const leftLabelStyles = useMemo(
    () => ({
      ...labelStyles,
      left: 0,
    }),
    [labelStyles]
  );

  const rightLabelStyles = useMemo(
    () => ({
      ...labelStyles,
      right: 0,
    }),
    [labelStyles]
  );

  if (!isVisible) return null;

  return (
    <>
      <Box sx={leftLabelStyles}>{startTime}</Box>
      <Box sx={rightLabelStyles}>{endTime}</Box>
    </>
  );
};

export const DurationLabel = React.memo(
  DurationLabelComponent,
  (prevProps, nextProps) => {
    // 精确比较props，避免不必要的重渲染
    return (
      prevProps.startTime === nextProps.startTime &&
      prevProps.endTime === nextProps.endTime &&
      prevProps.isVisible === nextProps.isVisible
    );
  }
);
