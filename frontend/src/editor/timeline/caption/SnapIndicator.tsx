import { Box } from "@mui/material";
import React, { memo, useMemo } from "react";

interface SnapIndicatorProps {
  position: "left" | "right";
  isVisible: boolean;
}

const SnapIndicatorComponent = ({
  position,
  isVisible,
}: SnapIndicatorProps) => {
  // 优化样式对象的创建
  const indicatorStyles = useMemo(
    () => ({
      position: "absolute",
      [position]: 0,
      top: "-15px",
      width: "2px",
      height: "15px",
      bgcolor: "#ff9800", // 橙色
      zIndex: 60,
      boxShadow: "0 0 4px rgba(255,152,0,0.7)",
      "&::after": {
        content: '""',
        position: "absolute",
        top: 0,
        [position === "left" ? "left" : "right"]: "-3px",
        width: "8px",
        height: "8px",
        borderRadius: "50%",
        bgcolor: "#ff9800",
        boxShadow: "0 0 4px rgba(255,152,0,0.7)",
      },
      animation: "pulseSnap 1.5s infinite",
      "@keyframes pulseSnap": {
        "0%": { opacity: 0.7 },
        "50%": { opacity: 1 },
        "100%": { opacity: 0.7 },
      },
    }),
    [position]
  );

  if (!isVisible) return null;

  return <Box sx={indicatorStyles} />;
};

export const SnapIndicator = memo(
  SnapIndicatorComponent,
  (prevProps, nextProps) => {
    // 精确比较props，避免不必要的重渲染
    return (
      prevProps.position === nextProps.position &&
      prevProps.isVisible === nextProps.isVisible
    );
  }
);

SnapIndicator.displayName = "SnapIndicator";
