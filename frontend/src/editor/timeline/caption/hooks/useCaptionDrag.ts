import { useCallback, useState, useMemo, useRef } from "react";
import { Caption } from "../../../../types";
import { formatCaptionTime, timeStringToMs } from "../../../../utils/timeUtils";
import {
  generateSnapPointsFromCaptions,
  findNearestSnapPoint,
} from "../../utils";
import { checkTimeOverlap } from "../../utils";

interface UseCaptionDragProps {
  caption: Caption;
  allCaptions: Caption[];
  containerWidth: number | null;
  store: any;
}

interface DragHandlers {
  handleLeftHandleDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  handleRightHandleDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  handleCenterDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  isDragging: boolean;
  isSnappedStart: boolean;
  isSnappedEnd: boolean;
}

// 辅助函数：直接更新字幕元素的视觉位置，不触发数据更新
const updateCaptionVisualPosition = (
  itemEl: HTMLElement | null,
  startTimeMs: number,
  endTimeMs: number,
  store?: any
) => {
  if (!itemEl) return;

  // 获取时间轴容器
  const timelineContainer = document.querySelector(".timeline-container");
  if (!timelineContainer) return;

  const containerRect = timelineContainer.getBoundingClientRect();
  const containerWidth = containerRect.width;

  // 获取当前的时间轴显示范围
  let visibleStartTime = 0;
  let timelineDisplayDuration = 60000; // 默认60秒

  // 从传入的store或全局store获取时间轴信息
  const storeToUse = store || (window as any).store;
  if (storeToUse) {
    visibleStartTime = storeToUse.timelinePan?.offsetX || 0;
    timelineDisplayDuration = storeToUse.timelineDisplayDuration || 60000;
  }

  // 计算字幕在时间轴上的位置和宽度
  const startPercent = Math.max(
    0,
    Math.min(1, (startTimeMs - visibleStartTime) / timelineDisplayDuration)
  );
  const endPercent = Math.max(
    0,
    Math.min(1, (endTimeMs - visibleStartTime) / timelineDisplayDuration)
  );

  const left = startPercent * containerWidth;
  const width = Math.max(1, (endPercent - startPercent) * containerWidth);

  // 直接更新DOM样式
  itemEl.style.left = `${left}px`;
  itemEl.style.width = `${width}px`;
};

export const useCaptionDrag = ({
  caption,
  allCaptions,
  containerWidth,
  store,
}: UseCaptionDragProps): DragHandlers => {
  const [isDragging, setIsDragging] = useState(false);
  const [isSnappedStart, setIsSnappedStart] = useState(false);
  const [isSnappedEnd, setIsSnappedEnd] = useState(false);

  const startTimeMs = timeStringToMs(caption.startTime);
  const endTimeMs = timeStringToMs(caption.endTime);

  // 使用 useRef 缓存 DOM 元素引用
  const elementRefs = useRef({
    leftHandle: null as HTMLElement | null,
    rightHandle: null as HTMLElement | null,
    element: null as HTMLElement | null,
  });

  // 获取最新的字幕数据，确保碰撞检测使用最新的位置信息
  const getCurrentCaptions = useCallback((): Caption[] => {
    return store.captions || allCaptions;
  }, [store.captions, allCaptions]);

  // 使用 useMemo 缓存吸附点计算结果
  const snapPointsCache = useMemo(() => {
    return generateSnapPointsFromCaptions(getCurrentCaptions(), caption.id);
  }, [getCurrentCaptions, caption.id]);

  // 更新 DOM 元素引用的函数
  const updateElementRefs = useCallback(() => {
    elementRefs.current = {
      leftHandle: document.getElementById("left-handle"),
      rightHandle: document.getElementById("right-handle"),
      element: document.getElementById(`caption-item-${caption.id}`),
    };
  }, [caption.id]);

  // 统一的碰撞视觉反馈处理函数
  const handleCollisionFeedback = useCallback(
    (
      itemEl: HTMLElement | null,
      isColliding: boolean,
      dragType: "left" | "right" | "center"
    ) => {
      if (!itemEl) return;

      // 使用 CSS transform 代替直接修改位置
      if (isColliding) {
        itemEl.classList.add("panning-mode");
        itemEl.style.transform = "translateX(0)";
      } else {
        itemEl.classList.remove("panning-mode");
      }
    },
    []
  );

  // 左侧控制柄拖拽处理
  const handleLeftHandleDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      const trackElement =
        document.querySelector(".timeline-container") ||
        event.currentTarget.parentElement;
      if (!trackElement) return;

      const leftHandleEl = document.getElementById("left-handle");
      const itemEl = document.getElementById(`caption-item-${caption.id}`);

      // 记录初始位置，用于计算拖拽距离
      const initialPosition = {
        x: event.clientX,
        y: event.clientY,
      };

      // 添加拖拽预览效果的状态
      let dragPreviewActive = false;
      let dragDistance = 0;
      let lastMouseX = event.clientX; // 记录上一次鼠标X位置，用于检测拖动方向

      const initialStartTime = startTimeMs;

      // 获取所有结束时间小于等于当前字幕开始时间的字幕，按开始时间排序（从右到左）
      const currentCaptions = getCurrentCaptions();
      const precedingCaptionsRaw = currentCaptions
        .filter((c) => c.id !== caption.id)
        .filter((c) => timeStringToMs(c.endTime) <= initialStartTime)
        .sort(
          (a, b) => timeStringToMs(b.startTime) - timeStringToMs(a.startTime)
        ); // 按开始时间降序排列，最近的在前面

      // 计算每个字幕与其后一个字幕之间的间隔
      const precedingCaptions = precedingCaptionsRaw.map((c, index) => {
        const startTime = timeStringToMs(c.startTime);
        const endTime = timeStringToMs(c.endTime);

        // 计算与后一个字幕之间的间隔
        let gapToNext = 0;
        if (index === 0) {
          // 第一个（最近的）字幕与当前字幕之间的间隔
          gapToNext = initialStartTime - endTime;
        } else {
          // 与前一个字幕之间的间隔
          const nextCaption = precedingCaptionsRaw[index - 1];
          const nextStartTime = timeStringToMs(nextCaption.startTime);
          gapToNext = nextStartTime - endTime;
        }

        return {
          id: c.id,
          startTime,
          endTime,
          gapToNext: Math.max(0, gapToNext), // 确保间隔不为负数
          offsetFromCurrent: initialStartTime - endTime,
        };
      });

      // 获取最近的前一个字幕（结束时间最接近当前字幕开始时间的字幕）
      const prevCaption =
        precedingCaptions.length > 0
          ? precedingCaptions[0] // 由于已经按开始时间降序排列，第一个就是最近的
          : null;

      // 检查初始碰撞状态
      const initialCollisionState =
        prevCaption && initialStartTime <= prevCaption.endTime;
      console.log("左侧控制器初始碰撞检测:", {
        initialStartTime,
        prevCaptionEndTime: prevCaption?.endTime,
        isInitiallyColliding: initialCollisionState,
        gap: prevCaption ? initialStartTime - prevCaption.endTime : null,
        hasPrevCaption: !!prevCaption,
        hasItemEl: !!itemEl,
        exactComparison: prevCaption
          ? initialStartTime === prevCaption.endTime
          : false,
      });

      // 显示初始碰撞样式
      if (initialCollisionState && itemEl) {
        console.log("应用左侧碰撞样式，添加 panning-mode 类");
        handleCollisionFeedback(itemEl, true, "left");
        // 验证样式是否被正确添加
        setTimeout(() => {
          console.log("验证样式应用:", {
            hasPanningMode: itemEl.classList.contains("panning-mode"),
            allClasses: Array.from(itemEl.classList),
          });
        }, 10);
      } else {
        console.log("不应用碰撞样式，原因:", {
          hasInitialCollision: initialCollisionState,
          hasItemEl: !!itemEl,
        });
      }

      let isPanningMode = false;

      // 创建临时状态来存储拖动过程中的时间变化，避免立即更新store
      let tempStartTime = initialStartTime;
      let tempPrecedingCaptionsUpdates: Array<{
        id: string;
        startTime: number;
        endTime: number;
      }> = [];

      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 计算拖拽距离
        dragDistance = Math.sqrt(
          Math.pow(moveEvent.clientX - initialPosition.x, 2) +
            Math.pow(moveEvent.clientY - initialPosition.y, 2)
        );

        // 只有当拖拽距离超过阈值时才开始拖拽
        if (!dragPreviewActive && dragDistance > 5) {
          dragPreviewActive = true;
          // 添加拖拽预览效果的类
          if (leftHandleEl) {
            leftHandleEl.classList.add("handle-dragging");
          }
          if (itemEl) {
            itemEl.classList.add("resize-active");
          }
        }

        const trackRect = trackElement.getBoundingClientRect();
        const trackX = moveEvent.clientX - trackRect.left;
        const trackPercent = Math.max(0, Math.min(1, trackX / trackRect.width));

        const visibleStartTime = store.timelinePan.offsetX;
        const newStartTime =
          visibleStartTime + trackPercent * store.timelineDisplayDuration;

        let safeStartTime = Math.min(newStartTime, endTimeMs - 100);

        // 检测鼠标移动方向
        const isMouseMovingLeft = moveEvent.clientX < lastMouseX;
        lastMouseX = moveEvent.clientX;

        // 检测是否当前已经与相邻字幕碰撞
        const isCurrentlyColliding =
          prevCaption && safeStartTime <= prevCaption.endTime;

        // 检测拖动方向（基于时间变化）
        const isDraggingLeft = safeStartTime < initialStartTime;

        // 平推模式触发条件（参考右侧控制器逻辑）：
        // 当向左拖动且当前字幕的startTime小于等于前面字幕的endTime时，进入平推模式
        // 这样可以在还有距离的时候就开始推动前面的字幕
        const isColliding =
          prevCaption &&
          ((isDraggingLeft && safeStartTime <= prevCaption.endTime) ||
            (isMouseMovingLeft && isCurrentlyColliding));

        // 调试日志
        if (prevCaption) {
          console.log("左侧碰撞检测:", {
            safeStartTime,
            prevCaptionEndTime: prevCaption.endTime,
            isDraggingLeft,
            isMouseMovingLeft,
            isCurrentlyColliding,
            isColliding,
            gap: safeStartTime - prevCaption.endTime,
          });
        }

        // 只在向左拖动并且碰撞时进入平移模式
        isPanningMode = isColliding;

        // 更新平移模式的视觉反馈
        handleCollisionFeedback(itemEl, isPanningMode, "left");

        // 更新临时状态，不立即更新store数据
        tempStartTime = safeStartTime;

        // 如果不在平移模式，直接更新当前字幕元素的视觉位置
        if (!isPanningMode) {
          updateCaptionVisualPosition(itemEl, safeStartTime, endTimeMs, store);
        }

        if (isPanningMode) {
          // 在平移模式（向左拖动碰撞），计算前面字幕的临时位置
          let adjustedStartTime = safeStartTime;

          // 检查是否需要限制平移距离以防止最左边的元素超出0
          // 计算平推链条，允许将最左边字幕推到0，保持原始间隔
          if (precedingCaptions.length > 0) {
            // 模拟计算整个平推链条，找到最左边字幕的新开始时间
            let simulatedReferenceTime = adjustedStartTime;
            let minStartTime = Number.MAX_SAFE_INTEGER;
            let leftmostCaptionInfo = null;

            // 从右到左计算每个字幕的新位置
            for (let i = 0; i < precedingCaptions.length; i++) {
              const captionInfo = precedingCaptions[i];
              const { gapToNext } = captionInfo;
              const duration = captionInfo.endTime - captionInfo.startTime;

              // 计算新的结束时间（保持间隔）
              const simulatedEnd =
                i === 0
                  ? simulatedReferenceTime
                  : simulatedReferenceTime - gapToNext;
              const simulatedStart = simulatedEnd - duration;

              if (simulatedStart < minStartTime) {
                minStartTime = simulatedStart;
                leftmostCaptionInfo = {
                  ...captionInfo,
                  newStart: simulatedStart,
                  newEnd: simulatedEnd,
                };
              }

              simulatedReferenceTime = simulatedStart;
            }

            // 如果最左边的字幕会被推到0以下，调整整个链条
            if (minStartTime < 0 && leftmostCaptionInfo) {
              const adjustment = -minStartTime; // 需要向右调整的距离

              console.log(
                `左侧平推: 最左边字幕会被推到 ${minStartTime}，需要向右调整 ${adjustment}`
              );

              // 调整当前字幕的开始时间，确保最左边字幕的startTime为0
              adjustedStartTime = safeStartTime + adjustment;
              tempStartTime = adjustedStartTime;

              console.log(
                `左侧平推: 调整当前字幕开始时间到 ${adjustedStartTime}`
              );
            }
          }

          // 更新当前字幕元素的视觉位置（使用调整后的开始时间）
          updateCaptionVisualPosition(
            itemEl,
            adjustedStartTime,
            endTimeMs,
            store
          );

          // 计算前面字幕的临时更新，使用调整后的开始时间
          // 从右到左（从最近的到最远的）逐个计算前面字幕的新位置，保持原始间隔
          let currentReferenceTime = adjustedStartTime; // 当前字幕的新开始时间作为参考点

          console.log(
            `左侧平推: 开始计算 ${precedingCaptions.length} 个前面字幕的新位置`
          );
          console.log(
            `左侧平推: 参考时间（当前字幕新开始时间）= ${currentReferenceTime}`
          );

          tempPrecedingCaptionsUpdates = precedingCaptions.map(
            (captionInfo, index) => {
              const { id, startTime, endTime, gapToNext } = captionInfo;
              const duration = endTime - startTime;

              console.log(`左侧平推: 处理字幕 ${id} (index=${index})`);
              console.log(
                `  原始: startTime=${startTime}, endTime=${endTime}, duration=${duration}`
              );
              console.log(
                `  gapToNext=${gapToNext}, currentReferenceTime=${currentReferenceTime}`
              );

              // 计算新的结束时间，保持与右边字幕的间隔
              let newEnd: number;

              if (index === 0) {
                // 第一个前面字幕：紧贴当前字幕（参考右侧控制器逻辑）
                newEnd = currentReferenceTime;
                console.log(
                  `左侧平推: 第一个字幕 ${id} 紧贴当前字幕，newEnd=${newEnd}`
                );
              } else {
                // 后续字幕：与右边字幕保持原始间隔
                const gap = gapToNext; // 这是与右边字幕的间隔
                newEnd = currentReferenceTime - gap;
                console.log(
                  `左侧平推: 字幕 ${id} 与右边字幕保持间隔 ${gap}ms，newEnd=${newEnd}`
                );
              }

              let newStart = newEnd - duration;

              // 确保平移后的字幕时间仍然合理，最小为0
              // 由于我们已经在前面调整了adjustedStartTime，这里应该不会出现newStart < 0的情况
              // 但为了安全起见，仍然进行检查
              if (newStart < 0) {
                console.warn(
                  `左侧平推: 字幕 ${id} 的newStart=${newStart} < 0，这不应该发生`
                );
                newStart = 0;
                newEnd = newStart + duration; // 保持原始持续时间
              }

              // 最终验证：确保startTime < endTime
              if (newStart >= newEnd) {
                console.warn(`平移字幕 ${id} 时检测到时间错乱，跳过更新`);
                return { id, startTime, endTime };
              }

              // 更新下一个字幕的参考点（当前字幕的开始时间）
              currentReferenceTime = newStart;

              console.log(`  最终: newStart=${newStart}, newEnd=${newEnd}`);
              console.log(`  下一个参考时间: ${currentReferenceTime}`);

              // 直接更新前面字幕元素的视觉位置
              const precedingItemEl = document.getElementById(
                `caption-item-${id}`
              );
              updateCaptionVisualPosition(
                precedingItemEl,
                newStart,
                newEnd,
                store
              );

              return { id, startTime: newStart, endTime: newEnd };
            }
          );
        } else {
          // 清空前面字幕的临时更新，并恢复它们的原始视觉位置
          precedingCaptions.forEach(({ id, startTime, endTime }) => {
            const precedingItemEl = document.getElementById(
              `caption-item-${id}`
            );
            updateCaptionVisualPosition(
              precedingItemEl,
              startTime,
              endTime,
              store
            );
          });
          tempPrecedingCaptionsUpdates = [];
        }
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        setIsDragging(false);

        // 移除所有拖拽预览效果的类
        if (leftHandleEl) {
          leftHandleEl.classList.remove("handle-dragging");
          leftHandleEl.classList.remove("snapped");
        }

        if (itemEl) {
          // 移除所有可能的类，包括碰撞反馈样式
          itemEl.classList.remove("resize-active");
          itemEl.classList.remove("panning-mode");
          itemEl.classList.remove("dragging-active");
          handleCollisionFeedback(itemEl, false, "left"); // 清除碰撞样式
        }

        // 现在才真正更新字幕数据
        console.log("左侧控制柄拖动结束，开始更新数据:");
        console.log("当前字幕新的开始时间:", tempStartTime);
        console.log("前面字幕的更新数量:", tempPrecedingCaptionsUpdates.length);

        // 最终验证：确保当前字幕的startTime < endTime
        const currentEndTime = endTimeMs;
        let finalStartTime = tempStartTime;

        if (finalStartTime >= currentEndTime) {
          console.warn(`左侧拖动: 检测到startTime >= endTime，进行修复`);
          console.warn(
            `原始: startTime=${finalStartTime}, endTime=${currentEndTime}`
          );
          // 确保至少有100ms的持续时间
          finalStartTime = Math.max(0, currentEndTime - 100);
          console.warn(
            `修复后: startTime=${finalStartTime}, endTime=${currentEndTime}`
          );
        }

        // 首先更新当前字幕的startTime
        const startTimeFormatted = formatCaptionTime(finalStartTime);
        store.updateCaption(caption.id, "startTime", startTimeFormatted);

        // 如果有前面字幕的临时更新，也要应用它们
        if (tempPrecedingCaptionsUpdates.length > 0) {
          console.log("应用前面字幕的更新:");
          tempPrecedingCaptionsUpdates.forEach(({ id, startTime, endTime }) => {
            // 验证每个前面字幕的时间
            let validatedStartTime = startTime;
            let validatedEndTime = endTime;

            if (validatedStartTime >= validatedEndTime) {
              console.warn(
                `前面字幕 ${id}: 检测到startTime >= endTime，进行修复`
              );
              console.warn(
                `原始: startTime=${validatedStartTime}, endTime=${validatedEndTime}`
              );
              // 确保至少有100ms的持续时间
              validatedStartTime = Math.max(0, validatedEndTime - 100);
              console.warn(
                `修复后: startTime=${validatedStartTime}, endTime=${validatedEndTime}`
              );
            }

            const startFormatted = formatCaptionTime(validatedStartTime);
            const endFormatted = formatCaptionTime(validatedEndTime);
            console.log(`更新字幕 ${id}: ${startFormatted} - ${endFormatted}`);
            store.updateCaptionTimeFrame(id, startFormatted, endFormatted);
          });
        }

        // 重置isPanningMode
        isPanningMode = false;

        // 拖拽结束后才进行排序
        store.captionManager.sortCaptionsByStartTime();

        // 如果没有进行平推操作，才使用智能碰撞检测和修复函数
        // 平推操作已经确保了不会有重叠并保持了间隔
        if (tempPrecedingCaptionsUpdates.length === 0) {
          store.captionManager.preventAllCaptionsOverlap();
        }

        // 同步回store.captions
        store.captions = store.captionManager.captions;

        // 拖拽完成后，根据当前时间线指示器位置更新字幕显示
        // 这确保了字幕在时间范围外时能正确隐藏
        store.captionManager.updateCurrentCaption(store.currentTimeInMs);

        // 清除所有字幕元素的临时样式，让它们重新基于数据渲染
        getCurrentCaptions().forEach((cap) => {
          const capItemEl = document.getElementById(`caption-item-${cap.id}`);
          if (capItemEl) {
            capItemEl.style.left = "";
            capItemEl.style.width = "";
          }
        });

        // 保存更改
        store.saveChange();
      };

      setIsDragging(true);
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [
      caption.id,
      startTimeMs,
      endTimeMs,
      containerWidth,
      isDragging,
      store,
      allCaptions,
    ]
  );

  // 右侧控制柄拖拽处理
  const handleRightHandleDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      const trackElement =
        document.querySelector(".timeline-container") ||
        event.currentTarget.parentElement;
      if (!trackElement) return;

      const rightHandleEl = document.getElementById("right-handle");
      const itemEl = document.getElementById(`caption-item-${caption.id}`);

      // 记录初始位置，用于计算拖拽距离
      const initialPosition = {
        x: event.clientX,
        y: event.clientY,
      };

      // 添加拖拽预览效果的状态
      let dragPreviewActive = false;
      let dragDistance = 0;
      let lastMouseX = event.clientX; // 记录上一次鼠标X位置，用于检测拖动方向

      const initialEndTime = endTimeMs;

      // 获取所有在当前字幕结束时间之后或紧贴的字幕，并计算间隔
      const currentCaptions = getCurrentCaptions();
      const followingCaptionsRaw = currentCaptions
        .filter((c) => c.id !== caption.id)
        .filter((c) => timeStringToMs(c.startTime) >= initialEndTime)
        .sort(
          (a, b) => timeStringToMs(a.startTime) - timeStringToMs(b.startTime)
        ); // 按开始时间升序排列

      // 计算每个字幕与其前一个字幕之间的间隔
      const followingCaptions = followingCaptionsRaw.map((c, index) => {
        const startTime = timeStringToMs(c.startTime);
        const endTime = timeStringToMs(c.endTime);

        // 计算与前一个字幕之间的间隔
        let gapFromPrev = 0;
        if (index === 0) {
          // 第一个（最近的）字幕与当前字幕之间的间隔
          gapFromPrev = startTime - initialEndTime;
        } else {
          // 与前一个字幕之间的间隔
          const prevCaption = followingCaptionsRaw[index - 1];
          const prevEndTime = timeStringToMs(prevCaption.endTime);
          gapFromPrev = startTime - prevEndTime;
        }

        return {
          id: c.id,
          startTime,
          endTime,
          gapFromPrev: Math.max(0, gapFromPrev), // 确保间隔不为负数
          offsetFromCurrent: startTime - initialEndTime,
        };
      });

      // 获取最近的后一个字幕（开始时间最接近当前字幕结束时间的字幕）
      const nextCaption =
        followingCaptions.length > 0
          ? followingCaptions[0] // 由于已经按开始时间升序排列，第一个就是最近的
          : null;

      // 检查初始碰撞状态
      const initialCollisionState =
        nextCaption && initialEndTime >= nextCaption.startTime;
      console.log("右侧控制器初始碰撞检测:", {
        currentCaptionId: caption.id,
        currentCaptionText: caption.text,
        initialEndTime,
        initialEndTimeFormatted: formatCaptionTime(initialEndTime),
        nextCaptionId: nextCaption?.id,
        nextCaptionText: nextCaption
          ? currentCaptions.find((c) => c.id === nextCaption.id)?.text
          : null,
        nextCaptionStartTime: nextCaption?.startTime,
        nextCaptionStartTimeFormatted: nextCaption
          ? formatCaptionTime(nextCaption.startTime)
          : null,
        isInitiallyColliding: initialCollisionState,
        gap: nextCaption ? nextCaption.startTime - initialEndTime : null,
        gapInSeconds: nextCaption
          ? (nextCaption.startTime - initialEndTime) / 1000
          : null,
        hasNextCaption: !!nextCaption,
        hasItemEl: !!itemEl,
        exactComparison: nextCaption
          ? initialEndTime === nextCaption.startTime
          : false,
        allFollowingCaptions: followingCaptions.map((c) => ({
          id: c.id,
          text: currentCaptions.find((ac) => ac.id === c.id)?.text,
          startTime: c.startTime,
          startTimeFormatted: formatCaptionTime(c.startTime),
        })),
      });

      // 显示初始碰撞样式
      if (initialCollisionState && itemEl) {
        console.log("应用右侧碰撞样式，添加 panning-mode 类");
        handleCollisionFeedback(itemEl, true, "right");
        // 验证样式是否被正确添加
        setTimeout(() => {
          console.log("验证样式应用:", {
            hasPanningMode: itemEl.classList.contains("panning-mode"),
            allClasses: Array.from(itemEl.classList),
          });
        }, 10);
      } else {
        console.log("不应用碰撞样式，原因:", {
          hasInitialCollision: initialCollisionState,
          hasItemEl: !!itemEl,
        });
      }

      let isPanningMode = false;

      // 创建临时状态来存储拖动过程中的时间变化，避免立即更新store
      let tempEndTime = initialEndTime;
      let tempFollowingCaptionsUpdates: Array<{
        id: string;
        startTime: number;
        endTime: number;
      }> = [];

      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 计算拖拽距离
        dragDistance = Math.sqrt(
          Math.pow(moveEvent.clientX - initialPosition.x, 2) +
            Math.pow(moveEvent.clientY - initialPosition.y, 2)
        );

        // 只有当拖拽距离超过阈值时才开始拖拽
        if (!dragPreviewActive && dragDistance > 5) {
          dragPreviewActive = true;
          // 添加拖拽预览效果的类
          if (rightHandleEl) {
            rightHandleEl.classList.add("handle-dragging");
          }
          if (itemEl) {
            itemEl.classList.add("resize-active");
          }
        }

        const trackRect = trackElement.getBoundingClientRect();
        const trackX = moveEvent.clientX - trackRect.left;
        const trackPercent = Math.max(0, Math.min(1, trackX / trackRect.width));

        const visibleStartTime = store.timelinePan.offsetX;
        const newEndTime =
          visibleStartTime + trackPercent * store.timelineDisplayDuration;

        let safeEndTime = Math.max(newEndTime, startTimeMs + 100);

        // 检测鼠标移动方向
        const isMouseMovingRight = moveEvent.clientX > lastMouseX;
        lastMouseX = moveEvent.clientX;

        // 检测是否当前已经与相邻字幕碰撞
        const isCurrentlyColliding =
          nextCaption && safeEndTime >= nextCaption.startTime;

        // 检测拖动方向（基于时间变化）
        const isDraggingRight = safeEndTime > initialEndTime;

        // 碰撞条件：
        // 1. 向右拖动且发生重叠，或
        // 2. 鼠标向右移动且当前已经碰撞（处理已碰撞状态下继续拖动的情况）
        const isColliding =
          nextCaption &&
          ((isDraggingRight && safeEndTime > nextCaption.startTime) ||
            (isMouseMovingRight && isCurrentlyColliding));

        // 调试日志
        if (nextCaption) {
          console.log("右侧碰撞检测:", {
            safeEndTime,
            nextCaptionStartTime: nextCaption.startTime,
            isDraggingRight,
            isMouseMovingRight,
            isCurrentlyColliding,
            isColliding,
            gap: nextCaption.startTime - safeEndTime,
          });
        }

        // 只在向右拖动并且碰撞时进入平移模式
        isPanningMode = isColliding;

        // 更新平移模式的视觉反馈
        handleCollisionFeedback(itemEl, isPanningMode, "right");

        // 更新临时状态，不立即更新store数据
        tempEndTime = safeEndTime;

        // 直接更新当前字幕元素的视觉位置
        updateCaptionVisualPosition(itemEl, startTimeMs, safeEndTime, store);

        if (isPanningMode) {
          // 在平移模式（向右拖动碰撞），计算后面字幕的临时位置，保持原始间隔
          let currentReferenceTime = safeEndTime; // 当前字幕的新结束时间作为参考点

          tempFollowingCaptionsUpdates = followingCaptions.map(
            (captionInfo, index) => {
              const { id, startTime, endTime, gapFromPrev } = captionInfo;
              const duration = endTime - startTime;

              // 第一个后面字幕应该紧贴当前字幕，后续字幕保持原始间隔
              let newStart: number;
              if (index === 0) {
                // 第一个后面字幕紧贴当前字幕
                newStart = currentReferenceTime;
                console.log(
                  `右侧平推: 第一个字幕 ${id} 紧贴当前字幕，newStart=${newStart}`
                );
              } else {
                // 后续字幕保持原始间隔
                newStart = currentReferenceTime + gapFromPrev;
                console.log(
                  `右侧平推: 字幕 ${id} 保持间隔 ${gapFromPrev}，newStart=${newStart}`
                );
              }
              let newEnd = newStart + duration;

              // 确保平移后的字幕时间仍然合理
              if (newStart < 0) {
                newStart = 0;
                newEnd = Math.max(newStart + duration, newStart + 100); // 确保最小持续时间
              }

              // 最终验证：确保startTime < endTime
              if (newStart >= newEnd) {
                console.warn(`平移字幕 ${id} 时检测到时间错乱，跳过更新`);
                return { id, startTime, endTime };
              }

              // 更新下一个字幕的参考点（当前字幕的结束时间）
              currentReferenceTime = newEnd;

              // 直接更新后面字幕元素的视觉位置
              const followingItemEl = document.getElementById(
                `caption-item-${id}`
              );
              updateCaptionVisualPosition(
                followingItemEl,
                newStart,
                newEnd,
                store
              );

              return { id, startTime: newStart, endTime: newEnd };
            }
          );
        } else {
          // 清空后面字幕的临时更新，并恢复它们的原始视觉位置
          followingCaptions.forEach(({ id, startTime, endTime }) => {
            const followingItemEl = document.getElementById(
              `caption-item-${id}`
            );
            updateCaptionVisualPosition(
              followingItemEl,
              startTime,
              endTime,
              store
            );
          });
          tempFollowingCaptionsUpdates = [];
        }
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        setIsDragging(false);

        // 移除所有拖拽预览效果的类
        if (rightHandleEl) {
          rightHandleEl.classList.remove("handle-dragging");
          rightHandleEl.classList.remove("snapped");
        }

        const itemEl = document.getElementById(`caption-item-${caption.id}`);
        if (itemEl) {
          // 移除所有可能的类，包括碰撞反馈样式
          itemEl.classList.remove("resize-active");
          itemEl.classList.remove("panning-mode");
          itemEl.classList.remove("dragging-active");
          handleCollisionFeedback(itemEl, false, "right"); // 清除碰撞样式
        }

        // 现在才真正更新字幕数据
        console.log("右侧控制柄拖动结束，开始更新数据:");
        console.log("当前字幕新的结束时间:", tempEndTime);
        console.log("后面字幕的更新数量:", tempFollowingCaptionsUpdates.length);

        // 最终验证：确保当前字幕的startTime < endTime
        const currentStartTime = startTimeMs;
        let finalEndTime = tempEndTime;

        if (currentStartTime >= finalEndTime) {
          console.warn(`右侧拖动: 检测到startTime >= endTime，进行修复`);
          console.warn(
            `原始: startTime=${currentStartTime}, endTime=${finalEndTime}`
          );
          // 确保至少有100ms的持续时间
          finalEndTime = currentStartTime + 100;
          console.warn(
            `修复后: startTime=${currentStartTime}, endTime=${finalEndTime}`
          );
        }

        // 首先更新当前字幕的endTime
        const endTimeFormatted = formatCaptionTime(finalEndTime);
        store.updateCaption(caption.id, "endTime", endTimeFormatted);

        // 如果有后面字幕的临时更新，也要应用它们
        if (tempFollowingCaptionsUpdates.length > 0) {
          console.log("应用后面字幕的更新:");
          tempFollowingCaptionsUpdates.forEach(({ id, startTime, endTime }) => {
            // 验证每个后面字幕的时间
            let validatedStartTime = startTime;
            let validatedEndTime = endTime;

            if (validatedStartTime >= validatedEndTime) {
              console.warn(
                `后面字幕 ${id}: 检测到startTime >= endTime，进行修复`
              );
              console.warn(
                `原始: startTime=${validatedStartTime}, endTime=${validatedEndTime}`
              );
              // 确保至少有100ms的持续时间
              validatedEndTime = validatedStartTime + 100;
              console.warn(
                `修复后: startTime=${validatedStartTime}, endTime=${validatedEndTime}`
              );
            }

            const startFormatted = formatCaptionTime(validatedStartTime);
            const endFormatted = formatCaptionTime(validatedEndTime);
            console.log(`更新字幕 ${id}: ${startFormatted} - ${endFormatted}`);
            store.updateCaptionTimeFrame(id, startFormatted, endFormatted);
          });
        }

        // 重置isPanningMode
        isPanningMode = false;

        // 拖拽结束后才进行排序
        store.captionManager.sortCaptionsByStartTime();

        // 如果没有进行平推操作，才使用智能碰撞检测和修复函数
        // 平推操作已经确保了不会有重叠并保持了间隔
        if (tempFollowingCaptionsUpdates.length === 0) {
          store.captionManager.preventAllCaptionsOverlap();
        }

        // 同步回store.captions
        store.captions = store.captionManager.captions;

        // 拖拽完成后，根据当前时间线指示器位置更新字幕显示
        // 这确保了字幕在时间范围外时能正确隐藏
        store.captionManager.updateCurrentCaption(store.currentTimeInMs);

        // 清除所有字幕元素的临时样式，让它们重新基于数据渲染
        getCurrentCaptions().forEach((cap) => {
          const capItemEl = document.getElementById(`caption-item-${cap.id}`);
          if (capItemEl) {
            capItemEl.style.left = "";
            capItemEl.style.width = "";
          }
        });

        // 保存更改
        store.saveChange();
      };

      setIsDragging(true);
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [
      caption.id,
      startTimeMs,
      endTimeMs,
      containerWidth,
      isDragging,
      store,
      allCaptions,
    ]
  );

  // 中间拖拽处理
  const handleCenterDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      const initialX = event.clientX;
      const initialStartTime = startTimeMs;
      const duration = endTimeMs - startTimeMs;

      const itemEl = document.getElementById(`caption-item-${caption.id}`);
      const leftHandleEl = document.getElementById("left-handle");
      const rightHandleEl = document.getElementById("right-handle");

      // 记录初始位置，用于计算拖拽距离
      const initialPosition = {
        x: event.clientX,
        y: event.clientY,
      };

      // 添加拖拽预览效果的状态
      let dragPreviewActive = false;
      let dragDistance = 0;

      // 创建临时状态来存储拖动过程中的时间变化，避免立即更新store
      let tempStartTime = initialStartTime;
      let tempEndTime = endTimeMs;
      let tempAdjustmentUpdates: Array<{
        id: string;
        field: "startTime" | "endTime";
        value: number;
      }> = [];

      const trackElement =
        event.currentTarget.closest(".timeline-container") ||
        document.querySelector(".timeline-container") ||
        event.currentTarget.parentElement?.parentElement;
      if (!trackElement) return;
      const trackRect = trackElement.getBoundingClientRect();

      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 计算拖拽距离
        dragDistance = Math.sqrt(
          Math.pow(moveEvent.clientX - initialPosition.x, 2) +
            Math.pow(moveEvent.clientY - initialPosition.y, 2)
        );

        // 只有当拖拽距离超过阈值时才开始拖拽
        if (!dragPreviewActive && dragDistance > 5) {
          dragPreviewActive = true;
          // 添加拖拽预览效果的类
          if (itemEl) {
            itemEl.classList.add("dragging-active");
          }
        }

        const deltaX = moveEvent.clientX - initialX;
        const pixelsPerMs = trackRect.width / store.timelineDisplayDuration;
        const timeDelta = deltaX / pixelsPerMs;

        let newStartTime = Math.max(0, initialStartTime + timeDelta);
        let newEndTime = newStartTime + duration;

        // 生成吸附点
        // const snapPoints = generateSnapPointsFromCaptions(
        //   allCaptions,
        //   caption.id
        // );

        // 检查是否与其他字幕重叠
        const currentCaptions = getCurrentCaptions();
        const overlapResult = checkTimeOverlap(
          newStartTime,
          newEndTime,
          currentCaptions,
          caption.id
        );

        // 更新碰撞视觉反馈
        handleCollisionFeedback(itemEl, overlapResult.hasOverlap, "center");

        // 检查碰撞视觉反馈已在上面处理

        // 清空之前的临时调整更新
        tempAdjustmentUpdates = [];

        // 如果检测到重叠，限制当前字幕的移动范围，不修改其他字幕的duration
        if (overlapResult.hasOverlap) {
          console.log("中心拖拽: 检测到重叠，限制当前字幕移动范围");

          // 使用checkTimeOverlap返回的调整后的时间，这些时间已经避免了重叠
          newStartTime = overlapResult.startTime;
          newEndTime = overlapResult.endTime;

          console.log(`中心拖拽: 调整后时间 ${newStartTime} - ${newEndTime}`);

          // 不修改其他字幕，tempAdjustmentUpdates保持为空
        }

        // 最终验证：确保startTime永远不会大于endTime
        if (newStartTime >= newEndTime) {
          console.warn("检测到startTime >= endTime，进行修复");
          // 保持duration不变，调整时间
          const safeDuration = Math.max(duration, 100); // 最小100ms
          if (newStartTime < 0) {
            newStartTime = 0;
            newEndTime = safeDuration;
          } else {
            // 保持endTime，调整startTime
            newStartTime = Math.max(0, newEndTime - safeDuration);
          }
        }

        // 二次验证：确保时间合理性
        if (newStartTime < 0) {
          newStartTime = 0;
        }
        if (newEndTime <= newStartTime) {
          newEndTime = newStartTime + Math.max(duration, 100);
        }

        // 更新临时状态，不立即更新store数据
        tempStartTime = newStartTime;
        tempEndTime = newEndTime;

        // 直接更新当前字幕元素的视觉位置
        updateCaptionVisualPosition(itemEl, newStartTime, newEndTime, store);

        // 如果有临时调整更新，也要更新相关字幕的视觉位置
        tempAdjustmentUpdates.forEach(({ id, field, value }) => {
          const adjustmentItemEl = document.getElementById(
            `caption-item-${id}`
          );
          if (adjustmentItemEl && field === "endTime") {
            // 获取该字幕的当前startTime
            const targetCaption = getCurrentCaptions().find((c) => c.id === id);
            if (targetCaption) {
              const currentStartTime = timeStringToMs(targetCaption.startTime);
              updateCaptionVisualPosition(
                adjustmentItemEl,
                currentStartTime,
                value,
                store
              );
            }
          } else if (adjustmentItemEl && field === "startTime") {
            // 获取该字幕的当前endTime
            const targetCaption = getCurrentCaptions().find((c) => c.id === id);
            if (targetCaption) {
              const currentEndTime = timeStringToMs(targetCaption.endTime);
              updateCaptionVisualPosition(
                adjustmentItemEl,
                value,
                currentEndTime,
                store
              );
            }
          }
        });
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        setIsDragging(false);

        // 移除所有拖拽预览效果的类
        if (leftHandleEl) {
          leftHandleEl.classList.remove("handle-dragging");
          leftHandleEl.classList.remove("snapped");
        }

        if (rightHandleEl) {
          rightHandleEl.classList.remove("handle-dragging");
          rightHandleEl.classList.remove("snapped");
        }

        const itemEl = document.getElementById(`caption-item-${caption.id}`);
        if (itemEl) {
          // 移除所有可能的类
          itemEl.classList.remove("dragging-active");
          itemEl.classList.remove("resize-active");
          itemEl.classList.remove("panning-mode");
        }

        // 现在才真正更新字幕数据
        console.log("中心拖动结束，开始更新数据:");
        console.log("当前字幕新的时间:", tempStartTime, "-", tempEndTime);
        console.log("其他字幕的调整数量:", tempAdjustmentUpdates.length);

        // 最终验证：确保当前字幕的startTime < endTime
        let finalStartTime = tempStartTime;
        let finalEndTime = tempEndTime;

        if (finalStartTime >= finalEndTime) {
          console.warn(`中心拖动: 检测到startTime >= endTime，进行修复`);
          console.warn(
            `原始: startTime=${finalStartTime}, endTime=${finalEndTime}`
          );

          // 保持字幕的中心位置，调整持续时间为100ms
          const centerTime = (finalStartTime + finalEndTime) / 2;
          finalStartTime = Math.max(0, centerTime - 50);
          finalEndTime = finalStartTime + 100;

          console.warn(
            `修复后: startTime=${finalStartTime}, endTime=${finalEndTime}`
          );
        }

        // 首先更新当前字幕的时间
        const startTimeFormatted = formatCaptionTime(finalStartTime);
        const endTimeFormatted = formatCaptionTime(finalEndTime);
        store.updateCaptionTimeFrame(
          caption.id,
          startTimeFormatted,
          endTimeFormatted
        );

        // 中心拖拽不再修改其他字幕的时间，只移动当前字幕
        console.log("中心拖拽: 只更新当前字幕，不修改其他字幕的duration");

        // 拖拽结束后才进行排序
        store.captionManager.sortCaptionsByStartTime();

        // 对于中心拖动，我们不修改其他字幕，所以总是调用智能碰撞检测
        // 但由于我们已经在拖拽过程中限制了移动范围，这里应该不会有重叠
        store.captionManager.preventAllCaptionsOverlap();

        // 同步回store.captions
        store.captions = store.captionManager.captions;

        // 拖拽完成后，根据当前时间线指示器位置更新字幕显示
        // 这确保了字幕在时间范围外时能正确隐藏
        store.captionManager.updateCurrentCaption(store.currentTimeInMs);

        // 清除所有字幕元素的临时样式，让它们重新基于数据渲染
        getCurrentCaptions().forEach((cap) => {
          const capItemEl = document.getElementById(`caption-item-${cap.id}`);
          if (capItemEl) {
            capItemEl.style.left = "";
            capItemEl.style.width = "";
          }
        });

        // 强制刷新字幕轨道的字幕数组位置

        // 保存更改
        store.saveChange();
      };

      setIsDragging(true);
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      if (store.playing) {
        store.setPlaying(false);
      }

      event.stopPropagation();
      event.preventDefault();
    },
    [
      caption.id,
      startTimeMs,
      endTimeMs,
      containerWidth,
      isDragging,
      store,
      allCaptions,
    ]
  );

  return {
    handleLeftHandleDrag,
    handleRightHandleDrag,
    handleCenterDrag,
    isDragging,
    isSnappedStart,
    isSnappedEnd,
  };
};
