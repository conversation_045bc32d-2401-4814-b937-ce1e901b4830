import { Box, Tooltip } from "@mui/material";
import React, { useMemo } from "react";
import { ControlHandleProps } from "./types/types";
import { handleStyles } from "../styles";

export const ControlHandle = React.memo(
  ({ position, time, color, isVisible }: ControlHandleProps) => {
    // 提前返回，避免不必要的计算
    if (!isVisible) return null;

    // 使用useMemo优化样式计算
    const handleStyle = useMemo(
      () => ({
        ...handleStyles,
        position: "absolute",
        top: "50%",
        transform:
          position === "left"
            ? "translate(0%, -50%)"
            : "translate(-100%, -50%)",
        left: position === "left" ? "0%" : "100%",
        // 如果提供了颜色，则使用提供的颜色
        ...(color ? { borderColor: color } : {}),
      }),
      [position, color]
    );

    // 使用useMemo优化tooltip标题
    const tooltipTitle = useMemo(
      () => `${position === "left" ? "Start" : "End"}: ${time}`,
      [position, time]
    );

    return (
      <Tooltip title={tooltipTitle} arrow placement="top">
        <Box id={position} sx={handleStyle} />
      </Tooltip>
    );
  }
);
