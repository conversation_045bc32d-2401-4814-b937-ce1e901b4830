import { Box } from "@mui/material";
import { TextFields } from "@mui/icons-material";
import React, { useMemo } from "react";
import { CaptionContentProps } from "./types/types";
import { captionContentStyles } from "../styles";
import SubtitlesIcon from "@mui/icons-material/Subtitles";

const CaptionContentComponent = ({
  caption,
  handleClick,
  handleDoubleClick,
}: CaptionContentProps) => {
  const isSelected = caption.isSelected === true;

  // 使用useMemo优化内部Box样式计算
  const innerBoxStyle = useMemo(
    () => ({
      display: "flex",
      alignItems: "center",
      zIndex: 2,
      position: "relative",
      width: "100%",
    }),
    []
  );

  // 使用useMemo优化文本样式计算
  const textStyle = useMemo(
    () => ({
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
      width: "100%",
    }),
    []
  );

  // 使用useMemo优化图标样式计算
  const iconStyle = useMemo(
    () => ({
      mr: 0.5,
      opacity: 0.9,
    }),
    []
  );

  // 优化主容器样式计算
  const containerStyle = useMemo(
    () => captionContentStyles(isSelected),
    [isSelected]
  );

  return (
    <Box
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      sx={containerStyle}
    >
      <Box sx={innerBoxStyle}>
        <SubtitlesIcon fontSize="small" sx={iconStyle} />
        <span style={textStyle}>{caption.text}</span>
      </Box>
    </Box>
  );
};

export const CaptionContent = React.memo(
  CaptionContentComponent,
  (prevProps, nextProps) => {
    // 精确比较props，避免不必要的重渲染
    return (
      prevProps.caption.id === nextProps.caption.id &&
      prevProps.caption.text === nextProps.caption.text &&
      prevProps.caption.isSelected === nextProps.caption.isSelected &&
      prevProps.handleClick === nextProps.handleClick &&
      prevProps.handleDoubleClick === nextProps.handleDoubleClick
    );
  }
);
