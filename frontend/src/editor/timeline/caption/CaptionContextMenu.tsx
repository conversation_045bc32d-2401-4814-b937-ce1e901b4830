import React from "react";
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import {
  Delete,
  Edit,
  ContentCopy,
  Translate,
  TextFields,
} from "@mui/icons-material";
import { StoreContext } from "../../../store";
import { Caption } from "../../../types";
import {
  contextMenuStyles,
  contextMenuItemStyles,
  menuDividerStyles,
} from "../styles";
import { useLanguage } from "../../../i18n/LanguageContext";

interface CaptionContextMenuProps {
  caption: Caption;
  contextMenu: {
    mouseX: number;
    mouseY: number;
  } | null;
  handleClose: () => void;
}

const CaptionContextMenu: React.FC<CaptionContextMenuProps> = ({
  caption,
  contextMenu,
  handleClose,
}) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();

  const handleDelete = () => {
    store.deleteCaption(caption.id);
    handleClose();
  };

  const handleEdit = () => {
    // 选中当前字幕
    store.captionManager.selectCaption(caption.id);
    handleClose();
  };

  const handleCopy = () => {
    // 目前只关闭菜单，实际复制功能需要根据实际情况实现
    handleClose();
  };

  return (
    <Menu
      open={contextMenu !== null}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu !== null
          ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
          : undefined
      }
      PaperProps={{ sx: contextMenuStyles.paper }}
    >
      <MenuItem onClick={handleEdit} sx={contextMenuItemStyles}>
        <ListItemIcon>
          <Edit fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_edit")}</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCopy} sx={contextMenuItemStyles}>
        <ListItemIcon>
          <ContentCopy fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_copy")}</ListItemText>
      </MenuItem>
      <Divider sx={menuDividerStyles} />
      <MenuItem
        onClick={() => {
          handleClose();
        }}
        sx={contextMenuItemStyles}
      >
        <ListItemIcon>
          <TextFields fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_adjust_style")}</ListItemText>
      </MenuItem>
      <MenuItem
        onClick={() => {
          handleClose();
        }}
        sx={contextMenuItemStyles}
      >
        <ListItemIcon>
          <Translate fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_auto_translate")}</ListItemText>
      </MenuItem>
      <Divider sx={menuDividerStyles} />
      <MenuItem onClick={handleDelete} sx={contextMenuItemStyles}>
        <ListItemIcon>
          <Delete fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_delete")}</ListItemText>
      </MenuItem>
    </Menu>
  );
};

export default CaptionContextMenu;
