import { Caption } from "../../../../types";

export interface CaptionsTrackViewProps {
  captions: Caption[];
}

export interface CaptionContentProps {
  caption: Caption;
  handleClick: (e: React.MouseEvent) => void;
  handleDoubleClick: (e: React.MouseEvent) => void;
}

export interface DurationLabelProps {
  startTime: string;
  endTime: string;
  isVisible: boolean;
}

export interface ControlHandleProps {
  position: "left" | "right";
  time: string;
  color: string;
  isVisible: boolean;
}

export interface CaptionItemProps {
  caption: Caption;
  containerWidth: number | null;
  handleTimeFrameChange: (caption: Caption, start: number, end: number) => void;
  allCaptions: Caption[];
}
