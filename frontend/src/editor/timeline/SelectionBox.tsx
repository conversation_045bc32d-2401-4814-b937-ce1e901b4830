"use client";
import { Box } from "@mui/material";
import { observer } from "mobx-react";

interface SelectionBoxProps {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  isVisible: boolean;
}

/**
 * 时间线框选组件
 * 显示一个半透明的选择框，用于框选时间线元素
 */
export const SelectionBox = observer(
  ({ startX, startY, endX, endY, isVisible }: SelectionBoxProps) => {
    if (!isVisible) {
      return null;
    }

    // 计算选择框的位置和尺寸
    const left = Math.min(startX, endX);
    const top = Math.min(startY, endY);
    const width = Math.abs(endX - startX);
    const height = Math.abs(endY - startY);

    return (
      <Box
        sx={{
          position: "absolute",
          left: `${left}px`,
          top: `${top}px`,
          width: `${width}px`,
          height: `${height}px`,
          backgroundColor: "rgba(33, 150, 243, 0.1)", // 蓝色半透明背景
          border: "1px solid rgba(33, 150, 243, 0.6)", // 蓝色边框
          borderRadius: "2px",
          pointerEvents: "none", // 不阻止鼠标事件
          zIndex: 1000, // 确保在最上层
          boxSizing: "border-box",
          // 添加一些视觉效果
          boxShadow: "0 0 0 1px rgba(33, 150, 243, 0.2)",
          backdropFilter: "blur(1px)",
        }}
      />
    );
  }
);

// 设置组件显示名称
(SelectionBox as any).displayName = "SelectionBox";
