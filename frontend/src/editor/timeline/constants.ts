// 导入统一的常量定义
import {
  CONSTANTS,
  COLOR_CONSTANTS,
  DRAG_CONSTANTS as STORE_DRAG_CONSTANTS,
  SNAP_CONSTANTS as STORE_SNAP_CONSTANTS,
  ELEMENT_CONTENT_MAP as STORE_ELEMENT_CONTENT_MAP,
} from "../../store/constants";

// 时间线高度常量
export const TIMELINE_HEIGHT = CONSTANTS.TIMELINE.HEIGHT;
export const TRACK_HEIGHT = CONSTANTS.TIMELINE.TRACK_HEIGHT;

// 元素类型到图标/颜色的映射
export const ELEMENT_COLORS = COLOR_CONSTANTS.ELEMENT_COLORS;

// 轨道类型对应的颜色
export const TRACK_COLORS = COLOR_CONSTANTS.TRACK_COLORS;

// 字幕颜色
export const CAPTION_COLOR = COLOR_CONSTANTS.CAPTION_COLOR;

// 元素内容渲染映射
export const ELEMENT_CONTENT_MAP = STORE_ELEMENT_CONTENT_MAP;

// 拖拽相关常量
export const DRAG_CONSTANTS = STORE_DRAG_CONSTANTS;

// 吸附相关常量
export const SNAP_CONSTANTS = STORE_SNAP_CONSTANTS;

// 最小元素持续时间（毫秒）
export const MIN_ELEMENT_DURATION = CONSTANTS.TIMELINE.MIN_ELEMENT_DURATION;
