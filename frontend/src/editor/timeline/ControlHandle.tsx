import { Box, Tooltip } from "@mui/material";
import React from "react";

interface ControlHandleProps {
  position: "left" | "right";
  time: string;
  handleStyles: any;
  isSnapped?: boolean;
}

/**
 * 控制把手组件
 * 用于调整元素的开始和结束时间
 */
export const ControlHandle: React.FC<ControlHandleProps> = ({
  position,
  time,
  handleStyles,
  isSnapped = false,
}) => {
  return (
    <Tooltip
      title={
        <Box sx={{ fontWeight: "bold" }}>
          {position === "left" ? "Start: " : "End: "}
          {time}
        </Box>
      }
      arrow
      placement="top"
      enterDelay={500}
      leaveDelay={200}
    >
      <Box
        id={`${position}-handle`}
        sx={{
          ...handleStyles,
          [position]: 0,
          transform: "translate(0%, -50%)",
          borderColor: isSnapped ? "#ff9800" : "#2196f3",
          boxShadow: isSnapped ? "0 0 8px rgba(255,152,0,0.5)" : "none",
        }}
      />
    </Tooltip>
  );
};
