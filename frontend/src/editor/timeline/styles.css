/* 性能优化通用类 */
.gpu-accelerated {
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 拖拽相关样式 - 优化性能 */
.handle-dragging {
  transform: translate3d(0, -50%, 0) scale(1.2) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  will-change: transform;
}

.snapped,
.snapped-start,
.snapped-end {
  border-color: #ff9800 !important;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.5) !important;
  will-change: border-color, box-shadow;
}

.resize-active {
  z-index: 30 !important;
  outline: 1px dashed rgba(33, 150, 243, 0.8) !important;
  will-change: outline;
}

.dragging-active {
  z-index: 30 !important;
  cursor: grabbing !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  transform: translate3d(0, 0, 0) scale(1.01) !important;
  will-change: transform, box-shadow;
}

.panning-mode {
  background-color: rgba(255, 152, 0, 0.2) !important;
  border: 1px dashed #ff9800 !important;
  will-change: background-color, border;
}

/* 选中元素样式 */
.element-selected {
  border: 2px dashed rgba(33, 150, 243, 0.6) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  z-index: 20 !important;
  will-change: border, box-shadow;
}

/* 吸附指示器样式 */
.snap-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #ff9800;
  border-radius: 2px;
  opacity: 0.8;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.5);
  z-index: 30;
  will-change: opacity;
  pointer-events: none; /* 防止干扰鼠标事件 */
}

.snap-indicator-left {
  left: 0;
}

.snap-indicator-right {
  right: 0;
}

/* 基础类用于所有时间线元素 */
.timeline-element-container {
  will-change: transform;
  transform: translate3d(0, 0, 0);
  pointer-events: auto;
  border-radius: 6px;
}

/* 性能优化容器类 */
.timeline-container,
.timeline-elements-container,
.timeline-elements-list {
  contain: content; /* 包含内容，提高性能 */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.timeline-container {
  -webkit-overflow-scrolling: touch; /* 在iOS上启用惯性滚动 */
  overscroll-behavior: contain; /* 防止滚动传播 */
  border-radius: 8px;
}

.timeline-elements-container {
  contain: layout style; /* 包含布局和样式变化 */
  border-radius: 6px;
}

/* 轨道样式美化 */
.timeline-track {
  border-radius: 6px;
  margin: 4px 0;
  transition: background-color 0.2s ease;
}

/* 轨道标题美化 */
.track-title {
  font-weight: 500;
  color: #3a3a3a;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.03);
  margin-right: 8px;
}

/* 美化时间线元素 */
.timeline-element {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.timeline-element:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* 时间线元素内容 */
.element-content {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 字幕元素样式美化 */
.caption-element {
  background-color: rgba(156, 39, 176, 0.8);
  border-radius: 6px;
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
}

.caption-element:hover {
  background-color: rgba(156, 39, 176, 0.9);
  box-shadow: 0 3px 6px rgba(156, 39, 176, 0.4);
}

/* 控制把手美化 */
.control-handle {
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: transform 0.15s ease;
}

.control-handle:hover {
  transform: scale(1.2);
}

/* 时间线高度调整手柄样式 */
.timeline-resize-handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  cursor: ns-resize;
  background-color: transparent;
  transition: background-color 0.2s ease;
  z-index: 1000;
}

.timeline-resize-handle:hover {
  background-color: rgba(25, 118, 210, 0.3);
}

.timeline-resize-handle.resizing {
  background-color: rgba(25, 118, 210, 0.6);
}

.timeline-resize-handle::before {
  content: "";
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 8px;
  background-color: transparent;
}

/* 右键菜单样式 */
.context-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.context-menu-item {
  padding: 10px 16px;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: rgba(33, 150, 243, 0.08);
}
