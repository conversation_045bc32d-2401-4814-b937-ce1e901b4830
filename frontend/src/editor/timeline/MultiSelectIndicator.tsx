"use client";
import React from "react";
import { Box, Typography, Chip } from "@mui/material";
import { observer } from "mobx-react";
import { useContext } from "react";
import { StoreContext } from "../../store";
import { SelectAll, Clear } from "@mui/icons-material";

/**
 * 多选状态指示器组件
 * 显示当前选中元素的数量和提供清除选择的功能
 */
export const MultiSelectIndicator = observer(() => {
  const store = useContext(StoreContext);

  // 如果没有多选元素，不显示指示器
  if (store.selectedElements.length === 0) {
    return null;
  }

  const handleClearSelection = () => {
    store.clearAllSelections();
  };

  return (
    <Box
      sx={{
        position: "fixed",
        top: 16,
        right: 16,
        zIndex: 1000,
        display: "flex",
        alignItems: "center",
        gap: 1,
        bgcolor: "rgba(255, 152, 0, 0.9)",
        color: "white",
        px: 2,
        py: 1,
        borderRadius: 2,
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        backdropFilter: "blur(8px)",
        border: "1px solid rgba(255, 255, 255, 0.2)",
      }}
    >
      <SelectAll sx={{ fontSize: 20 }} />
      <Typography variant="body2" fontWeight="medium">
        已选中 {store.selectedElements.length} 个元素
      </Typography>
      <Chip
        label="清除"
        size="small"
        variant="outlined"
        deleteIcon={<Clear />}
        onDelete={handleClearSelection}
        onClick={handleClearSelection}
        sx={{
          color: "white",
          borderColor: "rgba(255, 255, 255, 0.5)",
          "& .MuiChip-deleteIcon": {
            color: "rgba(255, 255, 255, 0.8)",
            "&:hover": {
              color: "white",
            },
          },
          "&:hover": {
            borderColor: "white",
            bgcolor: "rgba(255, 255, 255, 0.1)",
          },
        }}
      />
    </Box>
  );
});

// 设置组件显示名称
(MultiSelectIndicator as any).displayName = "MultiSelectIndicator";
