import { ReactNode } from "react";

// 元素内容组件属性
export interface ElementContentProps {
  element: any;
}

// 持续时间标签属性
export interface DurationLabelProps {
  duration: string;
  isVisible: boolean;
}

// 控制把手属性
export interface ControlHandleProps {
  position: "left" | "right";
  time: string;
  handleStyles: React.CSSProperties & {
    "&:hover"?: React.CSSProperties;
    [key: string]: any;
  };
}

// 主时间帧视图组件的属性
export interface TimeFrameViewProps {
  element: any;
  containerWidth?: number | null;
  handleTimeFrameChange?: (element: any, start: number, end: number) => void;
  allElements?: any[];
  isDraggable?: boolean;
}
