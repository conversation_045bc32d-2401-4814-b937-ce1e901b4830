/* 修改拖拽样式，使其只高亮元素本身而不是整行 */
.timeline-item-dragging {
  opacity: 0.95;
  background-color: rgba(33, 150, 243, 0.15);
  box-shadow: 0 0 15px rgba(33, 150, 243, 0.4) !important;
  border: 2px dashed rgba(33, 150, 243, 0.6) !important;
  position: relative;
  z-index: 100 !important;
  will-change: transform, opacity, box-shadow;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
}

/* 垂直排序时的样式 */
.timeline-item-container.sorting {
  cursor: ns-resize;
  background-color: rgba(156, 39, 176, 0.1);
  border-radius: 4px;
  will-change: transform, background-color;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
}

/* 添加排序区域的指示样式 */
.timeline-item-container {
  position: relative;
  padding-left: 10px; /* 为排序把手预留空间 */
  will-change: transform, opacity, background-color;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  transition: opacity 0.2s ease;
}

/* 排序把手样式 */
.timeline-sort-handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 10px;
  height: 100%;
  background-color: #b0aeae;
  border-radius: 4px 0 0 4px;
  opacity: 0.6;
  cursor: ns-resize;
  transition: background-color 0.2s, opacity 0.2s;
  will-change: background-color, opacity;
}

.timeline-sort-handle:hover,
.timeline-item-container.sorting .timeline-sort-handle {
  background-color: #9c27b0;
  opacity: 0.8;
}

/* 添加拖动指示器图标 */
.timeline-sort-handle::before {
  content: "\2261"; /* 三横线图标 */
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 水平拖拽元素时的样式 */
.timeline-item-container .MuiBox-root[role="slider"] {
  cursor: ew-resize !important;
}

/* 添加全局排序模式样式 */
body.timeline-sorting-active .timeline-item-container:not(.sorting) {
  opacity: 0.7;
  transition: opacity 0.2s;
}

/* 轨道拖拽样式 */
.track-dragging {
  opacity: 0.8;
  background-color: rgba(33, 150, 243, 0.15);
  box-shadow: 0 0 15px rgba(33, 150, 243, 0.4) !important;
  border: 2px dashed rgba(33, 150, 243, 0.6) !important;
  position: relative;
  z-index: 100 !important;
  transform: translate3d(0, 0, 0) scale(1.02);
  pointer-events: none;
  will-change: transform, opacity, box-shadow;
  backface-visibility: hidden;
}

/* 轨道高亮样式 */
.track-highlight {
  background-color: rgba(33, 150, 243, 0.2) !important;
  border: 2px dashed rgba(33, 150, 243, 0.6) !important;
  box-shadow: inset 0 0 10px rgba(33, 150, 243, 0.3) !important;
  transition: all 0.2s ease;
  will-change: background-color, box-shadow;
}

/* 类型不匹配样式 */
.track-type-mismatch {
  background-color: rgba(244, 67, 54, 0.1) !important;
  border: 2px dashed rgba(244, 67, 54, 0.8) !important;
  box-shadow: inset 0 0 10px rgba(244, 67, 54, 0.3) !important;
  animation: pulse-mismatch 1.5s infinite alternate;
  will-change: box-shadow;
  transform: translateZ(0);
}

/* 类型不匹配脉冲动画 - 只改变box-shadow和opacity，不改变大小 */
@keyframes pulse-mismatch {
  from {
    opacity: 0.9;
    box-shadow: inset 0 0 5px rgba(244, 67, 54, 0.2);
  }
  to {
    opacity: 1;
    box-shadow: inset 0 0 15px rgba(244, 67, 54, 0.4);
  }
}

/* 全局轨道拖拽模式 */
body.track-dragging-active .timeline-track:not(.track-highlight) {
  opacity: 0.8;
  transition: opacity 0.2s;
}

/* 垂直拖拽指示器 */
.vertical-drag-indicator {
  position: absolute;
  top: 0;
  left: 50%;
  width: 2px;
  height: 10px;
  background-color: #2196f3;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.2s, height 0.2s;
  pointer-events: none;
  z-index: 10;
  will-change: opacity, transform, height;
}

.timeline-element-container:hover .vertical-drag-indicator {
  opacity: 0.8;
  height: 15px;
}

/* 垂直拖拽样式 */
.vertical-dragging {
  cursor: ns-resize !important;
  transform: translate3d(0, 0, 0) scale(1.02);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  z-index: 1000 !important;
  border: 2px dashed rgba(33, 150, 243, 0.6) !important;
  background-color: rgba(33, 150, 243, 0.15);
  transition: transform 0.1s ease, box-shadow 0.1s ease;
  will-change: transform, box-shadow, opacity;
  backface-visibility: hidden;
}

/* 拖拽中的元素样式 */
.element-dragging {
  cursor: grabbing !important;
  opacity: 0.9;
  transform: translate3d(0, 0, 0) scale(1.02);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  z-index: 1000 !important;
  pointer-events: none;
  will-change: transform, opacity, box-shadow;
  backface-visibility: hidden;
}

/* 轨道间隔样式 */
.track-gap {
  position: relative;
  transition: all 0.2s ease;
  will-change: transform, opacity, background-color;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
}

/* 轨道间隔高亮样式 - 不改变高度，使用transform缩放 */
.track-gap-highlight {
  position: relative;
  animation: pulse-gap 1.5s infinite alternate;
  will-change: transform, box-shadow, opacity, background-color;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
}

/* 添加视觉高亮效果的伪元素 */
.track-gap-highlight::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 16px;
  transform: translateY(-50%);
  background-color: rgba(33, 150, 243, 0.1);
  border: 2px dashed rgba(33, 150, 243, 0.6);
  box-shadow: inset 0 0 10px rgba(33, 150, 243, 0.2);
  z-index: -1;
  pointer-events: none;
  will-change: opacity, box-shadow;
}

/* 轨道间隔中心线 */
.track-gap::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 2px;
  transform: translateY(-50%);
  background-color: transparent;
  transition: background-color 0.2s ease;
  will-change: background-color;
}

/* 轨道间隔高亮中心线 */
.track-gap-highlight::before {
  background-color: #2196f3;
  height: 2px; /* 保持与轨道间隔相同的高度 */
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.5);
  z-index: 1; /* 确保在伪元素之上 */
}

/* 轨道间隔脉冲动画 - 使用transform和opacity代替box-shadow变化 */
@keyframes pulse-gap {
  from {
    opacity: 0.8;
    transform: translateZ(0) scale(1);
    box-shadow: inset 0 0 5px rgba(33, 150, 243, 0.2);
  }
  to {
    opacity: 1;
    transform: translateZ(0) scale(1.02);
    box-shadow: inset 0 0 15px rgba(33, 150, 243, 0.4);
  }
}

/* 添加时间线基础容器性能优化 */
.timeline-container {
  will-change: transform;
  transform: translateZ(0);
}

/* 添加时间线轨道性能优化 */
.timeline-track {
  will-change: transform, opacity;
  transform: translateZ(0);
  transition: opacity 0.2s ease;
}
