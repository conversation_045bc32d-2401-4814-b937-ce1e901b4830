import React from "react";
import {
  <PERSON>u,
  <PERSON>uItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import {
  ContentPaste,
  DeleteSweep,
  Add,
} from "@mui/icons-material";
import { StoreContext } from "../../../store";
import {
  contextMenuStyles,
  contextMenuItemStyles,
  menuDividerStyles,
} from "../styles";
import { useLanguage } from "../../../i18n/LanguageContext";

interface CaptionTrackContextMenuProps {
  contextMenu: {
    mouseX: number;
    mouseY: number;
  } | null;
  handleClose: () => void;
}

const CaptionTrackContextMenu: React.FC<CaptionTrackContextMenuProps> = ({
  contextMenu,
  handleClose,
}) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();

  // Check if there are any captions
  const hasCaptions = React.useMemo(() => {
    return store.captions && store.captions.length > 0;
  }, [store.captions]);

  // Check if there are any copied/cut elements (for paste functionality)
  const hasClipboardElement = React.useMemo(() => {
    // Here we only check if there is a currently selected element, can be extended to check the actual clipboard
    return store.selectedElement !== null;
  }, [store.selectedElement]);

  const handlePaste = () => {
    if (!store.selectedElement) return;

    // Copy the selected element
    const element = store.selectedElement;

    // Create a new element (clone the selected element)
    store.cloneElement(element.id);

    // Get the latest created element (the cloned element should be the last one in the editorElements array)
    const newElement = store.editorElements[store.editorElements.length - 1];

    if (newElement) {
      // Set the start time of the new element to the current indicator position
      const indicatorTime = store.currentTimeInMs;
      const duration = newElement.timeFrame.end - newElement.timeFrame.start;

      store.updateEditorElementTimeFrame(
        newElement,
        {
          start: indicatorTime,
          end: indicatorTime + duration,
        },
        true
      );

      // Select the new element
      store.setSelectedElement(newElement);
    }

    handleClose();
  };

  const handleAddCaption = () => {
    // Add a new caption at the current time
    store.addCaption();
    handleClose();
  };

  const handleDeleteAllCaptions = () => {
    if (!hasCaptions) {
      handleClose();
      return;
    }

    // 直接清除所有字幕
    store.clearAllCaptions();

    // 清除选中状态
    store.deselectAllCaptions();

    handleClose();
  };

  return (
    <Menu
      open={contextMenu !== null}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu !== null
          ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
          : undefined
      }
      PaperProps={{ sx: contextMenuStyles.paper }}
    >
      <MenuItem onClick={handleAddCaption} sx={contextMenuItemStyles}>
        <ListItemIcon>
          <Add fontSize="small" />
        </ListItemIcon>
        <ListItemText>Add Caption</ListItemText>
      </MenuItem>

      <MenuItem
        onClick={handlePaste}
        sx={contextMenuItemStyles}
        disabled={!hasClipboardElement}
      >
        <ListItemIcon>
          <ContentPaste fontSize="small" />
        </ListItemIcon>
        <ListItemText>Paste</ListItemText>
      </MenuItem>

      <Divider sx={menuDividerStyles} />

      <MenuItem
        onClick={handleDeleteAllCaptions}
        sx={{
          ...contextMenuItemStyles,
          color: hasCaptions ? "#f44336" : "rgba(0, 0, 0, 0.26)",
          "&:hover": {
            backgroundColor: hasCaptions ? "rgba(244, 67, 54, 0.08)" : "transparent",
          },
        }}
        disabled={!hasCaptions}
      >
        <ListItemIcon>
          <DeleteSweep 
            fontSize="small" 
            sx={{ 
              color: hasCaptions ? "#f44336" : "rgba(0, 0, 0, 0.26)" 
            }} 
          />
        </ListItemIcon>
        <ListItemText>Delete All Captions</ListItemText>
      </MenuItem>
    </Menu>
  );
};

export default CaptionTrackContextMenu;
