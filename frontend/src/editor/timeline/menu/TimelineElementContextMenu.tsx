import React from "react";
import {
  <PERSON>u,
  <PERSON>uItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import {
  Delete,
  ContentCut,
  ContentCopy,
  Edit,
  Share,
  Visibility,
} from "@mui/icons-material";
import { StoreContext } from "../../../store";
import { EditorElement } from "../../../types";
import {
  contextMenuStyles,
  contextMenuItemStyles,
  menuDividerStyles,
} from "../styles";
import { useLanguage } from "../../../i18n/LanguageContext";

interface TimelineElementContextMenuProps {
  element: EditorElement; // Use EditorElement type instead of any
  contextMenu: {
    mouseX: number;
    mouseY: number;
  } | null;
  handleClose: () => void;
}

const TimelineElementContextMenu: React.FC<TimelineElementContextMenuProps> = ({
  element,
  contextMenu,
  handleClose,
}) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();

  const handleDelete = () => {
    handleClose();
    console.log(
      "TimelineElementContextMenu - handleDelete called for element:",
      element.id
    );
    store.deleteElement(element.id);
  };

  const handleCut = () => {
    handleClose();
    // Temporarily use select and delete as a substitute for cut functionality
    console.log(
      "TimelineElementContextMenu - handleCut called for element:",
      element.id
    );
    store.setSelectedElement(element);
    store.deleteElement(element.id);
  };

  const handleCopy = () => {
    // Temporarily use clone functionality as a substitute for copy functionality
    store.cloneElement(element.id);
    handleClose();
  };

  const handleEdit = () => {
    store.setSelectedElement(element);
    store.handleSeek(element.timeFrame.start);
    handleClose();
  };

  return (
    <Menu
      open={contextMenu !== null}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu !== null
          ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
          : undefined
      }
      PaperProps={{ sx: contextMenuStyles.paper }}
    >
      <MenuItem onClick={handleEdit} sx={contextMenuItemStyles}>
        <ListItemIcon>
          <Edit fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_edit")}</ListItemText>
      </MenuItem>
      <Divider sx={menuDividerStyles} />
      <MenuItem onClick={handleCopy} sx={contextMenuItemStyles}>
        <ListItemIcon>
          <ContentCopy fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_copy")}</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCut} sx={contextMenuItemStyles}>
        <ListItemIcon>
          <ContentCut fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_cut")}</ListItemText>
      </MenuItem>
      <Divider sx={menuDividerStyles} />
      <MenuItem onClick={handleDelete} sx={contextMenuItemStyles}>
        <ListItemIcon>
          <Delete fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_delete")}</ListItemText>
      </MenuItem>
      {/* <MenuItem
        onClick={() => {
          handleClose();
        }}
        sx={contextMenuItemStyles}
      >
        <ListItemIcon>
          <Visibility fontSize="small" />
        </ListItemIcon>
        <ListItemText>{t("timeline_preview")}</ListItemText>
      </MenuItem> */}
    </Menu>
  );
};

export default TimelineElementContextMenu;
