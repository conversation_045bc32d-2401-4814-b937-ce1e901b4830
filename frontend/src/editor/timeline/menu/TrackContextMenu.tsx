import React from "react";
import {
  <PERSON>u,
  <PERSON>uItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import { ContentPaste, DeleteSweep } from "@mui/icons-material";
import { StoreContext } from "../../../store";
import { Track } from "../../../types";
import {
  contextMenuStyles,
  contextMenuItemStyles,
  menuDividerStyles,
} from "../styles";

interface TrackContextMenuProps {
  track: Track;
  contextMenu: {
    mouseX: number;
    mouseY: number;
  } | null;
  handleClose: () => void;
}

const TrackContextMenu: React.FC<TrackContextMenuProps> = ({
  track,
  contextMenu,
  handleClose,
}) => {
  const store = React.useContext(StoreContext);

  // Check if there are any copied/cut elements
  const hasClipboardElement = React.useMemo(() => {
    // Here we only check if there is a currently selected element, can be extended to check the actual clipboard
    return store.selectedElement !== null;
  }, [store.selectedElement]);

  // Check if the track has any elements
  const hasElementsOnTrack = React.useMemo(() => {
    const elementsOnTrack = store.trackManager.getElementsByTrackId(track.id);
    return elementsOnTrack.length > 0;
  }, [store.trackManager, track.id]);

  const handlePaste = () => {
    if (!store.selectedElement) return;

    // Copy the selected element
    const element = store.selectedElement;

    // Create a new element (clone the selected element)
    store.cloneElement(element.id);

    // Get the latest created element (the cloned element should be the last one in the editorElements array)
    const newElement = store.editorElements[store.editorElements.length - 1];

    if (newElement) {
      // Move the new element to the current track
      store.trackManager.moveElementToTrack(newElement.id, track.id);

      // Set the start time of the new element to the current indicator position
      const indicatorTime = store.currentTimeInMs;
      const duration = newElement.timeFrame.end - newElement.timeFrame.start;

      store.updateEditorElementTimeFrame(
        newElement,
        {
          start: indicatorTime,
          end: indicatorTime + duration,
        },
        true
      );

      // Select the new element
      store.setSelectedElement(newElement);
    }

    handleClose();
  };

  const handleDeleteAllOnTrack = () => {
    // 获取轨道上的所有元素
    const elementsOnTrack = store.trackManager.getElementsByTrackId(track.id);

    if (elementsOnTrack.length === 0) {
      handleClose();
      return;
    }

    // 直接批量删除元素
    elementsOnTrack.forEach((element) => {
      store.deleteElement(element.id);
    });

    // 清除选中状态
    store.setSelectedElement(null);

    handleClose();
  };

  return (
    <Menu
      open={contextMenu !== null}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu !== null
          ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
          : undefined
      }
      PaperProps={{ sx: contextMenuStyles.paper }}
    >
      <MenuItem
        onClick={handlePaste}
        sx={contextMenuItemStyles}
        disabled={!hasClipboardElement}
      >
        <ListItemIcon>
          <ContentPaste fontSize="small" />
        </ListItemIcon>
        <ListItemText>Paste</ListItemText>
      </MenuItem>

      <Divider sx={menuDividerStyles} />

      <MenuItem
        onClick={handleDeleteAllOnTrack}
        sx={{
          ...contextMenuItemStyles,
          color: hasElementsOnTrack ? "#f44336" : "rgba(0, 0, 0, 0.26)",
          "&:hover": {
            backgroundColor: hasElementsOnTrack
              ? "rgba(244, 67, 54, 0.08)"
              : "transparent",
          },
        }}
        disabled={!hasElementsOnTrack}
      >
        <ListItemIcon>
          <DeleteSweep
            fontSize="small"
            sx={{
              color: hasElementsOnTrack ? "#f44336" : "rgba(0, 0, 0, 0.26)",
            }}
          />
        </ListItemIcon>
        <ListItemText>Delete All on Track</ListItemText>
      </MenuItem>

      {/* <MenuItem
        onClick={() => {
          handleClose();
        }}
        sx={contextMenuItemStyles}
      >
        <ListItemIcon>
          <AddCircleOutline fontSize="small" />
        </ListItemIcon>
        <ListItemText>Add New Element</ListItemText>
      </MenuItem> */}
    </Menu>
  );
};

export default TrackContextMenu;
