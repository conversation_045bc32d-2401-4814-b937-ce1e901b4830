"use client";
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { getTimelineContainerWidth } from "../../utils/timeUtils";

// 定义上下文类型
interface TimelineContextType {
  containerWidth: number | null;
  updateContainerWidth: () => void;
}

// 创建上下文
const TimelineContext = createContext<TimelineContextType>({
  containerWidth: null,
  updateContainerWidth: () => {},
});

// 上下文提供者组件
export const TimelineProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [containerWidth, setContainerWidth] = useState<number | null>(null);

  // 更新容器宽度的函数
  const updateContainerWidth = useCallback(() => {
    const width = getTimelineContainerWidth();
    setContainerWidth(width);
  }, []);

  // 初始化和窗口大小变化时更新宽度
  useEffect(() => {
    updateContainerWidth();

    // 添加窗口大小变化监听器
    window.addEventListener("resize", updateContainerWidth);

    // 清理函数
    return () => {
      window.removeEventListener("resize", updateContainerWidth);
    };
  }, [updateContainerWidth]);

  // 提供上下文值
  const contextValue = {
    containerWidth,
    updateContainerWidth,
  };

  return (
    <TimelineContext.Provider value={contextValue}>
      {children}
    </TimelineContext.Provider>
  );
};

// 自定义钩子，用于访问上下文
export const useTimelineContext = () => useContext(TimelineContext);
