import { alpha } from "@mui/material";
import { SNAP_CONSTANTS, TRACK_HEIGHT } from "./constants";

// 通用高度常量
export const CAPTION_HEIGHT = "30px";
export const ELEMENT_HEIGHT = TRACK_HEIGHT;
// 导出字幕颜色常量
export const CAPTION_COLOR = "#9c27b0"; // 紫色

// 性能优化常量 - 预计算常用的透明色值
export const CAPTION_COLOR_ALPHA_03 = alpha(CAPTION_COLOR, 0.3);
export const CAPTION_COLOR_ALPHA_08 = alpha(CAPTION_COLOR, 0.8);
export const SNAP_COLOR_ALPHA_02 = alpha(SNAP_CONSTANTS.SNAP_COLOR, 0.2);
export const SNAP_COLOR_ALPHA_05 = alpha(SNAP_CONSTANTS.SNAP_COLOR, 0.5);
export const SNAP_COLOR_ALPHA_07 = alpha(SNAP_CONSTANTS.SNAP_COLOR, 0.7);
export const BLUE_ALPHA_03 = alpha("#2196f3", 0.3);

// 通用控制把手样式
export const handleStyles = {
  bgcolor: "white",
  border: 2,
  borderColor: "#2196f3", // 默认使用蓝色
  borderRadius: "2px",
  width: "8px", // 增加宽度，更易点击
  height: "16px", // 增加高度，更易点击
  cursor: "ew-resize",
  transition: "transform 0.15s", // 优化：只对transform应用过渡效果
  position: "absolute",
  top: "50%",
  zIndex: 50,
  transform: "translateY(-50%)",
  willChange: "transform, border-color", // 性能优化：提示浏览器
  "&:hover": {
    bgcolor: BLUE_ALPHA_03,
  },
  "&.handle-dragging": {
    bgcolor: BLUE_ALPHA_03,
    width: "10px",
    height: "20px",
  },
  "&.snapped": {
    borderColor: SNAP_CONSTANTS.SNAP_COLOR,
    bgcolor: SNAP_COLOR_ALPHA_02,
    boxShadow: `0 0 8px ${SNAP_COLOR_ALPHA_05}`,
  },
};

// 字幕控制把手样式
export const captionHandleStyles = {
  ...handleStyles,
  borderColor: CAPTION_COLOR,
  "&:hover": {
    bgcolor: CAPTION_COLOR_ALPHA_03,
  },
  "&.handle-dragging": {
    bgcolor: CAPTION_COLOR_ALPHA_03,
    width: "10px",
    height: "20px",
  },
};

// 通用内容样式函数 - 优化减少计算
export const contentStyles = (color: string, isSelected: boolean) => ({
  height: "100%",
  width: "100%",
  bgcolor: isSelected ? color : alpha(color, 0.8),
  color: "white",
  fontSize: "0.75rem",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  pl: 1,
  pr: 1,
  borderRadius: "4px",
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  zIndex: 5,
  cursor: "move",
  transition: "background-color 0.2s", // 优化：只对背景色应用过渡效果
  willChange: "background-color", // 性能优化：提示浏览器
  "&:hover": {
    bgcolor: color,
  },
  userSelect: "none",
});

// 字幕内容样式 - 使用预计算的颜色值
export const captionContentStyles = (isSelected: boolean) => ({
  ...contentStyles(CAPTION_COLOR, isSelected),
  willChange: "background-color, transform", // 性能优化
});

// 通用时间线项目样式函数 - 内存化常用样式组合
const shadowStyles = {
  dragging: "0 4px 8px rgba(0,0,0,0.3)",
  selected: "0 2px 5px rgba(0,0,0,0.2)",
  hovering: "0 1px 3px rgba(0,0,0,0.1)",
  none: "none",
};

const borderStyles = {
  selected: "2px dashed rgba(33, 150, 243, 0.8)",
  multiSelected: "2px solid rgba(255, 152, 0, 0.9)", // 橙色边框表示多选
  dragging: "1px solid rgba(33, 150, 243, 0.8)",
  none: "none",
};

// 通用时间线项目样式函数 - 优化
export const timelineItemStyles = (
  color: string,
  isSelected: boolean,
  isHovering: boolean,
  isDragging: boolean = false,
  isMultiSelected: boolean = false
) => ({
  position: "absolute",
  top: 0,
  height: ELEMENT_HEIGHT,
  zIndex: isSelected ? 20 : isDragging ? 30 : 10,
  cursor: "move",
  borderRadius: 1,
  overflow: "hidden",

  boxShadow: isDragging
    ? shadowStyles.dragging
    : isSelected
    ? shadowStyles.selected
    : isHovering
    ? shadowStyles.hovering
    : shadowStyles.none,

  border: isDragging
    ? borderStyles.dragging
    : isMultiSelected
    ? borderStyles.multiSelected
    : isSelected
    ? borderStyles.selected
    : borderStyles.none,

  bgcolor: isSelected ? color : alpha(color, 0.8),
  willChange: "transform, box-shadow", // 性能优化：提示浏览器
  "&:hover": {
    bgcolor: color,
  },
  "&.dragging-active": {
    transform: "scale(1.02)",
    boxShadow: shadowStyles.dragging,
    zIndex: 30,
  },
  "&.resize-active": {
    boxShadow: "0 2px 6px rgba(0,0,0,0.25)",
  },
  "&.panning-mode": {
    borderLeft: isDragging ? "3px solid #ff9800" : "2px solid #ff9800",
    borderRight: isDragging ? "3px solid #ff9800" : "2px solid #ff9800",
    boxShadow: "0 0 8px rgba(255,152,0,0.5)",
    bgcolor: alpha(color, 0.9),
    "&::before": {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      border: "1px dashed #ff9800",
      borderRadius: "4px",
      pointerEvents: "none",
    },
  },
  userSelect: "none",
});

// 字幕项目样式 - 使用预计算的颜色值
export const captionItemStyles = (
  isSelected: boolean,
  isHovering: boolean,
  isDragging: boolean = false
) => ({
  ...timelineItemStyles(CAPTION_COLOR, isSelected, isHovering, isDragging),
  height: CAPTION_HEIGHT,
});

// 持续时间标签样式
export const durationLabelStyles = (
  compactMode: boolean,
  isVisible: boolean
) => ({
  position: "absolute" as const,
  right: compactMode ? 2 : 4,
  top: "50%",
  transform: "translateY(-50%)",
  fontSize: compactMode ? "0.7rem" : "0.8rem",
  opacity: isVisible ? 1 : 0,
  transition: "opacity 0.2s",
  pointerEvents: "none" as const,
  userSelect: "none" as const,
});

// 吸附指示器样式 - 使用预计算的透明度值
export const snapIndicatorStyles = (position: "left" | "right") => ({
  position: "absolute",
  [position]: 0,
  top: "-15px",
  width: "2px",
  height: "15px",
  bgcolor: SNAP_CONSTANTS.SNAP_COLOR,
  zIndex: 60,
  boxShadow: `0 0 4px ${SNAP_COLOR_ALPHA_07}`,
  willChange: "opacity", // 性能优化：提示浏览器
  "&::after": {
    content: '""',
    position: "absolute",
    top: 0,
    [position === "left" ? "left" : "right"]: "-3px",
    width: "8px",
    height: "8px",
    borderRadius: "50%",
    bgcolor: SNAP_CONSTANTS.SNAP_COLOR,
    boxShadow: `0 0 4px ${SNAP_COLOR_ALPHA_07}`,
  },
  animation: "pulseSnap 1.5s infinite",
  "@keyframes pulseSnap": {
    "0%": { opacity: 0.7 },
    "50%": { opacity: 1 },
    "100%": { opacity: 0.7 },
  },
});

// 右键菜单样式
export const contextMenuStyles = {
  paper: {
    width: "200px", // 增加菜单宽度
    borderRadius: "8px",
    boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
    border: "1px solid rgba(0,0,0,0.08)",
    overflow: "hidden",
  },
};

// 右键菜单项样式
export const contextMenuItemStyles = {
  padding: "5px 8px",
  transition: "background-color 0.2s", // 优化：只对背景色应用过渡效果
  "& .MuiListItemIcon-root": {
    minWidth: "36px",
    color: "#555",
  },
  "& .MuiListItemText-primary": {
    fontSize: "14px",
    fontWeight: 500,
  },
};

// 菜单分组的分隔线样式
export const menuDividerStyles = {
  margin: "4px 0",
};
