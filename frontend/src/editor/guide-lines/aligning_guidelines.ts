/**
 * 对齐指导线功能 - 优化版本
 * 修复了缩放对象对齐边缘的问题
 * 改进性能和代码可读性
 */

import { fabric } from "fabric";

// 配置常量
const ALIGNMENT_CONFIG = {
  lineOffset: 5,
  lineMargin: 4,
  lineWidth: 1,
  lineColor: "rgb(0,150,255)",
} as const;

// 类型定义
interface Point {
  x: number;
  y: number;
}

interface Line {
  x?: number;
  y?: number;
  x1?: number;
  y1?: number;
  x2?: number;
  y2?: number;
}

interface ObjectBounds {
  center: Point;
  left: number;
  top: number;
  right: number;
  bottom: number;
  width: number;
  height: number;
}

interface CanvasState {
  viewportTransform: number[];
  zoom: number;
  ctx: CanvasRenderingContext2D;
}

class AlignmentGuidelineManager {
  private canvas: fabric.Canvas;
  private canvasState: CanvasState;
  private verticalLines: Line[] = [];
  private horizontalLines: Line[] = [];

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.canvasState = {
      viewportTransform: [1, 0, 0, 1, 0, 0],
      zoom: 1,
      ctx: canvas.getSelectionContext(),
    };
    this.initializeEventListeners();
  }

  private initializeEventListeners(): void {
    this.canvas.on("mouse:down", () => this.updateCanvasState());
    this.canvas.on("object:moving", (e) => this.handleObjectMoving(e));
    this.canvas.on("before:render", () => this.clearContext());
    this.canvas.on("after:render", () => this.renderGuidelines());
    this.canvas.on("mouse:up", () => this.clearGuidelines());
  }

  private updateCanvasState(): void {
    this.canvasState.viewportTransform = this.canvas.viewportTransform || [
      1, 0, 0, 1, 0, 0,
    ];
    this.canvasState.zoom = this.canvas.getZoom();
  }

  private clearContext(): void {
    const contextTop = (this.canvas as any).contextTop;
    if (contextTop) {
      this.canvas.clearContext(contextTop);
    }
  }

  private clearGuidelines(): void {
    this.verticalLines.length = 0;
    this.horizontalLines.length = 0;
    this.canvas.renderAll();
  }

  private getObjectBounds(obj: fabric.Object): ObjectBounds {
    const center = obj.getCenterPoint();
    const boundingRect = obj.getBoundingRect();
    const transform = this.canvasState.viewportTransform;

    const width = boundingRect.width / transform[0];
    const height = boundingRect.height / transform[3];

    return {
      center,
      left: center.x - width / 2,
      top: center.y - height / 2,
      right: center.x + width / 2,
      bottom: center.y + height / 2,
      width,
      height,
    };
  }

  private isInRange(value1: number, value2: number): boolean {
    const rounded1 = Math.round(value1);
    const rounded2 = Math.round(value2);
    const margin = ALIGNMENT_CONFIG.lineMargin;

    return Math.abs(rounded1 - rounded2) <= margin;
  }

  private createVerticalLine(
    x: number,
    activeObj: ObjectBounds,
    targetObj: ObjectBounds
  ): Line {
    const offset = ALIGNMENT_CONFIG.lineOffset;

    return {
      x,
      y1:
        Math.min(targetObj.top, activeObj.top) -
        Math.max(targetObj.height, activeObj.height) / 2 -
        offset,
      y2: Math.max(targetObj.bottom, activeObj.bottom) + offset,
    };
  }

  private createHorizontalLine(
    y: number,
    activeObj: ObjectBounds,
    targetObj: ObjectBounds
  ): Line {
    const offset = ALIGNMENT_CONFIG.lineOffset;

    return {
      y,
      x1:
        Math.min(targetObj.left, activeObj.left) -
        Math.max(targetObj.width, activeObj.width) / 2 -
        offset,
      x2: Math.max(targetObj.right, activeObj.right) + offset,
    };
  }

  private snapToVerticalAlignment(
    activeObject: fabric.Object,
    activeObjBounds: ObjectBounds,
    targetObjBounds: ObjectBounds
  ): boolean {
    let snapped = false;

    // 水平中心线对齐
    if (this.isInRange(targetObjBounds.center.x, activeObjBounds.center.x)) {
      this.verticalLines.push(
        this.createVerticalLine(
          targetObjBounds.center.x,
          activeObjBounds,
          targetObjBounds
        )
      );
      activeObject.setPositionByOrigin(
        new fabric.Point(targetObjBounds.center.x, activeObjBounds.center.y),
        "center",
        "center"
      );
      snapped = true;
    }

    // 左边缘对齐
    if (this.isInRange(targetObjBounds.left, activeObjBounds.left)) {
      this.verticalLines.push(
        this.createVerticalLine(
          targetObjBounds.left,
          activeObjBounds,
          targetObjBounds
        )
      );
      activeObject.setPositionByOrigin(
        new fabric.Point(
          targetObjBounds.left + activeObjBounds.width / 2,
          activeObjBounds.center.y
        ),
        "center",
        "center"
      );
      snapped = true;
    }

    // 右边缘对齐
    if (this.isInRange(targetObjBounds.right, activeObjBounds.right)) {
      this.verticalLines.push(
        this.createVerticalLine(
          targetObjBounds.right,
          activeObjBounds,
          targetObjBounds
        )
      );
      activeObject.setPositionByOrigin(
        new fabric.Point(
          targetObjBounds.right - activeObjBounds.width / 2,
          activeObjBounds.center.y
        ),
        "center",
        "center"
      );
      snapped = true;
    }

    return snapped;
  }

  private snapToHorizontalAlignment(
    activeObject: fabric.Object,
    activeObjBounds: ObjectBounds,
    targetObjBounds: ObjectBounds
  ): boolean {
    let snapped = false;

    // 垂直中心线对齐
    if (this.isInRange(targetObjBounds.center.y, activeObjBounds.center.y)) {
      this.horizontalLines.push(
        this.createHorizontalLine(
          targetObjBounds.center.y,
          activeObjBounds,
          targetObjBounds
        )
      );
      activeObject.setPositionByOrigin(
        new fabric.Point(activeObjBounds.center.x, targetObjBounds.center.y),
        "center",
        "center"
      );
      snapped = true;
    }

    // 上边缘对齐
    if (this.isInRange(targetObjBounds.top, activeObjBounds.top)) {
      this.horizontalLines.push(
        this.createHorizontalLine(
          targetObjBounds.top,
          activeObjBounds,
          targetObjBounds
        )
      );
      activeObject.setPositionByOrigin(
        new fabric.Point(
          activeObjBounds.center.x,
          targetObjBounds.top + activeObjBounds.height / 2
        ),
        "center",
        "center"
      );
      snapped = true;
    }

    // 下边缘对齐
    if (this.isInRange(targetObjBounds.bottom, activeObjBounds.bottom)) {
      this.horizontalLines.push(
        this.createHorizontalLine(
          targetObjBounds.bottom,
          activeObjBounds,
          targetObjBounds
        )
      );
      activeObject.setPositionByOrigin(
        new fabric.Point(
          activeObjBounds.center.x,
          targetObjBounds.bottom - activeObjBounds.height / 2
        ),
        "center",
        "center"
      );
      snapped = true;
    }

    return snapped;
  }

  private handleObjectMoving(e: fabric.IEvent): void {
    const activeObject = e.target as fabric.Object;
    if (!activeObject || !(this.canvas as any)._currentTransform) return;

    const canvasObjects = this.canvas.getObjects();
    const activeObjBounds = this.getObjectBounds(activeObject);

    let hasVerticalAlignment = false;
    let hasHorizontalAlignment = false;

    // 遍历所有其他对象进行对齐检测
    for (const obj of canvasObjects) {
      if (obj === activeObject) continue;

      const targetObjBounds = this.getObjectBounds(obj);

      // 检测垂直对齐
      if (
        this.snapToVerticalAlignment(
          activeObject,
          activeObjBounds,
          targetObjBounds
        )
      ) {
        hasVerticalAlignment = true;
        // 重新获取更新后的边界信息
        const updatedBounds = this.getObjectBounds(activeObject);
        Object.assign(activeObjBounds, updatedBounds);
      }

      // 检测水平对齐
      if (
        this.snapToHorizontalAlignment(
          activeObject,
          activeObjBounds,
          targetObjBounds
        )
      ) {
        hasHorizontalAlignment = true;
        // 重新获取更新后的边界信息
        const updatedBounds = this.getObjectBounds(activeObject);
        Object.assign(activeObjBounds, updatedBounds);
      }
    }

    // 清理未使用的指导线
    if (!hasVerticalAlignment) {
      this.verticalLines.length = 0;
    }
    if (!hasHorizontalAlignment) {
      this.horizontalLines.length = 0;
    }
  }

  private drawLine(x1: number, y1: number, x2: number, y2: number): void {
    const { ctx, zoom, viewportTransform } = this.canvasState;

    ctx.save();
    ctx.lineWidth = ALIGNMENT_CONFIG.lineWidth;
    ctx.strokeStyle = ALIGNMENT_CONFIG.lineColor;
    ctx.beginPath();
    ctx.moveTo(
      x1 * zoom + viewportTransform[4],
      y1 * zoom + viewportTransform[5]
    );
    ctx.lineTo(
      x2 * zoom + viewportTransform[4],
      y2 * zoom + viewportTransform[5]
    );
    ctx.stroke();
    ctx.restore();
  }

  private drawVerticalLine(line: Line): void {
    if (
      line.x !== undefined &&
      line.y1 !== undefined &&
      line.y2 !== undefined
    ) {
      this.drawLine(
        line.x + 0.5,
        Math.min(line.y1, line.y2),
        line.x + 0.5,
        Math.max(line.y1, line.y2)
      );
    }
  }

  private drawHorizontalLine(line: Line): void {
    if (
      line.y !== undefined &&
      line.x1 !== undefined &&
      line.x2 !== undefined
    ) {
      this.drawLine(
        Math.min(line.x1, line.x2),
        line.y + 0.5,
        Math.max(line.x1, line.x2),
        line.y + 0.5
      );
    }
  }

  private renderGuidelines(): void {
    // 渲染垂直指导线
    for (const line of this.verticalLines) {
      this.drawVerticalLine(line);
    }

    // 渲染水平指导线
    for (const line of this.horizontalLines) {
      this.drawHorizontalLine(line);
    }

    // 清理指导线数组以准备下一帧
    this.verticalLines.length = 0;
    this.horizontalLines.length = 0;
  }

  // 公共方法：销毁实例
  public destroy(): void {
    this.canvas.off("mouse:down");
    this.canvas.off("object:moving");
    this.canvas.off("before:render");
    this.canvas.off("after:render");
    this.canvas.off("mouse:up");
    this.clearGuidelines();
  }
}

// 导出初始化函数以保持向后兼容
export default function initAligningGuidelines(
  canvas: fabric.Canvas
): AlignmentGuidelineManager {
  return new AlignmentGuidelineManager(canvas);
}

// 同时导出类，以便需要更多控制的使用者可以直接使用
export { AlignmentGuidelineManager, ALIGNMENT_CONFIG };
