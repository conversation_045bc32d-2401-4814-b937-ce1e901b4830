/**
 * 画布居中对齐指导线功能 - 优化版本
 * 提供对象与画布中心对齐的视觉指导
 * 改进性能和代码可读性
 */

import { fabric } from "fabric";

// 配置常量
const CENTERING_CONFIG = {
  lineMargin: 4,
  lineColor: "rgba(30,144,255,0.6)",
  lineWidth: 1,
} as const;

// 类型定义
interface Point {
  x: number;
  y: number;
}

interface CanvasState {
  width: number;
  height: number;
  centerX: number;
  centerY: number;
  viewportTransform: number[];
  ctx: CanvasRenderingContext2D;
}

interface CenteringState {
  isInVerticalCenter: boolean;
  isInHorizontalCenter: boolean;
}

class CenteringGuidelineManager {
  private canvas: fabric.Canvas;
  private canvasState: CanvasState;
  private centeringState: CenteringState = {
    isInVerticalCenter: false,
    isInHorizontalCenter: false,
  };

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.canvasState = this.initializeCanvasState();
    this.initializeEventListeners();
  }

  private initializeCanvasState(): CanvasState {
    const width = this.canvas.getWidth();
    const height = this.canvas.getHeight();

    return {
      width,
      height,
      centerX: width / 2,
      centerY: height / 2,
      viewportTransform: [1, 0, 0, 1, 0, 0],
      ctx: this.canvas.getSelectionContext(),
    };
  }

  private initializeEventListeners(): void {
    this.canvas.on("mouse:down", () => this.updateViewportTransform());
    this.canvas.on("object:moving", (e) => this.handleObjectMoving(e));
    this.canvas.on("before:render", () => this.clearContext());
    this.canvas.on("after:render", () => this.renderCenterLines());
    this.canvas.on("mouse:up", () => this.clearCenteringState());
  }

  private updateViewportTransform(): void {
    this.canvasState.viewportTransform = this.canvas.viewportTransform || [
      1, 0, 0, 1, 0, 0,
    ];
  }

  private clearContext(): void {
    const contextTop = (this.canvas as any).contextTop;
    if (contextTop) {
      this.canvas.clearContext(contextTop);
    }
  }

  private clearCenteringState(): void {
    this.centeringState.isInVerticalCenter = false;
    this.centeringState.isInHorizontalCenter = false;
    this.canvas.renderAll();
  }

  /**
   * 检查对象是否在画布中心区域内
   */
  private isInCenterRange(value: number, centerValue: number): boolean {
    const margin = CENTERING_CONFIG.lineMargin;
    return Math.abs(Math.round(value) - centerValue) <= margin;
  }

  /**
   * 将对象吸附到画布中心
   */
  private snapToCenter(
    activeObject: fabric.Object,
    objectCenter: Point
  ): Point {
    const { centerX, centerY } = this.canvasState;

    // 检查垂直中心对齐
    const isInVerticalCenter = this.isInCenterRange(objectCenter.x, centerX);
    // 检查水平中心对齐
    const isInHorizontalCenter = this.isInCenterRange(objectCenter.y, centerY);

    // 更新状态
    this.centeringState.isInVerticalCenter = isInVerticalCenter;
    this.centeringState.isInHorizontalCenter = isInHorizontalCenter;

    // 计算新位置
    const newPosition = new fabric.Point(
      isInVerticalCenter ? centerX : objectCenter.x,
      isInHorizontalCenter ? centerY : objectCenter.y
    );

    // 如果需要对齐，设置新位置
    if (isInVerticalCenter || isInHorizontalCenter) {
      activeObject.setPositionByOrigin(newPosition, "center", "center");
    }

    return newPosition;
  }

  private handleObjectMoving(e: fabric.IEvent): void {
    const activeObject = e.target as fabric.Object;
    if (!activeObject || !(this.canvas as any)._currentTransform) return;

    const objectCenter = activeObject.getCenterPoint();
    this.snapToCenter(activeObject, objectCenter);
  }

  /**
   * 绘制中心线
   */
  private drawCenterLine(x1: number, y1: number, x2: number, y2: number): void {
    const { ctx, viewportTransform } = this.canvasState;

    ctx.save();
    ctx.strokeStyle = CENTERING_CONFIG.lineColor;
    ctx.lineWidth = CENTERING_CONFIG.lineWidth;
    ctx.beginPath();
    ctx.moveTo(x1 * viewportTransform[0], y1 * viewportTransform[3]);
    ctx.lineTo(x2 * viewportTransform[0], y2 * viewportTransform[3]);
    ctx.stroke();
    ctx.restore();
  }

  /**
   * 显示垂直中心线
   */
  private showVerticalCenterLine(): void {
    const { centerX, height } = this.canvasState;
    this.drawCenterLine(centerX + 0.5, 0, centerX + 0.5, height);
  }

  /**
   * 显示水平中心线
   */
  private showHorizontalCenterLine(): void {
    const { centerY, width } = this.canvasState;
    this.drawCenterLine(0, centerY + 0.5, width, centerY + 0.5);
  }

  /**
   * 渲染中心指导线
   */
  private renderCenterLines(): void {
    const { isInVerticalCenter, isInHorizontalCenter } = this.centeringState;

    if (isInVerticalCenter) {
      this.showVerticalCenterLine();
    }

    if (isInHorizontalCenter) {
      this.showHorizontalCenterLine();
    }
  }

  /**
   * 更新画布尺寸（当画布大小改变时调用）
   */
  public updateCanvasSize(): void {
    const width = this.canvas.getWidth();
    const height = this.canvas.getHeight();

    this.canvasState.width = width;
    this.canvasState.height = height;
    this.canvasState.centerX = width / 2;
    this.canvasState.centerY = height / 2;
  }

  /**
   * 获取当前中心位置
   */
  public getCenterPosition(): Point {
    return {
      x: this.canvasState.centerX,
      y: this.canvasState.centerY,
    };
  }

  /**
   * 手动设置对象到画布中心
   */
  public centerObject(
    object: fabric.Object,
    centerX = true,
    centerY = true
  ): void {
    const { centerX: canvasCenterX, centerY: canvasCenterY } = this.canvasState;
    const currentCenter = object.getCenterPoint();

    const newPosition = new fabric.Point(
      centerX ? canvasCenterX : currentCenter.x,
      centerY ? canvasCenterY : currentCenter.y
    );

    object.setPositionByOrigin(newPosition, "center", "center");
    this.canvas.renderAll();
  }

  /**
   * 销毁实例，清理事件监听器
   */
  public destroy(): void {
    this.canvas.off("mouse:down");
    this.canvas.off("object:moving");
    this.canvas.off("before:render");
    this.canvas.off("after:render");
    this.canvas.off("mouse:up");
    this.clearCenteringState();
  }
}

// 导出初始化函数以保持向后兼容
export default function initCenteringGuidelines(
  canvas: fabric.Canvas
): CenteringGuidelineManager {
  return new CenteringGuidelineManager(canvas);
}

// 同时导出类和配置，以便需要更多控制的使用者可以直接使用
export { CenteringGuidelineManager, CENTERING_CONFIG };
