import React from "react";
import {
  Box,
  Typography,
  Stack,
  Tab,
  Tabs,
  ToggleButton,
  ToggleButtonGroup,
  Select,
  MenuItem,
  SelectChangeEvent,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { UnifiedColorPicker } from "./UnifiedColorPicker";
import { GradientPicker } from "../../editor/components/color/GradientPicker";

// 1. 统一的样式组件
export const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

export const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.8rem",
  fontWeight: theme.typography.fontWeightRegular,
  "&:hover": {
    color: theme.palette.primary.main,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
  },
}));

export const StyledSelect = styled(Select)({
  "& .MuiSelect-select": {
    fontSize: "0.9rem",
  },
});

export const StyledMenuItem = styled(MenuItem)({
  fontSize: "0.9rem",
  display: "flex",
  alignItems: "center",
  "&:hover": {
    backgroundColor: "#f0f0f0",
  },
});

export const FontPreview = styled("span")({
  marginRight: "8px",
  fontSize: "0.9rem",
});

// 2. 控制面板头部组件
interface ControlHeaderProps {
  title: string;
  actions?: React.ReactNode;
}

export const ControlHeader: React.FC<ControlHeaderProps> = ({
  title,
  actions,
}) => (
  <Box
    sx={{
      height: 48,
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      px: 2,
      flexShrink: 0,
      borderColor: "divider",
    }}
  >
    <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
      {title}
    </Typography>
    {actions && <Box>{actions}</Box>}
  </Box>
);

// 3. 滚动容器组件
interface ScrollContainerProps {
  children: React.ReactNode;
  sx?: any;
}

export const ScrollContainer: React.FC<ScrollContainerProps> = ({
  children,
  sx = {},
}) => (
  <Box
    sx={{
      ml: 2,
      my: 2,
      mr: 0,
      height: "100%",
      overflowY: "auto",
      overflowX: "hidden",
      pr: 2,
      "&::-webkit-scrollbar": {
        width: "6px",
      },
      "&::-webkit-scrollbar-track": {
        bgcolor: "grey.100",
      },
      "&::-webkit-scrollbar-thumb": {
        bgcolor: "grey.400",
        borderRadius: "6px",
      },
      ...sx,
    }}
  >
    {children}
  </Box>
);

// 4. 颜色选择器行组件
interface ColorRowProps {
  label: string;
  color: string | string[];
  onChange: (color: string | string[]) => void;
  isGradient?: boolean;
  allowTransparent?: boolean;
  size?: "small" | "medium" | "large";
}

export const ColorRow: React.FC<ColorRowProps> = ({
  label,
  color,
  onChange,
  isGradient = false,
  allowTransparent = true,
  size = "small",
}) => (
  <Stack
    direction="row"
    spacing={2}
    alignItems="center"
    justifyContent="space-between"
  >
    <Typography
      variant="body2"
      sx={{
        color: "text.secondary",
      }}
    >
      {label}
    </Typography>
    {isGradient ? (
      <GradientPicker
        colors={color as string[]}
        onChange={onChange as (colors: string[]) => void}
      />
    ) : (
      <UnifiedColorPicker
        color={color as string}
        onChange={onChange as (color: string) => void}
        mode="simple"
        size={size}
        allowTransparent={allowTransparent}
        pickerType="sketch"
      />
    )}
  </Stack>
);

// 5. 切换按钮行组件
interface ToggleButtonRowProps {
  label: string;
  value: any;
  onChange: (event: React.MouseEvent<HTMLElement>, value: any) => void;
  buttons: Array<{
    value: string;
    icon: React.ReactNode;
    label: string;
  }>;
  exclusive?: boolean;
}

export const ToggleButtonRow: React.FC<ToggleButtonRowProps> = ({
  label,
  value,
  onChange,
  buttons,
  exclusive = true,
}) => (
  <Stack direction="row" justifyContent="space-between" alignItems="center">
    <Typography
      variant="body2"
      sx={{
        color: "text.secondary",
      }}
    >
      {label}
    </Typography>
    <ToggleButtonGroup
      value={value}
      exclusive={exclusive}
      onChange={onChange}
      size="small"
    >
      {buttons.map((button) => (
        <ToggleButton
          key={button.value}
          value={button.value}
          aria-label={button.label}
          size="small"
        >
          {button.icon}
        </ToggleButton>
      ))}
    </ToggleButtonGroup>
  </Stack>
);

// 6. 选择器行组件
interface SelectRowProps {
  label: string;
  value: string;
  onChange: (event: SelectChangeEvent<unknown>) => void;
  options: Array<{ value: string; label: string; preview?: React.ReactNode }>;
  sx?: any;
  size?: "small" | "medium";
}

export const SelectRow: React.FC<SelectRowProps> = ({
  label,
  value,
  onChange,
  options,
  sx = {},
  size = "small",
}) => (
  <Stack direction="row" justifyContent="space-between" alignItems="center">
    <Typography
      variant="body2"
      sx={{
        color: "text.secondary",
      }}
    >
      {label}
    </Typography>
    <StyledSelect
      value={value}
      onChange={onChange}
      sx={{ width: "60%", ...sx }}
      size={size}
    >
      {options.map((option) => (
        <StyledMenuItem key={option.value} value={option.value}>
          {option.preview && (
            <FontPreview style={{ fontFamily: option.value }}>
              {option.preview}
            </FontPreview>
          )}
        </StyledMenuItem>
      ))}
    </StyledSelect>
  </Stack>
);

// 7. 控制面板布局组件
interface ControlPanelProps {
  title: string;
  headerActions?: React.ReactNode;
  tabs?: Array<{ label: string; content: React.ReactNode }>;
  children?: React.ReactNode;
  scrollContainerSx?: any;
}

export const ControlPanel: React.FC<ControlPanelProps> = ({
  title,
  headerActions,
  tabs,
  children,
  scrollContainerSx,
}) => {
  const [activeTab, setActiveTab] = React.useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box
      sx={{
        width: "100%",
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <ControlHeader title={title} actions={headerActions} />

      {tabs && (
        <StyledTabs value={activeTab} onChange={handleTabChange}>
          {tabs.map((tab, index) => (
            <StyledTab key={index} label={tab.label} />
          ))}
        </StyledTabs>
      )}

      <ScrollContainer sx={scrollContainerSx}>
        {tabs ? (
          <Stack spacing={tabs.length > 1 ? 2 : 1}>
            {tabs[activeTab]?.content}
          </Stack>
        ) : (
          children
        )}
      </ScrollContainer>
    </Box>
  );
};
