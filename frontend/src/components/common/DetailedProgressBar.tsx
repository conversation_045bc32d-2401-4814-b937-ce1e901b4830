import React from "react";
import {
  Box,
  LinearProgress,
  Typography,
  Tooltip,
  useTheme,
} from "@mui/material";
import { alpha } from "@mui/material/styles";
import { UploadProgress } from "../../services/s3UploadService";

interface DetailedProgressBarProps {
  progress: UploadProgress;
  fileName?: string;
  variant?: "compact" | "detailed";
  showTooltip?: boolean;
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

/**
 * 格式化上传速度
 */
const formatSpeed = (bytesPerSecond: number): string => {
  if (bytesPerSecond === 0) return "0 B/s";
  const k = 1024;
  const sizes = ["B/s", "KB/s", "MB/s", "GB/s"];
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

/**
 * 格式化时间
 */
const formatTime = (seconds: number): string => {
  if (!isFinite(seconds) || seconds < 0) return "--";
  
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}分${remainingSeconds}秒`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分`;
  }
};

export const DetailedProgressBar: React.FC<DetailedProgressBarProps> = ({
  progress,
  fileName,
  variant = "detailed",
  showTooltip = true,
}) => {
  const theme = useTheme();
  
  const progressPercentage = Math.round(progress.percentage);
  const loadedSize = formatFileSize(progress.loaded);
  const totalSize = formatFileSize(progress.total);
  const speed = progress.speed ? formatSpeed(progress.speed) : "--";
  const remainingTime = progress.remainingTime ? formatTime(progress.remainingTime) : "--";
  const elapsedTime = progress.elapsedTime ? formatTime(progress.elapsedTime) : "--";

  const progressBarContent = (
    <Box sx={{ width: "100%" }}>
      {variant === "detailed" && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 0.5,
          }}
        >
          <Typography variant="body2" noWrap sx={{ flex: 1, mr: 1 }}>
            {fileName || "上传中..."}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: "text.secondary",
              fontWeight: "medium",
              minWidth: "fit-content",
            }}
          >
            {progressPercentage}%
          </Typography>
        </Box>
      )}

      <LinearProgress
        variant="determinate"
        value={progressPercentage}
        sx={{
          height: variant === "compact" ? 4 : 6,
          borderRadius: 3,
          backgroundColor: alpha(theme.palette.primary.main, 0.1),
          "& .MuiLinearProgress-bar": {
            borderRadius: 3,
            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
          },
        }}
      />

      {variant === "detailed" && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 0.5,
            gap: 2,
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {loadedSize} / {totalSize}
          </Typography>
          
          <Box sx={{ display: "flex", gap: 2 }}>
            <Typography variant="caption" color="text.secondary">
              {speed}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              剩余: {remainingTime}
            </Typography>
          </Box>
        </Box>
      )}

      {variant === "compact" && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 0.5,
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {progressPercentage}%
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {speed}
          </Typography>
        </Box>
      )}
    </Box>
  );

  if (showTooltip && variant === "compact") {
    return (
      <Tooltip
        title={
          <Box>
            <Typography variant="body2" sx={{ fontWeight: "medium" }}>
              {fileName || "上传中..."}
            </Typography>
            <Typography variant="caption" display="block">
              进度: {progressPercentage}% ({loadedSize} / {totalSize})
            </Typography>
            <Typography variant="caption" display="block">
              速度: {speed}
            </Typography>
            <Typography variant="caption" display="block">
              已用时间: {elapsedTime}
            </Typography>
            <Typography variant="caption" display="block">
              剩余时间: {remainingTime}
            </Typography>
          </Box>
        }
        placement="top"
        arrow
      >
        <Box>{progressBarContent}</Box>
      </Tooltip>
    );
  }

  return progressBarContent;
};

export default DetailedProgressBar;
