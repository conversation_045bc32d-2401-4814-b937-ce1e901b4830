# 统一组件库

这个目录包含了重构后的统一组件，用于替换项目中重复的功能组件。

## UnifiedColorPicker

统一的颜色选择器组件，支持多种模式和功能：

### 功能特性
- **多种模式**: simple（简单）、advanced（高级）、gradient（渐变）
- **多种选择器**: SketchPicker、ChromePicker
- **预设颜色**: 内置常用颜色面板
- **最近使用**: 自动记录最近使用的颜色
- **透明支持**: 可选择透明色
- **渐变编辑**: 支持双色渐变和角度调整

### 使用示例

```tsx
import { UnifiedColorPicker } from '@/components/common';

// 简单模式
<UnifiedColorPicker
  color="#ff0000"
  onChange={(color) => console.log(color)}
  mode="simple"
/>

// 高级模式
<UnifiedColorPicker
  color="#ff0000"
  onChange={(color) => console.log(color)}
  mode="advanced"
  showPresets={true}
  showRecents={true}
  allowTransparent={true}
/>

// 渐变模式
<UnifiedColorPicker
  color={["#ff0000", "#00ff00"]}
  onChange={(colors) => console.log(colors)}
  mode="gradient"
/>
```

## UnifiedFileUpload

统一的文件上传组件，支持多种上传方式和文件类型：

### 功能特性
- **多种模式**: single（单文件）、multiple（多文件）
- **多种样式**: button（按钮）、dropzone（拖拽区）、inline（内联）
- **文件验证**: 大小、类型、数量限制
- **上传进度**: 实时显示上传进度
- **错误处理**: 完善的错误提示和重试机制
- **预览功能**: 文件列表预览和管理

### 使用示例

```tsx
import { UnifiedFileUpload } from '@/components/common';

// 基础用法
<UnifiedFileUpload
  mode="multiple"
  variant="dropzone"
  onFilesSelected={(files) => console.log(files)}
/>

// 自定义验证
<UnifiedFileUpload
  mode="single"
  variant="button"
  validation={{
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['.jpg', '.png', '.mp4'],
    maxCount: 5
  }}
  customUploadHandler={async (file) => {
    // 自定义上传逻辑
    return { url: 'upload-url' };
  }}
/>

// 内联模式
<UnifiedFileUpload
  variant="inline"
  showPreview={false}
  onFilesSelected={(files) => handleFiles(files)}
/>
```

## 替换指南

### 替换颜色选择器

原有的颜色选择器组件可以按以下方式替换：

1. **ColorPicker.tsx** → `UnifiedColorPicker` (mode="simple")
2. **GradientPicker.tsx** → `UnifiedColorPicker` (mode="gradient")
3. **BackgroundSelector.tsx** → `UnifiedColorPicker` (mode="advanced")

### 替换文件上传

原有的上传功能可以按以下方式替换：

1. **Uploads.tsx** → `UnifiedFileUpload` (variant="dropzone")
2. **文件选择按钮** → `UnifiedFileUpload` (variant="button")
3. **内联上传** → `UnifiedFileUpload` (variant="inline")

## 优势

1. **减少代码重复**: 统一的API和实现
2. **提升性能**: 优化的状态管理和渲染
3. **更好的用户体验**: 一致的交互和视觉设计
4. **易于维护**: 集中的功能和bug修复
5. **类型安全**: 完整的TypeScript支持

## 注意事项

1. 替换时请确保所有依赖的props都已正确映射
2. 测试新组件的所有功能是否正常工作
3. 可能需要调整部分样式以匹配现有设计
4. 建议分步骤进行替换，避免一次性修改过多文件
