import React, { useState, useEffect, useCallback, memo, useRef } from "react";
import {
  Box,
  Typography,
  IconButton,
  Button,
  MenuItem,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tooltip,
  Skeleton,
  useMediaQuery,
  useTheme,
  Grid,
  CardMedia,
  ButtonGroup,
  Menu,
  ListItemIcon,
  ListItemText,
  LinearProgress,
  CircularProgress,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  Download as DownloadIcon,
  CloudUpload as UploadIcon,
  PlayArrow as PlayIcon,
  VolumeUp as AudioIcon,
  Image as ImageIcon,
  MoreVert as MoreVertIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import {
  mediaLibraryService,
  MediaMetadata,
  s3UploadService,
} from "../../services/s3UploadService";
import { useStore, UploadingFile as GlobalUploadingFile } from "../../store";
import { observer } from "mobx-react-lite";
import { getUid } from "../../utils";

// 常量定义
const MEDIA_TYPES = {
  ALL: "all",
  IMAGE: "image",
  VIDEO: "video",
  AUDIO: "audio",
} as const;

const FILE_TYPE_OPTIONS = [
  { label: "All", value: MEDIA_TYPES.ALL },
  { label: "Images", value: MEDIA_TYPES.IMAGE },
  { label: "Videos", value: MEDIA_TYPES.VIDEO },
  { label: "Audio", value: MEDIA_TYPES.AUDIO },
] as const;

const GRID_BREAKPOINTS = {
  xs: 12,
  sm: 6,
  md: 6,
  lg: 6,
} as const;

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const getFileTypeLabel = (fileType: string): string => {
  if (fileType.startsWith(MEDIA_TYPES.IMAGE)) return "Image";
  if (fileType.startsWith(MEDIA_TYPES.VIDEO)) return "Video";
  if (fileType.startsWith(MEDIA_TYPES.AUDIO)) return "Audio";
  return "File";
};

// 样式常量
const STYLES = {
  menuButton: {
    bgcolor: "rgba(255,255,255,0.9)",
    "&:hover": { bgcolor: "white" },
  },
  previewContainer: {
    aspectRatio: "16/9",
    position: "relative",
    borderRadius: 1,
    overflow: "hidden",
  },
  durationChip: {
    position: "absolute",
    bottom: 3,
    left: 3,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    color: "white",
    px: 0.5,
    py: 0.5,
    borderRadius: 1,
    fontSize: "0.65rem",
    fontWeight: 500,
  },
  menuContainer: {
    position: "absolute",
    top: 8,
    right: 8,
    opacity: 0,
    transition: "opacity 0.2s",
    ".media-preview:hover &": {
      opacity: 1,
    },
  },
} as const;

export interface MediaLibraryProps {
  onMediaSelect?: (media: MediaMetadata) => void;
  selectionMode?: boolean;
  allowedTypes?: string[];
  maxSelections?: number;
}

// 骨架屏组件
const MediaSkeleton = memo(() => (
  <Box>
    <Skeleton
      variant="rectangular"
      sx={STYLES.previewContainer}
      animation="wave"
    />
    <Skeleton variant="text" width="85%" height={20} sx={{ mt: 1 }} />
  </Box>
));

// 骨架屏列表
const SkeletonList = memo(() => (
  <Grid container spacing={2} sx={{ mt: 1 }}>
    {Array.from(new Array(8)).map((_, index) => (
      <Grid key={`skeleton-${index}`} size={GRID_BREAKPOINTS}>
        <MediaSkeleton />
      </Grid>
    ))}
  </Grid>
));

// 上传进度卡片组件
interface UploadCardProps {
  uploadingFile: GlobalUploadingFile;
  onCancel: (fileId: string) => void;
  onRetry?: (fileId: string) => void;
}

const UploadCard = memo(
  ({ uploadingFile, onCancel, onRetry }: UploadCardProps) => {
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    // 生成预览URL（仅对图片和视频）
    useEffect(() => {
      if (
        uploadingFile.file.type.startsWith("image/") ||
        uploadingFile.file.type.startsWith("video/")
      ) {
        const url = URL.createObjectURL(uploadingFile.file);
        setPreviewUrl(url);
        return () => URL.revokeObjectURL(url);
      }
    }, [uploadingFile.file]);

    const getFileIcon = () => {
      if (uploadingFile.file.type.startsWith("image/"))
        return <ImageIcon sx={{ fontSize: 48, color: "grey.400" }} />;
      if (uploadingFile.file.type.startsWith("video/"))
        return <PlayIcon sx={{ fontSize: 48, color: "grey.400" }} />;
      if (uploadingFile.file.type.startsWith("audio/"))
        return <AudioIcon sx={{ fontSize: 48, color: "grey.400" }} />;
      return <ImageIcon sx={{ fontSize: 48, color: "grey.400" }} />;
    };

    const getMediaPreview = () => {
      // 如果上传完成，显示媒体内容
      if (uploadingFile.status === "completed") {
        if (uploadingFile.file.type.startsWith("image/") && previewUrl) {
          return (
            <CardMedia
              component="img"
              src={previewUrl}
              alt={uploadingFile.file.name}
              sx={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          );
        }

        if (uploadingFile.file.type.startsWith("video/") && previewUrl) {
          return (
            <CardMedia
              component="video"
              src={previewUrl}
              sx={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
              preload="metadata"
              muted
            />
          );
        }

        if (uploadingFile.file.type.startsWith("audio/")) {
          return (
            <Box
              sx={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#f3f4f6",
              }}
            >
              <AudioIcon sx={{ fontSize: 48, color: "primary.main" }} />
            </Box>
          );
        }
      }

      // 上传中或错误状态，显示图标
      return (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "#f8f9fa",
          }}
        >
          {getFileIcon()}
        </Box>
      );
    };

    const getOverlayContent = () => {
      switch (uploadingFile.status) {
        case "uploading":
          return (
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: "rgba(255, 255, 255, 0.95)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                border: "1px solid",
                borderColor: "grey.200",
                borderRadius: 2,
              }}
            >
              <Box
                sx={{
                  position: "relative",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Box
                  sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Typography
                    variant="body2"
                    color="primary.main"
                    fontWeight="bold"
                    fontSize="0.875rem"
                  >
                    {Math.round(uploadingFile.progress)}%
                  </Typography>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    fontSize="0.65rem"
                    sx={{ mt: 0.5 }}
                  >
                    Uploading
                  </Typography>
                </Box>
              </Box>
            </Box>
          );
        case "error":
          return (
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: "grey.50",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography
                variant="caption"
                color="error.main"
                textAlign="center"
                sx={{ mb: 1 }}
              >
                Upload failed
              </Typography>
              <Box sx={{ display: "flex", gap: 1 }}>
                {onRetry && (
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => onRetry(uploadingFile.id)}
                    sx={{
                      bgcolor: "rgba(255, 255, 255, 0.9)",
                      "&:hover": { bgcolor: "white" },
                    }}
                  >
                    <RefreshIcon fontSize="small" />
                  </IconButton>
                )}
                <IconButton
                  size="small"
                  color="error"
                  onClick={() => onCancel(uploadingFile.id)}
                  sx={{
                    bgcolor: "rgba(255, 255, 255, 0.9)",
                    "&:hover": { bgcolor: "white" },
                  }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          );
        default:
          return null;
      }
    };

    return (
      <Box
        sx={{
          cursor: uploadingFile.status === "completed" ? "pointer" : "default",
          transition: "all 0.2s ease",
          "&:hover": {
            transform:
              uploadingFile.status === "completed"
                ? "translateY(-2px)"
                : "none",
          },
        }}
      >
        <Box
          className="media-preview"
          sx={{
            ...STYLES.previewContainer,
            border: uploadingFile.status === "error" ? 2 : 0,
            borderColor:
              uploadingFile.status === "error" ? "error.main" : "transparent",
            position: "relative",
            overflow: "hidden",
          }}
        >
          {getMediaPreview()}
          {getOverlayContent()}

          {/* 取消按钮 */}
          {uploadingFile.status === "uploading" && (
            <Box sx={STYLES.menuContainer}>
              <IconButton
                size="small"
                onClick={() => onCancel(uploadingFile.id)}
                sx={STYLES.menuButton}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
          )}

          {/* 成功状态指示器 */}
          {uploadingFile.status === "completed" && (
            <Box
              sx={{
                position: "absolute",
                top: 8,
                left: 8,
                width: 24,
                height: 24,
                borderRadius: "50%",
                bgcolor: "success.main",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "white",
                fontSize: "0.875rem",
                fontWeight: "bold",
                boxShadow: 2,
              }}
            >
              ✓
            </Box>
          )}
        </Box>

        {/* 文件名 */}
        <Box
          sx={{
            width: "100%",
            maxWidth: "100%",
            mt: 1,
          }}
        >
          <Tooltip title={uploadingFile.file.name}>
            <Typography
              variant="body2"
              sx={{
                display: "block",
                width: "100%",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                fontSize: "0.875rem",
                lineHeight: 1.2,
                // 确保在最小宽度下也能正常显示
                minWidth: 0,
                color:
                  uploadingFile.status === "error"
                    ? "error.main"
                    : "text.primary",
              }}
            >
              {uploadingFile.file.name}
            </Typography>
          </Tooltip>
        </Box>
      </Box>
    );
  }
);

// 媒体项组件
interface MediaItemProps {
  media: MediaMetadata;
  onMediaSelect: (media: MediaMetadata) => void;
  onDelete: (media: MediaMetadata) => void;
  isSelected: boolean;
  isDisabled: boolean;
  selectionMode: boolean;
  isAdding: boolean;
}

const MediaItem = memo(
  ({
    media,
    onMediaSelect,
    onDelete,
    isSelected,
    isDisabled,
    selectionMode,
    isAdding,
  }: MediaItemProps) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);
    const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
    const menuOpen = Boolean(menuAnchorEl);

    // 处理点击
    const handleClick = useCallback(() => {
      if (!isDisabled && !isAdding) {
        onMediaSelect(media);
      }
    }, [isDisabled, isAdding, onMediaSelect, media]);

    // 处理菜单打开
    const handleMenuOpen = useCallback((e: React.MouseEvent<HTMLElement>) => {
      e.stopPropagation();
      setMenuAnchorEl(e.currentTarget);
    }, []);

    // 处理菜单关闭
    const handleMenuClose = useCallback((e?: React.MouseEvent) => {
      if (e) e.stopPropagation();
      setMenuAnchorEl(null);
    }, []);

    // 处理删除
    const handleDeleteClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        handleMenuClose();
        onDelete(media);
      },
      [onDelete, media, handleMenuClose]
    );

    // 处理下载
    const handleDownload = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        handleMenuClose();
        window.open(media.url, "_blank");
      },
      [media.url, handleMenuClose]
    );

    // 获取媒体预览内容
    const getMediaPreview = () => {
      // 统一的容器样式，确保内容居中
      const commonContainerStyle = {
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        position: "relative",
      };

      if (media.fileType.startsWith("image/")) {
        if (imageError) {
          return (
            <Box
              sx={{
                ...commonContainerStyle,
                backgroundColor: "grey.50",
                flexDirection: "column",
                gap: 1,
              }}
            >
              <ImageIcon sx={{ fontSize: 48, color: "grey.500" }} />
              <Typography
                variant="caption"
                color="text.secondary"
                textAlign="center"
              >
                Image failed to load
              </Typography>
            </Box>
          );
        }

        return (
          <Box sx={commonContainerStyle}>
            <CardMedia
              component="img"
              src={media.url}
              alt={media.fileName}
              onLoad={() => setIsLoaded(true)}
              onError={() => {
                setImageError(true);
                setIsLoaded(true);
              }}
              sx={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "auto",
                height: "auto",
                objectFit: "contain",
              }}
            />
          </Box>
        );
      }

      if (media.fileType.startsWith("video/")) {
        if (imageError) {
          return (
            <Box
              sx={{
                ...commonContainerStyle,
                backgroundColor: "grey.50",
                flexDirection: "column",
                gap: 1,
              }}
            >
              <PlayIcon sx={{ fontSize: 48, color: "grey.500" }} />
              <Typography
                variant="caption"
                color="text.secondary"
                textAlign="center"
              >
                Video failed to load
              </Typography>
            </Box>
          );
        }

        return (
          <Box sx={commonContainerStyle}>
            <CardMedia
              component="video"
              src={media.url}
              onLoad={() => setIsLoaded(true)}
              onError={() => {
                setImageError(true);
                setIsLoaded(true);
              }}
              sx={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "auto",
                height: "auto",
                objectFit: "contain",
              }}
              preload="metadata"
              muted
            />
          </Box>
        );
      }

      // 音频文件显示图标
      if (media.fileType.startsWith("audio/")) {
        return (
          <Box
            sx={{
              ...commonContainerStyle,
              backgroundColor: "grey.100",
            }}
          >
            <AudioIcon sx={{ fontSize: 40, color: "primary.main" }} />
          </Box>
        );
      }

      // 其他文件类型显示通用图标
      return (
        <Box
          sx={{
            ...commonContainerStyle,
            backgroundColor: "grey.50",
          }}
        >
          <ImageIcon sx={{ fontSize: 48, color: "grey.500" }} />
        </Box>
      );
    };

    return (
      <Box
        sx={{
          cursor: isDisabled ? "not-allowed" : isAdding ? "wait" : "pointer",
          opacity: isDisabled ? 0.5 : 1,
          transition: "all 0.2s ease",
          "&:hover": {
            transform: isDisabled || isAdding ? "none" : "translateY(-2px)",
          },
        }}
        onClick={handleClick}
      >
        <Box
          className="media-preview"
          sx={{
            backgroundColor: "grey.100",
            height: "100%",
            width: "100%",
            ...STYLES.previewContainer,
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
            border: isSelected ? "2px solid" : "1px solid",
            borderColor: isSelected ? "primary.main" : "grey.200",
            borderRadius: 2,
            position: "relative",
          }}
        >
          {getMediaPreview()}

          {/* 添加加载状态覆盖层 */}
          {isAdding && (
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                borderRadius: 2,
                backdropFilter: "blur(2px)",
                zIndex: 10,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                <CircularProgress size={24} sx={{ color: "white" }} />
                <Typography
                  variant="caption"
                  sx={{ color: "white", fontWeight: 500 }}
                >
                  Adding to timeline...
                </Typography>
              </Box>
            </Box>
          )}

          {/* 时长显示 */}
          {media.duration && (
            <Box sx={STYLES.durationChip}>{formatDuration(media.duration)}</Box>
          )}

          {/* 操作按钮 */}
          <Box sx={STYLES.menuContainer}>
            <IconButton
              size="small"
              onClick={handleMenuOpen}
              sx={STYLES.menuButton}
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
          </Box>

          {/* 菜单 */}
          <Menu
            anchorEl={menuAnchorEl}
            open={menuOpen}
            onClose={handleMenuClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            onClick={(e) => e.stopPropagation()}
            sx={{
              "& .MuiPaper-root": {
                minWidth: 120,
                boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                borderRadius: 2,
              },
            }}
          >
            <MenuItem
              onClick={handleDownload}
              sx={{
                py: 1,
                px: 2,
                minHeight: "auto",
                fontSize: "0.875rem",
              }}
            >
              <ListItemIcon sx={{ minWidth: 32 }}>
                <DownloadIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary="Download"
                primaryTypographyProps={{
                  fontSize: "0.8rem",
                  fontWeight: 500,
                }}
              />
            </MenuItem>
            <MenuItem
              onClick={handleDeleteClick}
              sx={{
                py: 1,
                px: 2,
                minHeight: "auto",
                fontSize: "0.875rem",
              }}
            >
              <ListItemIcon sx={{ minWidth: 32 }}>
                <DeleteIcon fontSize="small" color="error" />
              </ListItemIcon>
              <ListItemText
                primary="Delete"
                primaryTypographyProps={{
                  fontSize: "0.8rem",
                  fontWeight: 500,
                  color: "error.main",
                }}
              />
            </MenuItem>
          </Menu>
        </Box>

        {/* 文件名 */}
        <Box
          sx={{
            width: "100%",
            maxWidth: "100%",
            mt: 1,
          }}
        >
          <Tooltip title={media.fileName}>
            <Typography
              variant="body2"
              sx={{
                display: "block",
                width: "100%",
                color: "text.secondary",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                // 确保在最小宽度下也能正常显示
                minWidth: 0,
              }}
            >
              {media.fileName}
            </Typography>
          </Tooltip>
        </Box>
      </Box>
    );
  }
);

export const MediaLibrary: React.FC<MediaLibraryProps> = observer(
  ({
    onMediaSelect,
    selectionMode = false,
    allowedTypes,
    maxSelections = 1,
  }) => {
    const theme = useTheme();
    const isXs = useMediaQuery(theme.breakpoints.only("xs"));
    const fileInputRef = useRef<HTMLInputElement>(null);
    const store = useStore();

    const [mediaList, setMediaList] = useState<MediaMetadata[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedMedia, setSelectedMedia] = useState<MediaMetadata[]>([]);
    const [isDragOver, setIsDragOver] = useState(false);

    // 分页和过滤状态
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [searchQuery, setSearchQuery] = useState("");
    const [fileTypeFilter, setFileTypeFilter] = useState("all");
    const [sortBy, setSortBy] = useState("uploadDate");
    const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

    // 对话框状态
    const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
      open: boolean;
      media: MediaMetadata | null;
    }>({ open: false, media: null });

    // 从全局store获取上传状态
    const uploadingFiles = store.uploadManager.currentUploads;

    // 添加媒体加载状态管理
    const [addingMediaStates, setAddingMediaStates] = useState<{
      [key: string]: boolean;
    }>({});

    // 加载媒体列表
    const loadMediaList = useCallback(async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await mediaLibraryService.getMediaList({
          page,
          limit: 20,
          search: searchQuery || undefined,
          fileType:
            fileTypeFilter === "all" ? undefined : fileTypeFilter || undefined,
          sortBy: sortBy as any,
          sortOrder,
        });

        setMediaList(response.items);
        setTotalPages(response.totalPages);
      } catch (err) {
        setError(err instanceof Error ? err.message : "加载媒体文件失败");
      } finally {
        setLoading(false);
      }
    }, [page, searchQuery, fileTypeFilter, sortBy, sortOrder]);

    // 初始加载
    useEffect(() => {
      loadMediaList();
    }, [loadMediaList]);

    // 统一的媒体添加函数
    const handleAddMediaToCanvas = useCallback(
      async (media: MediaMetadata) => {
        const mediaId = media.id;
        setAddingMediaStates((prev) => ({ ...prev, [mediaId]: true }));

        try {
          const id = getUid();

          if (media.fileType.startsWith("image/")) {
            // 添加图片元素
            const imageElement = document.createElement("img");
            imageElement.src = media.url;
            imageElement.id = `image-${id}`;
            imageElement.style.display = "none";
            document.body.appendChild(imageElement);

            imageElement.onload = () => {
              store.addImageElement(imageElement, id, {
                name: media.fileName,
                alt: media.fileName,
              });
              setAddingMediaStates((prev) => ({ ...prev, [mediaId]: false }));
            };

            imageElement.onerror = () => {
              setAddingMediaStates((prev) => ({ ...prev, [mediaId]: false }));
              setError("Failed to load image");
            };
          } else if (media.fileType.startsWith("video/")) {
            // 添加视频元素
            store.addVideoResource(media.url);
            const videoElement = document.createElement("video");
            videoElement.src = media.url;
            videoElement.id = `video-${id}`;
            videoElement.style.display = "none";
            document.body.appendChild(videoElement);

            await Promise.race([
              new Promise((resolve, reject) => {
                videoElement.addEventListener("loadedmetadata", () => {
                  store.addVideoElement(videoElement, id, {
                    name: media.fileName,
                    duration: media.duration,
                  });
                  resolve(true);
                });
                videoElement.addEventListener("error", (e) =>
                  reject(new Error(`Failed to load video: ${e}`))
                );
              }),
              new Promise((_, reject) =>
                setTimeout(
                  () => reject(new Error("Video loading timeout")),
                  30000
                )
              ),
            ]);

            setAddingMediaStates((prev) => ({ ...prev, [mediaId]: false }));
          } else if (media.fileType.startsWith("audio/")) {
            // 添加音频元素
            store.addAudioResource(media.url);
            const audioElement = document.createElement("audio");
            audioElement.src = media.url;
            audioElement.id = `audio-${id}`;
            audioElement.style.display = "none";
            audioElement.preload = "metadata";
            document.body.appendChild(audioElement);

            const handleLoadedMetadata = () => {
              try {
                store.addAudioElement(audioElement, id, {
                  name: media.fileName,
                  duration: media.duration,
                });
                setAddingMediaStates((prev) => ({ ...prev, [mediaId]: false }));
              } catch (error) {
                console.error("Error adding audio element to store:", error);
                setAddingMediaStates((prev) => ({ ...prev, [mediaId]: false }));
                setError("Failed to add audio to timeline");
              }
            };

            const handleError = (e: Event) => {
              console.error("Audio loading error:", e);
              setAddingMediaStates((prev) => ({ ...prev, [mediaId]: false }));
              setError("Failed to load audio");
            };

            audioElement.addEventListener(
              "loadedmetadata",
              handleLoadedMetadata
            );
            audioElement.addEventListener("error", handleError);

            // 音频加载超时处理
            setTimeout(() => {
              if (audioElement.readyState < 1) {
                audioElement.removeEventListener(
                  "loadedmetadata",
                  handleLoadedMetadata
                );
                audioElement.removeEventListener("error", handleError);
                handleError(new Event("timeout"));
              }
            }, 10000);
          } else {
            // 不支持的文件类型
            setAddingMediaStates((prev) => ({ ...prev, [mediaId]: false }));
            setError(`Unsupported file type: ${media.fileType}`);
          }
        } catch (error) {
          console.error("Error adding media to canvas:", error);
          setAddingMediaStates((prev) => ({ ...prev, [mediaId]: false }));
          setError(
            error instanceof Error ? error.message : "Failed to add media"
          );
        }
      },
      [store, setError]
    );

    // 处理媒体选择
    const handleMediaSelect = useCallback(
      (media: MediaMetadata) => {
        if (!selectionMode) {
          // 非选择模式下，直接添加到画布和时间线
          if (onMediaSelect) {
            onMediaSelect(media);
          } else {
            // 如果没有自定义的选择处理器，使用默认的添加到画布逻辑
            handleAddMediaToCanvas(media);
          }
          return;
        }

        if (
          allowedTypes &&
          !allowedTypes.some((type) => media.fileType.startsWith(type))
        ) {
          return;
        }

        setSelectedMedia((prev) => {
          const isSelected = prev.some((m) => m.id === media.id);

          if (isSelected) {
            return prev.filter((m) => m.id !== media.id);
          } else {
            if (prev.length >= maxSelections) {
              return [...prev.slice(1), media];
            }
            return [...prev, media];
          }
        });
      },
      [
        selectionMode,
        allowedTypes,
        maxSelections,
        onMediaSelect,
        handleAddMediaToCanvas,
      ]
    );

    // 处理删除
    const handleDelete = useCallback(async (media: MediaMetadata) => {
      try {
        const success = await mediaLibraryService.deleteMedia(media.id);
        if (success) {
          setMediaList((prev) => prev.filter((m) => m.id !== media.id));
          setSelectedMedia((prev) => prev.filter((m) => m.id !== media.id));
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "删除文件失败");
      }
      setDeleteConfirmDialog({ open: false, media: null });
    }, []);

    // 验证文件类型和大小
    const validateFiles = useCallback(
      (files: File[]): { validFiles: File[]; errors: string[] } => {
        const validFiles: File[] = [];
        const errors: string[] = [];
        const maxSize = 100 * 1024 * 1024; // 100MB
        const allowedExtensions = [
          // 图片格式
          ".jpg",
          ".jpeg",
          ".png",
          ".gif",
          ".webp",
          ".bmp",
          ".tiff",
          // 视频格式
          ".mp4",
          ".mov",
          ".avi",
          ".mkv",
          ".webm",
          ".flv",
          ".wmv",
          // 音频格式
          ".mp3",
          ".wav",
          ".aac",
          ".ogg",
          ".flac",
          ".m4a",
        ];

        files.forEach((file) => {
          // 检查文件大小
          if (file.size > maxSize) {
            errors.push(`${file.name}: 文件大小超过100MB限制`);
            return;
          }

          // 检查文件类型
          const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
          const isValidType =
            allowedExtensions.includes(fileExtension) ||
            file.type.startsWith("image/") ||
            file.type.startsWith("video/") ||
            file.type.startsWith("audio/");

          if (!isValidType) {
            errors.push(`${file.name}: 不支持的文件类型`);
            return;
          }

          validFiles.push(file);
        });

        return { validFiles, errors };
      },
      []
    );

    // 处理文件上传（统一的文件处理逻辑）
    const handleFileUpload = useCallback(
      async (files: File[]) => {
        if (files.length === 0) return;

        setError(null);

        // 验证文件
        const { validFiles, errors } = validateFiles(files);

        if (errors.length > 0) {
          setError(errors.join("; "));
          if (validFiles.length === 0) return;
        }

        // 使用全局uploadManager处理文件上传
        try {
          await store.uploadManager.uploadFiles(validFiles);
          // 上传完成后刷新媒体列表
          setTimeout(() => {
            loadMediaList();
          }, 1000);
        } catch (err) {
          setError(err instanceof Error ? err.message : "上传文件失败");
        }
      },
      [validateFiles, loadMediaList, store.uploadManager]
    );

    // 拖拽事件处理
    const handleDragOver = useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragOver(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      // 只有当鼠标离开整个组件区域时才设置为false
      if (!e.currentTarget.contains(e.relatedTarget as Node)) {
        setIsDragOver(false);
      }
    }, []);

    const handleDrop = useCallback(
      (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragOver(false);

        const files = Array.from(e.dataTransfer.files);
        handleFileUpload(files);
      },
      [handleFileUpload]
    );

    // 修改原来的文件选择处理函数
    const handleFileSelectChange = useCallback(
      async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        await handleFileUpload(Array.from(files));

        // 清空文件输入
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      },
      [handleFileUpload]
    );

    // 处理取消上传
    const handleCancelUpload = useCallback(
      (fileId: string) => {
        store.uploadManager.cancelUpload(fileId);
      },
      [store.uploadManager]
    );

    // 处理重试上传
    const handleRetryUpload = useCallback(
      (fileId: string) => {
        store.uploadManager.retryUpload(fileId).then(() => {
          // 重试完成后刷新媒体列表
          setTimeout(() => {
            loadMediaList();
          }, 1000);
        });
      },
      [store.uploadManager, loadMediaList]
    );

    // 处理Import按钮点击
    const handleImportClick = useCallback(() => {
      fileInputRef.current?.click();
    }, []);

    return (
      <Box
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        sx={{
          position: "relative",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden", // 确保最外层不产生滚动条
        }}
      >
        {/* 隐藏的文件输入元素 */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*,audio/*,.jpg,.jpeg,.png,.gif,.webp,.bmp,.tiff,.mp4,.mov,.avi,.mkv,.webm,.flv,.wmv,.mp3,.wav,.aac,.ogg,.flac,.m4a"
          style={{ display: "none" }}
          onChange={handleFileSelectChange}
        />

        {/* 拖拽覆盖层 */}
        {isDragOver && (
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "grey.50",
              border: "2px dashed",
              borderColor: "primary.main",
              borderRadius: 2,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
              backdropFilter: "blur(2px)",
            }}
          >
            <Box
              sx={{
                textAlign: "center",
              }}
            >
              <UploadIcon
                sx={{
                  color: "primary.main",
                  mb: 2,
                }}
              />
              <Typography
                variant="subtitle1"
                color="primary.main"
                fontWeight="bold"
                gutterBottom
              >
                Drop files here to upload
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Supports images, videos, audio files (max 100MB each)
              </Typography>
            </Box>
          </Box>
        )}

        {/* 顶部标题区域 */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            px: 2,
            mt: 2,
            mb: 3,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              color: "text.primary",
            }}
          >
            Your media
          </Typography>

          {/* Import media 按钮 */}
          <Button
            size="small"
            variant="contained"
            startIcon={<UploadIcon fontSize="small" />}
            onClick={handleImportClick}
          >
            Import
          </Button>
        </Box>

        {/* 过滤控制栏 */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 3,
            flexWrap: "wrap",
            gap: 1,
          }}
        >
          {/* 左侧过滤器 */}
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <ButtonGroup
              variant="outlined"
              size="small"
              sx={{
                px: 2,
                "& .MuiButton-root": {
                  borderColor: "divider",
                  color: "text.secondary",
                  textTransform: "none",
                  "&.Mui-selected": {
                    bgcolor: "primary.main",
                    color: "white",
                    borderColor: "primary.main",
                  },
                },
              }}
            >
              {FILE_TYPE_OPTIONS.map(({ label, value }) => (
                <Button
                  key={value}
                  className={fileTypeFilter === value ? "Mui-selected" : ""}
                  onClick={() => setFileTypeFilter(value)}
                >
                  {label}
                </Button>
              ))}
            </ButtonGroup>
          </Box>
        </Box>

        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* 文件列表容器（可滚动区域） */}
        <Box
          sx={{
            flex: 1,
            minHeight: 0, // 关键：允许flex子元素收缩
            overflowY: "auto",
            overflowX: "hidden",
            border: "1px solid",
            borderColor: "divider",
            borderRadius: 2,
            backgroundColor: "background.paper",
            "&::-webkit-scrollbar": {
              width: "8px",
            },
            "&::-webkit-scrollbar-track": {
              backgroundColor: "rgba(0,0,0,0.05)",
              borderRadius: "4px",
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "rgba(0,0,0,0.2)",
              borderRadius: "4px",
              "&:hover": {
                backgroundColor: "rgba(0,0,0,0.3)",
              },
            },
          }}
        >
          <Box sx={{ p: 2 }}>
            {/* 媒体网格 */}
            {loading ? (
              <SkeletonList />
            ) : (
              <>
                <Grid container spacing={1}>
                  {/* 上传中的文件 */}
                  {uploadingFiles.map((uploadingFile) => (
                    <Grid key={uploadingFile.id} size={GRID_BREAKPOINTS}>
                      <UploadCard
                        uploadingFile={uploadingFile}
                        onCancel={handleCancelUpload}
                        onRetry={handleRetryUpload}
                      />
                    </Grid>
                  ))}

                  {/* 已有的媒体文件 */}
                  {mediaList.map((media) => (
                    <Grid key={media.id} size={GRID_BREAKPOINTS}>
                      <MediaItem
                        media={media}
                        onMediaSelect={handleMediaSelect}
                        onDelete={(media) =>
                          setDeleteConfirmDialog({ open: true, media })
                        }
                        isSelected={selectedMedia.some(
                          (m) => m.id === media.id
                        )}
                        isDisabled={
                          allowedTypes
                            ? !allowedTypes.some((type) =>
                                media.fileType.startsWith(type)
                              )
                            : false
                        }
                        selectionMode={selectionMode}
                        isAdding={addingMediaStates[media.id] || false}
                      />
                    </Grid>
                  ))}
                </Grid>

                {/* 空状态显示 */}
                {mediaList.length === 0 && uploadingFiles.length === 0 && (
                  <Box
                    sx={{
                      textAlign: "center",
                      py: 2,
                      border: "2px dashed",
                      borderColor: "grey.300",
                      borderRadius: 2,
                      backgroundColor: "grey.50",
                      transition: "all 0.2s ease",
                      "&:hover": {
                        borderColor: "primary.main",
                        backgroundColor: "primary.50",
                      },
                    }}
                  >
                    <UploadIcon
                      sx={{
                        fontSize: 50,
                        color: "grey.400",
                        mb: 2,
                      }}
                    />
                    <Typography
                      variant="h6"
                      color="text.secondary"
                      gutterBottom
                      sx={{
                        fontWeight: 600,
                      }}
                    >
                      No media files
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 2 }}
                    >
                      Click Import button or drag files here to start adding
                      media
                    </Typography>
                  </Box>
                )}

                {/* 分页 */}
                {totalPages > 1 && (
                  <Box
                    sx={{ display: "flex", justifyContent: "center", mt: 4 }}
                  >
                    <Pagination
                      count={totalPages}
                      page={page}
                      onChange={(_, newPage) => setPage(newPage)}
                      color="primary"
                    />
                  </Box>
                )}
              </>
            )}
          </Box>
        </Box>

        {/* 删除确认对话框 */}
        <Dialog
          open={deleteConfirmDialog.open}
          onClose={() => setDeleteConfirmDialog({ open: false, media: null })}
          PaperProps={{
            sx: {
              borderRadius: 3,
              maxWidth: 420,
              boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
            },
          }}
        >
          <DialogTitle
            sx={{
              textAlign: "center",
              pb: 1,
              pt: 3,
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography variant="h6" fontWeight="600" color="text.primary">
                Delete Media File
              </Typography>
            </Box>
          </DialogTitle>

          <DialogContent sx={{ px: 3, py: 2 }}>
            <Box sx={{ textAlign: "center" }}>
              {/* 文件信息 */}
              {deleteConfirmDialog.media && (
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 2,
                    p: 2,
                    mb: 2,
                    backgroundColor: "grey.50",
                    borderRadius: 2,
                    border: "1px solid",
                    borderColor: "grey.200",
                  }}
                >
                  {/* 文件类型图标 */}
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 1,
                      backgroundColor: "background.paper",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      border: "1px solid",
                      borderColor: "grey.300",
                    }}
                  >
                    {deleteConfirmDialog.media.fileType.startsWith(
                      "image/"
                    ) && (
                      <ImageIcon sx={{ fontSize: 20, color: "primary.main" }} />
                    )}
                    {deleteConfirmDialog.media.fileType.startsWith(
                      "video/"
                    ) && (
                      <PlayIcon
                        sx={{ fontSize: 20, color: "secondary.main" }}
                      />
                    )}
                    {deleteConfirmDialog.media.fileType.startsWith(
                      "audio/"
                    ) && (
                      <AudioIcon sx={{ fontSize: 20, color: "warning.main" }} />
                    )}
                  </Box>

                  <Box sx={{ flex: 1, textAlign: "left" }}>
                    <Typography
                      variant="body2"
                      fontWeight="500"
                      color="text.primary"
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {deleteConfirmDialog.media.fileName}
                    </Typography>
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{ display: "block", mt: 0.5 }}
                    >
                      {getFileTypeLabel(deleteConfirmDialog.media.fileType)} •{" "}
                      {formatFileSize(deleteConfirmDialog.media.fileSize)}
                    </Typography>
                  </Box>
                </Box>
              )}

              {/* 警告信息 */}
              <Typography variant="body1" color="text.primary" sx={{ mb: 1 }}>
                Are you sure you want to delete this file?
              </Typography>
            </Box>
          </DialogContent>

          <DialogActions
            sx={{
              px: 3,
              py: 2.5,
              gap: 1.5,
              justifyContent: "center",
            }}
          >
            <Button
              variant="outlined"
              onClick={() =>
                setDeleteConfirmDialog({ open: false, media: null })
              }
              sx={{
                minWidth: 100,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 500,
                borderColor: "grey.300",
                color: "text.secondary",
                "&:hover": {
                  borderColor: "grey.400",
                  backgroundColor: "grey.50",
                },
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="error"
              onClick={() =>
                deleteConfirmDialog.media &&
                handleDelete(deleteConfirmDialog.media)
              }
              sx={{
                minWidth: 100,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 500,
                boxShadow: "0 2px 8px rgba(211, 47, 47, 0.3)",
                "&:hover": {
                  boxShadow: "0 4px 12px rgba(211, 47, 47, 0.4)",
                },
              }}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
  }
);
