import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Tooltip,
  useTheme as useMuiTheme,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  ListItemIcon,
  Radio,
} from "@mui/material";
import {
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Settings as SettingsIcon,
  Palette as PaletteIcon,
  Language as LanguageIcon,
} from "@mui/icons-material";
import { useTheme } from "./ThemeContext";
import { useLanguage, Language } from "../i18n/LanguageContext";

interface ThemeToggleProps {
  size?: "small" | "medium" | "large";
  tooltip?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  size = "medium",
  tooltip = true,
}) => {
  const { mode, setTheme } = useTheme();
  const { language, setLanguage, t } = useLanguage();
  const theme = useMuiTheme();
  const isDarkMode = theme.palette.mode === "dark";

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [themeMenuAnchorEl, setThemeMenuAnchorEl] =
    useState<null | HTMLElement>(null);
  const [langMenuAnchorEl, setLangMenuAnchorEl] = useState<null | HTMLElement>(
    null
  );

  const open = Boolean(anchorEl);
  const themeMenuOpen = Boolean(themeMenuAnchorEl);
  const langMenuOpen = Boolean(langMenuAnchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleThemeMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setThemeMenuAnchorEl(event.currentTarget);
    event.stopPropagation();
  };

  const handleThemeMenuClose = (event?: React.MouseEvent<HTMLElement>) => {
    if (event) {
      event.stopPropagation();
    }
    setThemeMenuAnchorEl(null);
  };

  const handleLangMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setLangMenuAnchorEl(event.currentTarget);
    event.stopPropagation();
  };

  const handleLangMenuClose = (event?: React.MouseEvent<HTMLElement>) => {
    if (event) {
      event.stopPropagation();
    }
    setLangMenuAnchorEl(null);
  };

  const handleThemeSelect = (selectedMode: "light" | "dark") => {
    setTheme(selectedMode);
    handleThemeMenuClose();
    handleClose();
  };

  const handleLanguageSelect = (selectedLang: Language) => {
    setLanguage(selectedLang);
    handleLangMenuClose();
    handleClose();
  };

  const button = (
    <IconButton
      onClick={handleClick}
      size={size}
      aria-label={t("settings")}
      aria-controls={open ? "settings-menu" : undefined}
      aria-haspopup="true"
      aria-expanded={open ? "true" : undefined}
    >
      {isDarkMode ? (
        <DarkModeIcon fontSize={size === "small" ? "small" : "medium"} />
      ) : (
        <LightModeIcon fontSize={size === "small" ? "small" : "medium"} />
      )}
    </IconButton>
  );

  return (
    <>
      {tooltip ? <Tooltip title={t("settings")}>{button}</Tooltip> : button}

      <Menu
        id="settings-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 220,
            maxWidth: "100%",
          },
        }}
      >
        {/* 主题选择菜单项 */}
        <MenuItem
          onClick={handleThemeMenuOpen}
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
          aria-controls={themeMenuOpen ? "theme-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={themeMenuOpen ? "true" : undefined}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <PaletteIcon sx={{ mr: 1 }} />
            <Typography variant="body2">{t("theme")}</Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            {isDarkMode ? t("dark_mode") : t("light_mode")}
          </Typography>
        </MenuItem>

        {/* 语言选择菜单项 */}
        <MenuItem
          onClick={handleLangMenuOpen}
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
          aria-controls={langMenuOpen ? "language-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={langMenuOpen ? "true" : undefined}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <LanguageIcon sx={{ mr: 1 }} />
            <Typography variant="body2">{t("language")}</Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            {language === "zh" ? "中文" : "English"}
          </Typography>
        </MenuItem>
      </Menu>

      {/* 主题选择子菜单 */}
      <Menu
        id="theme-menu"
        anchorEl={themeMenuAnchorEl}
        open={themeMenuOpen}
        onClose={handleThemeMenuClose}
        anchorOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <MenuItem
          onClick={() => handleThemeSelect("light")}
          dense
          sx={{ minWidth: 150 }}
        >
          <ListItemIcon>
            <Radio checked={mode === "light"} size="small" />
          </ListItemIcon>
          <Typography variant="body2">{t("light_mode")}</Typography>
        </MenuItem>
        <MenuItem onClick={() => handleThemeSelect("dark")} dense>
          <ListItemIcon>
            <Radio checked={mode === "dark"} size="small" />
          </ListItemIcon>
          <Typography variant="body2">{t("dark_mode")}</Typography>
        </MenuItem>
      </Menu>

      {/* 语言选择子菜单 */}
      <Menu
        id="language-menu"
        anchorEl={langMenuAnchorEl}
        open={langMenuOpen}
        onClose={handleLangMenuClose}
        anchorOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <MenuItem
          onClick={() => handleLanguageSelect("zh")}
          dense
          sx={{ minWidth: 150 }}
        >
          <ListItemIcon>
            <Radio checked={language === "zh"} size="small" />
          </ListItemIcon>
          <Typography variant="body2">{t("chinese")}</Typography>
        </MenuItem>
        <MenuItem onClick={() => handleLanguageSelect("en")} dense>
          <ListItemIcon>
            <Radio checked={language === "en"} size="small" />
          </ListItemIcon>
          <Typography variant="body2">{t("english")}</Typography>
        </MenuItem>
      </Menu>
    </>
  );
};
