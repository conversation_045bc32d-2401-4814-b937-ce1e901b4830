import React, {
  createContext,
  useState,
  useMemo,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import { ThemeProvider } from "@mui/material/styles";
import { lightTheme, darkTheme } from "./theme";

// 存储主题偏好的key
const THEME_STORAGE_KEY = "theme-preference";

// 扩展Context类型以包含更多功能
interface ThemeContextType {
  toggleTheme: () => void;
  setTheme: (mode: "light" | "dark") => void;
  mode: "light" | "dark";
}

const ThemeContext = createContext<ThemeContextType>({
  toggleTheme: () => {},
  setTheme: () => {},
  mode: "light",
});

export const useTheme = () => useContext(ThemeContext);

export const CustomThemeProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // 从localStorage读取主题设置，默认为light
  const [mode, setMode] = useState<"light" | "dark">(() => {
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
    return savedTheme === "dark" ? "dark" : "light";
  });

  const isDarkMode = mode === "dark";

  // 当主题变化时保存到localStorage
  useEffect(() => {
    localStorage.setItem(THEME_STORAGE_KEY, mode);
  }, [mode]);

  // 切换主题函数
  const toggleTheme = () => {
    setMode((prevMode) => (prevMode === "light" ? "dark" : "light"));
  };

  // 直接设置特定主题
  const setTheme = (newMode: "light" | "dark") => {
    setMode(newMode);
  };

  // 根据当前模式选择主题
  const theme = useMemo(
    () => (isDarkMode ? darkTheme : lightTheme),
    [isDarkMode]
  );

  // 提供更丰富的context值
  const contextValue = useMemo(
    () => ({
      toggleTheme,
      setTheme,
      mode,
    }),
    [mode]
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={theme}>{children}</ThemeProvider>
    </ThemeContext.Provider>
  );
};
