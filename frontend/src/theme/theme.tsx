import { createTheme, ThemeOptions } from "@mui/material/styles";

// Define common theme configurations for both light and dark themes
const commonThemeSettings: ThemeOptions = {};

export const lightTheme = createTheme({
  ...commonThemeSettings,
  palette: {
    mode: "light",
    primary: {
      main: "#2563eb", // Professional blue, more subdued than before
      light: "#60a5fa",
      dark: "#1d4ed8",
      contrastText: "#ffffff",
    },
    secondary: {
      main: "#8b5cf6", // Professional purple for accents
      light: "#a78bfa",
      dark: "#7c3aed",
    },
    grey: {
      100: "#f8fafc", // Very light grey
      200: "#e2e8f0",
      300: "#cbd5e1",
      400: "#94a3b8",
      500: "#64748b",
      600: "#475569",
      700: "#334155",
      800: "#1e293b",
      900: "#0f172a",
    },
    background: {
      default: "#f1f5f9", // Clean, professional light background
      paper: "#ffffff",
    },
    success: {
      main: "#059669", // Professional green for success states
      light: "#10b981",
      dark: "#047857",
    },
    error: {
      main: "#dc2626", // Clear red for errors
      light: "#ef4444",
      dark: "#b91c1c",
    },
    warning: {
      main: "#d97706", // Warm orange for warnings
      light: "#f59e0b",
      dark: "#b45309",
    },
    info: {
      main: "#0284c7", // Professional info blue
      light: "#0ea5e9",
      dark: "#0369a1",
    },
    text: {
      primary: "#0f172a", // High contrast dark text
      secondary: "#475569", // Medium contrast secondary text
    },
    divider: "rgba(0, 0, 0, 0.06)",
  },
});

export const darkTheme = createTheme({
  ...commonThemeSettings,
  palette: {
    mode: "dark",
    primary: {
      main: "#3b82f6", // Vibrant blue for dark theme, inspired by professional video editors
      light: "#60a5fa",
      dark: "#2563eb",
      contrastText: "#ffffff",
    },
    secondary: {
      main: "#f59e0b", // Warm amber accent, popular in video editing interfaces
      light: "#fbbf24",
      dark: "#d97706",
    },
    grey: {
      100: "#18181b", // Very dark grey, similar to DaVinci Resolve
      200: "#27272a",
      300: "#3f3f46",
      400: "#52525b",
      500: "#71717a",
      600: "#a1a1aa",
      700: "#d4d4d8",
      800: "#e4e4e7",
      900: "#f4f4f5",
    },
    background: {
      default: "#09090b", // Deep dark background for reduced eye strain
      paper: "#18181b", // Slightly lighter for panels and cards
    },
    success: {
      main: "#22c55e", // Bright success green for dark theme
      light: "#4ade80",
      dark: "#16a34a",
    },
    error: {
      main: "#ef4444", // Clear error red
      light: "#f87171",
      dark: "#dc2626",
    },
    warning: {
      main: "#f59e0b", // Warm warning orange
      light: "#fbbf24",
      dark: "#d97706",
    },
    info: {
      main: "#06b6d4", // Professional cyan for info
      light: "#22d3ee",
      dark: "#0891b2",
    },
    text: {
      primary: "#fafafa", // High contrast light text
      secondary: "#a1a1aa", // Medium contrast secondary text
    },
    divider: "rgba(255, 255, 255, 0.05)", // Very subtle dividers
  },
});
