import React, {
  useRef,
  useEffect,
  useState,
  useCallback,
  useMemo,
} from "react";
import WaveSurfer from "wavesurfer.js";
import { debounce } from "lodash";

interface WaveformOptions {
  width: number;
  height: number;
  waveColor: string;
  progressColor: string;
  backgroundColor: string;
  barWidth?: number;
  barGap?: number;
  normalize?: boolean;
  gradient?: boolean;
  style?: "bars" | "mirror" | "line";
  opacity?: number;
  borderRadius?: number;
}

interface TimelineOptions {
  height: number;
  timeInterval: number;
  primaryLabelInterval: number;
  secondaryLabelInterval: number;
  style: {
    fontSize: string;
    color: string;
  };
}

// 性能优化：使用WeakMap来实现更高效的缓存
const waveformCache = new Map<string, string>();
const MAX_CACHE_SIZE = 100;

/**
 * 缓存管理工具类
 */
class CacheManager {
  static manageCacheSize() {
    if (waveformCache.size > MAX_CACHE_SIZE) {
      const keysToDelete = Array.from(waveformCache.keys()).slice(0, 20);
      keysToDelete.forEach((key) => waveformCache.delete(key));
    }
  }

  static getCachedWaveform(key: string): string | undefined {
    return waveformCache.get(key);
  }

  static setCachedWaveform(key: string, value: string) {
    this.manageCacheSize();
    waveformCache.set(key, value);
  }

  static clearAll() {
    waveformCache.clear();
  }

  static clearForUrl(url: string) {
    const keysToDelete: string[] = [];
    for (const key of waveformCache.keys()) {
      if (key.includes(url)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach((key) => waveformCache.delete(key));
    console.log(
      `Cleared ${keysToDelete.length} waveform cache entries for URL: ${url}`
    );
  }
}

/**
 * 波形数据处理工具类
 */
class WaveformDataProcessor {
  /**
   * 计算指定区间的峰值
   */
  static calculatePeakValue(
    segmentPeaks: Float32Array,
    start: number,
    end: number,
    shouldNormalize: boolean
  ): number {
    let max = 0;
    for (let j = start; j < end && j < segmentPeaks.length; j++) {
      max = Math.max(max, Math.abs(segmentPeaks[j]));
    }
    return shouldNormalize ? Math.min(max * 2, 1) : max;
  }

  /**
   * 为每个条形计算峰值数据
   */
  static calculateBarPeaks(
    segmentPeaks: Float32Array,
    numBars: number,
    samplesPerBar: number,
    shouldNormalize: boolean
  ): number[] {
    const peaks: number[] = [];
    for (let i = 0; i < numBars; i++) {
      const start = i * samplesPerBar;
      const end = start + samplesPerBar;
      const peak = this.calculatePeakValue(
        segmentPeaks,
        start,
        end,
        shouldNormalize
      );
      peaks.push(peak);
    }
    return peaks;
  }
}

/**
 * 画布绘制工具类
 */
class CanvasDrawingHelper {
  /**
   * 设置画布样式
   */
  static setupCanvasStyle(
    ctx: CanvasRenderingContext2D,
    options: WaveformOptions,
    createGradient: (
      ctx: CanvasRenderingContext2D,
      height: number
    ) => CanvasGradient
  ) {
    // 设置填充样式
    if (options.gradient) {
      ctx.fillStyle = createGradient(ctx, options.height);
    } else {
      ctx.fillStyle = options.waveColor;
    }
    // 设置全局透明度
    ctx.globalAlpha = options.opacity || 0.8;
  }

  /**
   * 绘制圆角矩形
   */
  static drawRoundedRect(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    radius: number
  ) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
    ctx.fill();
  }

  /**
   * 绘制单个矩形或圆角矩形
   */
  static drawRect(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    borderRadius?: number
  ) {
    if (borderRadius) {
      this.drawRoundedRect(ctx, x, y, width, height, borderRadius);
    } else {
      ctx.fillRect(x, y, width, height);
    }
  }
}

/**
 * 错误处理工具类
 */
class ErrorHandler {
  static handleWavesurferError(err: any, audioSrc: string): string {
    console.error("Wavesurfer error:", err);

    // 检查是否是音频解码错误
    if (
      err &&
      (err.name === "EncodingError" || err.message?.includes("decode"))
    ) {
      console.warn(
        "Audio decoding failed, possibly due to CORS or invalid audio data:",
        audioSrc
      );
      return "Audio format not supported or CORS issue";
    }

    return "Failed to load audio waveform";
  }

  static handleGenerationError(err: any): string {
    console.error("Error generating waveform:", err);
    return "Failed to generate waveform";
  }

  static handleCreationError(err: any): string {
    console.error("Error creating wavesurfer:", err);
    return "Failed to create waveform";
  }
}

/**
 * 使用 Wavesurfer.js 生成音频波形的 Hook
 * @param audioSrc 音频源URL
 * @param timeFrame 时间帧信息
 * @param options 波形配置选项
 * @returns 波形相关的状态和方法
 */
export const useWavesurferWaveform = (
  audioSrc: string,
  timeFrame: { start: number; end: number },
  options: Partial<WaveformOptions> = {}
) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [waveformDataUrl, setWaveformDataUrl] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [duration, setDuration] = useState(0);

  // 使用 useMemo 稳定默认配置，避免频繁重新创建
  const defaultOptions: WaveformOptions = useMemo(
    () => ({
      width: 800,
      height: 60,
      waveColor: "#4A90E2",
      progressColor: "#2E5BBA",
      backgroundColor: "transparent",
      barWidth: 2,
      barGap: 1,
      normalize: true,
      style: "bars" as const,
      opacity: 0.9,
      borderRadius: 1,
      ...options,
    }),
    [
      options.width,
      options.height,
      options.waveColor,
      options.progressColor,
      options.backgroundColor,
      options.barWidth,
      options.barGap,
      options.normalize,
      options.gradient,
      options.style,
      options.opacity,
      options.borderRadius,
    ]
  );

  // 创建渐变效果
  const createGradient = useCallback(
    (ctx: CanvasRenderingContext2D, height: number) => {
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      const color = defaultOptions.waveColor;

      // 解析颜色并创建渐变
      if (color.startsWith("#")) {
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);

        gradient.addColorStop(
          0,
          `rgba(${r}, ${g}, ${b}, ${defaultOptions.opacity})`
        );
        gradient.addColorStop(
          0.5,
          `rgba(${r}, ${g}, ${b}, ${defaultOptions.opacity * 0.8})`
        );
        gradient.addColorStop(
          1,
          `rgba(${r}, ${g}, ${b}, ${defaultOptions.opacity * 0.3})`
        );
      } else {
        gradient.addColorStop(0, color);
        gradient.addColorStop(0.5, color);
        gradient.addColorStop(1, color);
      }

      return gradient;
    },
    [defaultOptions.waveColor, defaultOptions.opacity]
  );

  // 绘制镜像样式波形
  const drawMirrorWaveform = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      peaks: number[],
      height: number,
      barWidth: number,
      totalBarWidth: number,
      borderRadius?: number
    ) => {
      peaks.forEach((peak, i) => {
        const barHeight = peak * height * 0.4;
        const x = i * totalBarWidth;
        const centerY = height / 2;

        // 绘制上半部分
        CanvasDrawingHelper.drawRect(
          ctx,
          x,
          centerY - barHeight,
          barWidth,
          barHeight,
          borderRadius
        );

        // 绘制下半部分
        CanvasDrawingHelper.drawRect(
          ctx,
          x,
          centerY,
          barWidth,
          barHeight,
          borderRadius
        );
      });
    },
    []
  );

  // 绘制线条样式波形
  const drawLineWaveform = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      peaks: number[],
      height: number,
      barWidth: number,
      totalBarWidth: number,
      createGradient: (
        ctx: CanvasRenderingContext2D,
        height: number
      ) => CanvasGradient
    ) => {
      ctx.strokeStyle = defaultOptions.gradient
        ? createGradient(ctx, height)
        : defaultOptions.waveColor;
      ctx.lineWidth = barWidth;
      ctx.lineCap = "round";
      ctx.lineJoin = "round";

      ctx.beginPath();
      peaks.forEach((peak, i) => {
        const barHeight = peak * height * 0.8;
        const x = i * totalBarWidth;
        const y = (height - barHeight) / 2;

        if (i === 0) {
          ctx.moveTo(x, y + barHeight / 2);
        } else {
          ctx.lineTo(x, y + barHeight / 2);
        }
      });
      ctx.stroke();
    },
    [defaultOptions.gradient, defaultOptions.waveColor]
  );

  // 绘制默认条形样式波形
  const drawBarsWaveform = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      peaks: number[],
      height: number,
      barWidth: number,
      totalBarWidth: number,
      borderRadius?: number
    ) => {
      peaks.forEach((peak, i) => {
        const barHeight = peak * height * 0.8;
        const x = i * totalBarWidth;
        const y = (height - barHeight) / 2;

        CanvasDrawingHelper.drawRect(
          ctx,
          x,
          y,
          barWidth,
          barHeight,
          borderRadius
        );
      });
    },
    []
  );

  // 优化的波形绘制函数
  const drawWaveform = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      segmentPeaks: Float32Array,
      canvas: HTMLCanvasElement
    ) => {
      const { width, height, style, borderRadius } = defaultOptions;
      const barWidth = defaultOptions.barWidth || 2;
      const barGap = defaultOptions.barGap || 1;
      const totalBarWidth = barWidth + barGap;
      const numBars = Math.floor(width / totalBarWidth);
      const samplesPerBar = Math.max(
        1,
        Math.floor(segmentPeaks.length / numBars)
      );

      // 设置画布样式
      CanvasDrawingHelper.setupCanvasStyle(ctx, defaultOptions, createGradient);

      // 预计算所有峰值数据
      const peaks = WaveformDataProcessor.calculateBarPeaks(
        segmentPeaks,
        numBars,
        samplesPerBar,
        defaultOptions.normalize
      );

      // 根据样式绘制波形
      switch (style) {
        case "mirror":
          drawMirrorWaveform(
            ctx,
            peaks,
            height,
            barWidth,
            totalBarWidth,
            borderRadius
          );
          break;
        case "line":
          drawLineWaveform(
            ctx,
            peaks,
            height,
            barWidth,
            totalBarWidth,
            createGradient
          );
          break;
        default:
          drawBarsWaveform(
            ctx,
            peaks,
            height,
            barWidth,
            totalBarWidth,
            borderRadius
          );
          break;
      }
    },
    [
      defaultOptions,
      createGradient,
      drawMirrorWaveform,
      drawLineWaveform,
      drawBarsWaveform,
    ]
  );

  // 性能优化：使用 OffscreenCanvas 生成波形图片
  const generateWaveformImage = useCallback(
    async (wavesurfer: WaveSurfer): Promise<string> => {
      return new Promise((resolve, reject) => {
        // 使用 requestAnimationFrame 确保在下一帧执行
        requestAnimationFrame(() => {
          try {
            // 优先使用 OffscreenCanvas
            let canvas: HTMLCanvasElement | OffscreenCanvas;
            let ctx:
              | CanvasRenderingContext2D
              | OffscreenCanvasRenderingContext2D;

            if (typeof OffscreenCanvas !== "undefined") {
              canvas = new OffscreenCanvas(
                defaultOptions.width,
                defaultOptions.height
              );
              ctx = canvas.getContext("2d")!;
            } else {
              canvas = document.createElement("canvas");
              canvas.width = defaultOptions.width;
              canvas.height = defaultOptions.height;
              ctx = canvas.getContext("2d")!;
            }

            if (!ctx) {
              reject(new Error("Failed to get canvas context"));
              return;
            }

            // 设置背景
            if (defaultOptions.backgroundColor !== "transparent") {
              ctx.fillStyle = defaultOptions.backgroundColor;
              ctx.fillRect(0, 0, defaultOptions.width, defaultOptions.height);
            }

            // 获取波形数据
            const decodedData = wavesurfer.getDecodedData();
            if (!decodedData) {
              reject(new Error("No decoded data available"));
              return;
            }

            const peaks = decodedData.getChannelData(0);
            if (!peaks) {
              reject(new Error("No peaks data available"));
              return;
            }

            // 计算时间范围内的数据
            const totalDuration = wavesurfer.getDuration();
            const startRatio = timeFrame.start / 1000 / totalDuration;
            const endRatio = timeFrame.end / 1000 / totalDuration;

            const startIndex = Math.floor(startRatio * peaks.length);
            const endIndex = Math.floor(endRatio * peaks.length);
            const segmentPeaks = peaks.slice(startIndex, endIndex);

            // 绘制波形
            drawWaveform(
              ctx as CanvasRenderingContext2D,
              segmentPeaks,
              canvas as HTMLCanvasElement
            );

            // 转换为数据URL
            if (canvas instanceof OffscreenCanvas) {
              canvas
                .convertToBlob()
                .then((blob) => {
                  const reader = new FileReader();
                  reader.onload = () => resolve(reader.result as string);
                  reader.onerror = () =>
                    reject(new Error("Failed to convert blob to data URL"));
                  reader.readAsDataURL(blob);
                })
                .catch(reject);
            } else {
              resolve(canvas.toDataURL());
            }
          } catch (err) {
            reject(err);
          }
        });
      });
    },
    [defaultOptions, timeFrame, drawWaveform]
  );

  // 检查音频源可访问性
  const checkAudioAccessibility = useCallback(async (src: string) => {
    if (src.includes("/api/proxy/jamendo-audio")) {
      try {
        const response = await fetch(src, { method: "HEAD" });
        if (!response.ok) {
          throw new Error(`Audio source not accessible: ${response.status}`);
        }
      } catch (fetchError) {
        console.warn("Audio source check failed:", fetchError);
        throw new Error("Audio source not accessible");
      }
    }
  }, []);

  // 防抖的波形生成函数
  const generateWaveformDebounced = useCallback(
    debounce(async (src: string, key: string) => {
      if (!src) return;

      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      // 首先检查缓存
      const cachedWaveform = CacheManager.getCachedWaveform(key);
      if (cachedWaveform) {
        setWaveformDataUrl(cachedWaveform);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // 检查音频源可访问性
        await checkAudioAccessibility(src);

        // 清理之前的实例
        if (wavesurferRef.current) {
          wavesurferRef.current.destroy();
        }

        // 创建新的 Wavesurfer 实例 - 不需要DOM元素
        const wavesurfer = WaveSurfer.create({
          container: document.createElement("div"), // 临时容器
          waveColor: defaultOptions.waveColor,
          progressColor: defaultOptions.progressColor,
          height: defaultOptions.height,
          normalize: defaultOptions.normalize,
          interact: false,
          backend: "WebAudio", // 使用 WebAudio backend 提高性能
        });

        wavesurferRef.current = wavesurfer;

        // 监听加载完成事件
        const handleReady = async () => {
          if (abortControllerRef.current?.signal.aborted) {
            return;
          }

          try {
            setDuration(wavesurfer.getDuration());

            // 生成波形图片
            const dataUrl = await generateWaveformImage(wavesurfer);

            if (abortControllerRef.current?.signal.aborted) {
              return;
            }

            // 缓存结果
            CacheManager.setCachedWaveform(key, dataUrl);

            setWaveformDataUrl(dataUrl);
            setIsLoading(false);
          } catch (err) {
            if (!abortControllerRef.current?.signal.aborted) {
              setError(ErrorHandler.handleGenerationError(err));
              setIsLoading(false);
            }
          }
        };

        const handleError = (err: any) => {
          if (!abortControllerRef.current?.signal.aborted) {
            setError(ErrorHandler.handleWavesurferError(err, src));
            setIsLoading(false);

            // 清理失败的Wavesurfer实例
            if (wavesurfer) {
              try {
                wavesurfer.destroy();
              } catch (destroyErr) {
                console.warn(
                  "Error destroying failed wavesurfer instance:",
                  destroyErr
                );
              }
            }
          }
        };

        wavesurfer.on("ready", handleReady);
        wavesurfer.on("error", handleError);

        // 加载音频，添加超时处理
        const loadPromise = wavesurfer.load(src);

        // 设置超时，防止长时间等待
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error("Audio load timeout"));
          }, 60000); // 60秒超时
        });

        await Promise.race([loadPromise, timeoutPromise]);
      } catch (err) {
        if (!abortControllerRef.current?.signal.aborted) {
          setError(ErrorHandler.handleCreationError(err));
          setIsLoading(false);
        }
      }
    }, 300),
    [defaultOptions, generateWaveformImage, checkAudioAccessibility]
  );

  // 生成稳定的缓存键
  const cacheKey = useMemo(() => {
    const optionsHash = JSON.stringify({
      width: defaultOptions.width,
      height: defaultOptions.height,
      waveColor: defaultOptions.waveColor,
      style: defaultOptions.style,
      gradient: defaultOptions.gradient,
      opacity: defaultOptions.opacity,
      borderRadius: defaultOptions.borderRadius,
    });
    return `${audioSrc}_${timeFrame.start}_${timeFrame.end}_${btoa(
      optionsHash
    )}`;
  }, [audioSrc, timeFrame.start, timeFrame.end, defaultOptions]);

  // 初始缓存检查
  useEffect(() => {
    if (audioSrc && cacheKey) {
      const cachedWaveform = CacheManager.getCachedWaveform(cacheKey);
      if (cachedWaveform) {
        setWaveformDataUrl(cachedWaveform);
        setIsLoading(false);
        return;
      }
    }
  }, [audioSrc, cacheKey]);

  // 当音频源或时间帧改变时重新生成波形
  useEffect(() => {
    if (audioSrc && !CacheManager.getCachedWaveform(cacheKey)) {
      generateWaveformDebounced(audioSrc, cacheKey);
    }

    return () => {
      generateWaveformDebounced.cancel();
    };
  }, [audioSrc, cacheKey, generateWaveformDebounced]);

  // 监听全局清理事件
  useEffect(() => {
    const handleClearWaveformCache = (event: CustomEvent) => {
      const { audioSrc: targetAudioSrc } = event.detail;
      if (targetAudioSrc && targetAudioSrc === audioSrc) {
        CacheManager.clearForUrl(targetAudioSrc);
      }
    };

    window.addEventListener(
      "clearWaveformCache",
      handleClearWaveformCache as EventListener
    );

    return () => {
      window.removeEventListener(
        "clearWaveformCache",
        handleClearWaveformCache as EventListener
      );
    };
  }, [audioSrc]);

  // 清理函数
  useEffect(() => {
    return () => {
      // 取消所有进行中的操作
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // 取消防抖函数
      generateWaveformDebounced.cancel();

      // 清理Wavesurfer实例
      if (wavesurferRef.current) {
        try {
          wavesurferRef.current.destroy();
        } catch (err) {
          console.warn("Error destroying wavesurfer on cleanup:", err);
        }
        wavesurferRef.current = null;
      }

      // 清理状态
      setWaveformDataUrl("");
      setIsLoading(false);
      setError(null);
    };
  }, [generateWaveformDebounced]);

  // 清理缓存的方法
  const clearCache = useCallback(() => {
    CacheManager.clearAll();
  }, []);

  return {
    containerRef,
    waveformDataUrl,
    isLoading,
    error,
    duration,
    wavesurfer: wavesurferRef.current,
    clearCache,
  };
};

// 导出一个用于清理全局缓存的函数
export const clearGlobalWaveformCache = () => {
  CacheManager.clearAll();
};

// 导出一个用于清理特定URL相关缓存的函数
export const clearWaveformCacheForUrl = (url: string) => {
  CacheManager.clearForUrl(url);
};
