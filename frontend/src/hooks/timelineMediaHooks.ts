import React, { useCallback, useEffect, useRef } from "react";
import { StoreContext } from "../store";
import { useWavesurferWaveform } from "./useWavesurferWaveform";

/**
 * 处理GIF静态缩略图的Hook
 * @param element GIF元素
 * @returns 包含缩略图URL和加载状态的对象
 */
export const useGifStaticThumbnail = (element: any) => {
  const [gifStaticThumbnail, setGifStaticThumbnail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  useEffect(() => {
    if (element.type !== "gif" || !element.properties?.src) {
      setGifStaticThumbnail("");
      setIsLoading(false);
      setHasError(false);
      return;
    }

    // 重置状态
    setGifStaticThumbnail("");
    setIsLoading(true);
    setHasError(false);

    // 创建一个临时的img元素来加载GIF
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = element.properties.src;

    img.onload = () => {
      // 创建canvas来绘制GIF的第一帧
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      // 绘制图片（这会自动显示第一帧）
      ctx.drawImage(img, 0, 0);

      // 转换为静态图片URL
      setGifStaticThumbnail(canvas.toDataURL("image/jpeg", 0.8));
      setIsLoading(false);
    };

    img.onerror = (err) => {
      console.error("Error loading GIF for static thumbnail:", err);
      setGifStaticThumbnail("");
      setIsLoading(false);
      setHasError(true);
    };

    // 清理函数
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [element.type, element.properties?.src]);

  return {
    gifStaticThumbnail,
    isLoading,
    hasError,
  };
};

/**
 * 处理视频缩略图的Hook
 * @param element 视频元素
 * @returns 包含缩略图URL和加载状态的对象
 */
export const useVideoThumbnail = (element: any) => {
  const [videoThumbnail, setVideoThumbnail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [loadingProgress, setLoadingProgress] = React.useState(0);
  const [hasError, setHasError] = React.useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  useEffect(() => {
    if (
      element.type !== "video" ||
      !element.properties?.src ||
      element.properties.src.trim() === ""
    ) {
      setVideoThumbnail("");
      setIsLoading(false);
      setLoadingProgress(0);
      setHasError(false);
      return;
    }

    // 检查DOM元素是否存在，如果不存在说明元素已被删除
    if (element.properties.elementId) {
      const domElement = document.getElementById(element.properties.elementId);
      if (!domElement) {
        console.log(
          "Video DOM element not found, element may have been deleted"
        );
        setVideoThumbnail("");
        setIsLoading(false);
        setLoadingProgress(0);
        setHasError(false);
        return;
      }
    }

    // 重置状态
    setVideoThumbnail("");
    setIsLoading(true);
    setLoadingProgress(0);
    setHasError(false);

    const video = document.createElement("video");
    video.crossOrigin = "anonymous";
    video.src = element.properties.src;

    // 监听加载进度
    video.onprogress = () => {
      if (video.buffered.length > 0) {
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        const duration = video.duration;
        if (duration > 0) {
          const progress = Math.min((bufferedEnd / duration) * 100, 100);
          setLoadingProgress(progress);
        }
      }
    };

    video.onloadstart = () => {
      setLoadingProgress(10); // 开始加载
    };

    video.onloadedmetadata = () => {
      setLoadingProgress(50); // 元数据加载完成
      video.currentTime = 0;
    };

    video.onseeked = () => {
      setLoadingProgress(80); // 定位完成
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      try {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        setVideoThumbnail(canvas.toDataURL("image/jpeg"));
        setLoadingProgress(100); // 缩略图生成完成
        setIsLoading(false);
      } catch (error) {
        console.error("Error generating video thumbnail (CORS issue):", error);
        // 如果是CORS错误，尝试不使用crossOrigin重新加载
        if (error instanceof DOMException && error.name === "SecurityError") {
          console.log("Retrying without crossOrigin...");
          const fallbackVideo = document.createElement("video");
          fallbackVideo.src = element.properties.src;

          fallbackVideo.onseeked = () => {
            try {
              const fallbackCanvas = document.createElement("canvas");
              fallbackCanvas.width = fallbackVideo.videoWidth;
              fallbackCanvas.height = fallbackVideo.videoHeight;
              const fallbackCtx = fallbackCanvas.getContext("2d");
              if (fallbackCtx) {
                fallbackCtx.drawImage(
                  fallbackVideo,
                  0,
                  0,
                  fallbackCanvas.width,
                  fallbackCanvas.height
                );
                setVideoThumbnail(fallbackCanvas.toDataURL("image/jpeg"));
                setLoadingProgress(100);
                setIsLoading(false);
              } else {
                setIsLoading(false);
                setHasError(true);
              }
            } catch (fallbackError) {
              console.error(
                "Fallback thumbnail generation also failed:",
                fallbackError
              );
              setIsLoading(false);
              setHasError(true);
            }
          };

          fallbackVideo.onerror = () => {
            setIsLoading(false);
            setHasError(true);
          };

          fallbackVideo.onloadedmetadata = () => {
            fallbackVideo.currentTime = 0;
          };
        } else {
          setIsLoading(false);
          setHasError(true);
        }
      }
    };

    video.onerror = (err) => {
      // 检查是否是因为元素被删除导致的错误
      if (!element.properties?.src || element.properties.src.trim() === "") {
        console.log("Video element was removed, skipping error handling");
        return;
      }

      // 检查DOM元素是否还存在（更强的检查）
      if (element.properties.elementId) {
        const domElement = document.getElementById(
          element.properties.elementId
        );
        if (!domElement) {
          console.log("Video DOM element was removed, skipping error handling");
          setIsLoading(false);
          setHasError(false);
          setVideoThumbnail("");
          return;
        }
      }

      console.error("Error loading video for thumbnail:", err);
      setIsLoading(false);
      setHasError(true);
      setLoadingProgress(0);
    };

    videoRef.current = video;

    return () => {
      if (videoRef.current) {
        videoRef.current.src = "";
        videoRef.current = null;
      }
    };
  }, [element.type, element.properties?.src]);

  return {
    videoThumbnail,
    isLoading,
    loadingProgress,
    hasError,
  };
};

/**
 * 处理音频波形的Hook (使用 Wavesurfer.js)
 * @param element 音频元素
 * @returns 音频波形图URL
 */
export const useAudioWaveform = (element: any) => {
  const [audioWaveform, setAudioWaveform] = React.useState("");
  const [isInitialized, setIsInitialized] = React.useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const store = React.useContext(StoreContext);
  const waveformCacheRef = useRef(new Map<string, string>());

  // 只对音频元素处理波形
  const isAudioElement = element.type === "audio";
  const audioSrc = isAudioElement ? element.properties?.src : "";

  // 使用 Wavesurfer.js Hook - 只在是音频元素时才传入有效的src
  const { containerRef, waveformDataUrl, isLoading, error } =
    useWavesurferWaveform(audioSrc, element.timeFrame, {
      width: Math.max(
        200,
        ((element.timeFrame.end - element.timeFrame.start) / 1000) * 50
      ), // 根据时长调整宽度
      height: 60,
      waveColor: "#4A90E2",
      progressColor: "#2E5BBA",
      backgroundColor: "#3f51b5",
      barWidth: 1,
      barGap: 1,
      normalize: true,
    });

  // 当 Wavesurfer.js 生成的波形可用时，更新状态
  React.useEffect(() => {
    if (isAudioElement && waveformDataUrl) {
      setAudioWaveform(waveformDataUrl);
      setIsInitialized(true);
    } else if (!isAudioElement) {
      // 对于非音频元素，直接设置为初始化状态
      setAudioWaveform("");
      setIsInitialized(true);
    }
  }, [isAudioElement, waveformDataUrl]);

  // 防止在初始化前显示空白，造成闪烁
  React.useEffect(() => {
    if (isAudioElement && !isLoading && !waveformDataUrl && !isInitialized) {
      // 如果不在加载中且没有波形数据，设置为初始化状态
      setIsInitialized(true);
    }
  }, [isAudioElement, isLoading, waveformDataUrl, isInitialized]);

  // 创建隐藏的容器用于 Wavesurfer.js 渲染
  React.useEffect(() => {
    if (containerRef.current) {
      // 确保容器是隐藏的，只用于生成波形数据
      containerRef.current.style.position = "absolute";
      containerRef.current.style.left = "-9999px";
      containerRef.current.style.top = "-9999px";
      containerRef.current.style.visibility = "hidden";
      containerRef.current.style.pointerEvents = "none";
    }
  }, [containerRef]);

  // 添加隐藏的容器到 DOM 中用于 Wavesurfer.js
  React.useEffect(() => {
    if (!document.getElementById("wavesurfer-container")) {
      const container = document.createElement("div");
      container.id = "wavesurfer-container";
      container.style.position = "absolute";
      container.style.left = "-9999px";
      container.style.top = "-9999px";
      container.style.visibility = "hidden";
      container.style.pointerEvents = "none";
      document.body.appendChild(container);
    }
  }, []);

  // 返回波形数据URL，防止闪烁
  return isInitialized ? audioWaveform : "";
};
