import axios from 'axios';
import { 
  Template, 
  TemplateFilter, 
  CreateTemplateRequest, 
  TemplateCategory 
} from '../types';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080';

// API响应接口
export interface TemplateApiResponse {
  templates: Template[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TemplateStatsResponse {
  totalTemplates: number;
  categoryCounts: Record<TemplateCategory, number>;
  popularTags: Array<{ tag: string; count: number }>;
  averageRating: number;
}

/**
 * 获取模板列表
 */
export const getTemplates = async (
  filter: TemplateFilter = {},
  page: number = 1,
  limit: number = 20
): Promise<TemplateApiResponse> => {
  try {
    const params = new URLSearchParams();
    
    // 添加分页参数
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    // 添加过滤参数
    if (filter.category) params.append('category', filter.category);
    if (filter.tags?.length) params.append('tags', filter.tags.join(','));
    if (filter.hasVideo !== undefined) params.append('hasVideo', filter.hasVideo.toString());
    if (filter.hasAudio !== undefined) params.append('hasAudio', filter.hasAudio.toString());
    if (filter.hasText !== undefined) params.append('hasText', filter.hasText.toString());
    if (filter.hasAnimations !== undefined) params.append('hasAnimations', filter.hasAnimations.toString());
    if (filter.complexity) params.append('complexity', filter.complexity);
    if (filter.aspectRatio) params.append('aspectRatio', filter.aspectRatio);
    if (filter.sortBy) params.append('sortBy', filter.sortBy);
    if (filter.sortOrder) params.append('sortOrder', filter.sortOrder);
    
    // 添加时长过滤
    if (filter.duration?.min !== undefined) params.append('durationMin', filter.duration.min.toString());
    if (filter.duration?.max !== undefined) params.append('durationMax', filter.duration.max.toString());
    
    const response = await axios.get(`${API_BASE_URL}/api/templates?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('获取模板列表失败:', error);
    throw error;
  }
};

/**
 * 根据ID获取单个模板
 */
export const getTemplateById = async (id: string): Promise<Template> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/templates/${id}`);
    return response.data;
  } catch (error) {
    console.error('获取模板详情失败:', error);
    throw error;
  }
};

/**
 * 创建新模板
 */
export const createTemplate = async (templateData: CreateTemplateRequest): Promise<Template> => {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/templates`, templateData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('创建模板失败:', error);
    throw error;
  }
};

/**
 * 更新模板
 */
export const updateTemplate = async (
  id: string, 
  templateData: Partial<CreateTemplateRequest>
): Promise<Template> => {
  try {
    const response = await axios.put(`${API_BASE_URL}/api/templates/${id}`, templateData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('更新模板失败:', error);
    throw error;
  }
};

/**
 * 删除模板
 */
export const deleteTemplate = async (id: string): Promise<void> => {
  try {
    await axios.delete(`${API_BASE_URL}/api/templates/${id}`);
  } catch (error) {
    console.error('删除模板失败:', error);
    throw error;
  }
};

/**
 * 获取热门模板
 */
export const getPopularTemplates = async (limit: number = 10): Promise<Template[]> => {
  try {
    const response = await getTemplates(
      { sortBy: 'popular', sortOrder: 'desc' },
      1,
      limit
    );
    return response.templates;
  } catch (error) {
    console.error('获取热门模板失败:', error);
    throw error;
  }
};

/**
 * 获取最新模板
 */
export const getLatestTemplates = async (limit: number = 10): Promise<Template[]> => {
  try {
    const response = await getTemplates(
      { sortBy: 'newest', sortOrder: 'desc' },
      1,
      limit
    );
    return response.templates;
  } catch (error) {
    console.error('获取最新模板失败:', error);
    throw error;
  }
};

/**
 * 按分类获取模板
 */
export const getTemplatesByCategory = async (
  category: TemplateCategory,
  page: number = 1,
  limit: number = 20
): Promise<TemplateApiResponse> => {
  try {
    return await getTemplates({ category }, page, limit);
  } catch (error) {
    console.error('按分类获取模板失败:', error);
    throw error;
  }
};

/**
 * 搜索模板
 */
export const searchTemplates = async (
  query: string,
  filter: TemplateFilter = {},
  page: number = 1,
  limit: number = 20
): Promise<TemplateApiResponse> => {
  try {
    const params = new URLSearchParams();
    params.append('q', query);
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    // 添加过滤参数
    if (filter.category) params.append('category', filter.category);
    if (filter.tags?.length) params.append('tags', filter.tags.join(','));
    if (filter.sortBy) params.append('sortBy', filter.sortBy);
    if (filter.sortOrder) params.append('sortOrder', filter.sortOrder);
    
    const response = await axios.get(`${API_BASE_URL}/api/templates/search?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('搜索模板失败:', error);
    throw error;
  }
};

/**
 * 获取模板统计信息
 */
export const getTemplateStats = async (): Promise<TemplateStatsResponse> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/templates/stats`);
    return response.data;
  } catch (error) {
    console.error('获取模板统计失败:', error);
    throw error;
  }
};

/**
 * 为模板评分
 */
export const rateTemplate = async (id: string, rating: number): Promise<void> => {
  try {
    await axios.post(`${API_BASE_URL}/api/templates/${id}/rate`, { rating });
  } catch (error) {
    console.error('模板评分失败:', error);
    throw error;
  }
};

/**
 * 增加模板使用次数
 */
export const incrementTemplateUsage = async (id: string): Promise<void> => {
  try {
    await axios.post(`${API_BASE_URL}/api/templates/${id}/use`);
  } catch (error) {
    console.error('更新模板使用次数失败:', error);
    // 这个错误不应该阻止用户使用模板，所以只记录日志
  }
};

/**
 * 上传模板缩略图
 */
export const uploadTemplateThumbnail = async (
  templateId: string,
  file: File
): Promise<string> => {
  try {
    const formData = new FormData();
    formData.append('thumbnail', file);
    
    const response = await axios.post(
      `${API_BASE_URL}/api/templates/${templateId}/thumbnail`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    return response.data.thumbnailUrl;
  } catch (error) {
    console.error('上传模板缩略图失败:', error);
    throw error;
  }
};

/**
 * 生成模板预览视频
 */
export const generateTemplatePreview = async (templateId: string): Promise<string> => {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/templates/${templateId}/preview`);
    return response.data.taskId;
  } catch (error) {
    console.error('生成模板预览失败:', error);
    throw error;
  }
};

/**
 * 获取模板分类列表
 */
export const getTemplateCategories = (): TemplateCategory[] => {
  return [
    'business',
    'social',
    'education',
    'entertainment',
    'marketing',
    'presentation',
    'intro',
    'outro',
    'logo',
    'slideshow',
    'promotional',
    'tutorial',
    'news',
    'sports',
    'travel',
    'food',
    'fashion',
    'technology',
    'music',
    'gaming',
    'other'
  ];
};

/**
 * 获取分类显示名称
 */
export const getCategoryDisplayName = (category: TemplateCategory): string => {
  const categoryNames: Record<TemplateCategory, string> = {
    business: '商务',
    social: '社交媒体',
    education: '教育',
    entertainment: '娱乐',
    marketing: '营销',
    presentation: '演示',
    intro: '开场',
    outro: '结尾',
    logo: '标志',
    slideshow: '幻灯片',
    promotional: '推广',
    tutorial: '教程',
    news: '新闻',
    sports: '体育',
    travel: '旅行',
    food: '美食',
    fashion: '时尚',
    technology: '科技',
    music: '音乐',
    gaming: '游戏',
    other: '其他'
  };
  
  return categoryNames[category] || category;
};
