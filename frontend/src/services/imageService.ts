import axios from "axios";

// 定义图片源类型
export type ImageSource = "pexels" | "pixabay";

// API 配置
const PEXELS_API_KEY =
  "nF2P3Rmh2RKgwAihislOqfZHFRt0JpRVyDvQjgnLKfkj2KnpAgLG9TQ1";
const PEXELS_API_URL = "https://api.pexels.com/v1/curated";
const PEXELS_SEARCH_API_URL = "https://api.pexels.com/v1/search";

const PIXABAY_API_KEY = "**********************************";
const PIXABAY_API_URL = "https://pixabay.com/api/";

const PER_PAGE = 10; // 每页图片数量

// 图片接口
export interface ImageFile {
  id: string | number;
  width: number;
  height: number;
  photographer: string;
  src: {
    original: string;
    large: string;
    medium: string;
    small: string;
  };
  alt: string;
}

// API响应接口
export interface ImageApiResponse {
  total: number;
  images: ImageFile[];
}

// 将Pixabay图片转换为统一格式
const mapPixabayImagesToCommonFormat = (images: any[]): ImageFile[] => {
  return images.map((image) => ({
    id: image.id,
    width: image.imageWidth,
    height: image.imageHeight,
    photographer: image.user,
    src: {
      original: image.largeImageURL,
      large: image.largeImageURL,
      medium: image.webformatURL,
      small: image.previewURL,
    },
    alt: image.tags,
  }));
};

// 将Pexels图片转换为统一格式
const mapPexelsImagesToCommonFormat = (images: any[]): ImageFile[] => {
  return images.map((image) => ({
    id: image.id,
    width: image.width,
    height: image.height,
    photographer: image.photographer,
    src: {
      original: image.src.original,
      large: image.src.large,
      medium: image.src.medium,
      small: image.src.small,
    },
    alt: image.alt,
  }));
};

// 获取图片（通用方法）
export const getImages = async (
  source: ImageSource,
  page: number = 1,
  searchQuery: string = "",
  perPage: number = PER_PAGE
): Promise<ImageApiResponse> => {
  try {
    let response;
    let images: ImageFile[] = [];
    let total = 0;

    if (source === "pexels") {
      // 使用Pexels API
      const url = searchQuery
        ? `${PEXELS_SEARCH_API_URL}?query=${encodeURIComponent(
            searchQuery
          )}&per_page=${perPage}&page=${page}`
        : `${PEXELS_API_URL}?per_page=${perPage}&page=${page}`;

      response = await axios.get(url, {
        headers: {
          Authorization: PEXELS_API_KEY,
        },
      });

      images = mapPexelsImagesToCommonFormat(response.data.photos);
      total = response.data.total_results || 0;
    } else {
      // 使用Pixabay API
      const url = `${PIXABAY_API_URL}?key=${PIXABAY_API_KEY}&per_page=${perPage}&page=${page}`;
      const params = searchQuery ? `&q=${encodeURIComponent(searchQuery)}` : "";

      response = await axios.get(`${url}${params}`);
      images = mapPixabayImagesToCommonFormat(response.data.hits);
      total = response.data.totalHits || 0;
    }

    return {
      images,
      total,
    };
  } catch (error) {
    console.error(`获取${source}图片失败:`, error);
    return { images: [], total: 0 };
  }
};

// 根据标签搜索图片
export const getImagesByTag = async (
  source: ImageSource,
  tag: string,
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<ImageApiResponse> => {
  return getImages(source, page, tag, perPage);
};

// 获取图片标签配置
export const getImageTags = () => {
  return {
    common: [
      "nature",
      "city",
      "people",
      "animals",
      "food",
      "technology",
      "business",
      "travel",
    ],
    pixabay: [
      "backgrounds",
      "fashion",
      "architecture",
      "education",
      "science",
      "health",
      "places",
      "sports",
    ],
    pexels: [
      "sky",
      "night",
      "water",
      "beach",
      "mountain",
      "flowers",
      "abstract",
      "vintage",
    ],
  };
};
