import axios from "axios";

// Jamendo API 配置
const API_KEY = process.env.REACT_APP_JAMENDO_API_KEY || "922a636d"; // 从环境变量获取密钥
const BASE_URL = "https://api.jamendo.com/v3.0";

// 获取后端API基础URL
const API_BASE_URL =
  process.env.REACT_APP_API_BASE_URL || "http://localhost:8080";

/**
 * 将Jamendo音频URL转换为代理URL以解决跨域问题
 * @param jamendoUrl 原始Jamendo音频URL
 * @returns 代理后的URL
 */
export const getProxiedJamendoUrl = (jamendoUrl: string): string => {
  if (
    !jamendoUrl ||
    (!jamendoUrl.includes("jamendo.com") && !jamendoUrl.includes("jamen.do"))
  ) {
    return jamendoUrl; // 如果不是Jamendo URL，直接返回原URL
  }

  // 使用后端代理来解决跨域问题
  return `${API_BASE_URL}/api/proxy/jamendo-audio?url=${encodeURIComponent(
    jamendoUrl
  )}`;
};

// 音频类型接口
export interface JamendoTrack {
  id: string;
  name: string;
  duration: number;
  artist_name: string;
  audiodownload: string; // 音频下载链接
  image: string; // 封面图片
}

// API响应接口
export interface JamendoApiResponse {
  headers: {
    status: string;
    code: number;
    error_message: string;
    results_count: number;
    total_count: number; // 总结果数，用于分页
  };
  results: any[];
}

// 获取热门音乐
export const getPopularTracks = async (
  limit: number = 20,
  offset: number = 0
): Promise<{ tracks: JamendoTrack[]; total: number }> => {
  try {
    const response = await axios.get<JamendoApiResponse>(
      `${BASE_URL}/tracks/`,
      {
        params: {
          client_id: API_KEY,
          format: "json",
          limit,
          offset,
          boost: "popularity_total",
          include: "musicinfo",
          audioformat: "mp32",
        },
      }
    );

    // 打印完整响应以检查结构
    console.log("Jamendo API 响应:", response.data);

    // 计算总数，若API返回不正确则使用合理的默认值
    let totalCount = 0;
    if (
      response.data.headers &&
      typeof response.data.headers.total_count === "number"
    ) {
      totalCount = response.data.headers.total_count;
    } else if (
      response.data.headers &&
      typeof response.data.headers.results_count === "number"
    ) {
      // 备选：使用results_count乘以一个因子作为估计总数
      totalCount = response.data.headers.results_count * 10;
    } else {
      // 如果无法获取总数，则假设至少有100条数据
      totalCount = Math.max(100, response.data.results.length);
    }

    // 确保返回的总数至少比当前加载的结果数量多
    totalCount = Math.max(
      totalCount,
      offset + response.data.results.length + 1
    );

    return {
      tracks: response.data.results.map((track: any) => ({
        id: track.id,
        name: track.name,
        duration: track.duration,
        artist_name: track.artist_name,
        audiodownload: track.audiodownload,
        image: track.image,
      })),
      total: totalCount,
    };
  } catch (error) {
    console.error("获取Jamendo音乐失败:", error);
    return { tracks: [], total: 0 };
  }
};

// 搜索音乐
export const searchTracks = async (
  query: string,
  limit: number = 20,
  offset: number = 0
): Promise<{ tracks: JamendoTrack[]; total: number }> => {
  try {
    const response = await axios.get<JamendoApiResponse>(
      `${BASE_URL}/tracks/`,
      {
        params: {
          client_id: API_KEY,
          format: "json",
          limit,
          offset,
          namesearch: query,
          include: "musicinfo",
          audioformat: "mp32",
        },
      }
    );

    // 计算总数，若API返回不正确则使用合理的默认值
    let totalCount = 0;
    if (
      response.data.headers &&
      typeof response.data.headers.total_count === "number"
    ) {
      totalCount = response.data.headers.total_count;
    } else if (
      response.data.headers &&
      typeof response.data.headers.results_count === "number"
    ) {
      // 备选：使用results_count乘以一个因子作为估计总数
      totalCount = response.data.headers.results_count * 10;
    } else {
      // 如果无法获取总数，则假设至少有100条数据
      totalCount = Math.max(100, response.data.results.length);
    }

    // 确保返回的总数至少比当前加载的结果数量多
    totalCount = Math.max(
      totalCount,
      offset + response.data.results.length + 1
    );

    return {
      tracks: response.data.results.map((track: any) => ({
        id: track.id,
        name: track.name,
        duration: track.duration,
        artist_name: track.artist_name,
        audiodownload: track.audiodownload,
        image: track.image,
      })),
      total: totalCount,
    };
  } catch (error) {
    console.error("搜索Jamendo音乐失败:", error);
    return { tracks: [], total: 0 };
  }
};

// 根据标签获取音乐
export const getTracksByTags = async (
  tags: string,
  limit: number = 20,
  offset: number = 0
): Promise<{ tracks: JamendoTrack[]; total: number }> => {
  try {
    const response = await axios.get<JamendoApiResponse>(
      `${BASE_URL}/tracks/`,
      {
        params: {
          client_id: API_KEY,
          format: "json",
          limit,
          offset,
          tags: tags,
          include: "musicinfo",
          audioformat: "mp32",
        },
      }
    );

    // 计算总数，若API返回不正确则使用合理的默认值
    let totalCount = 0;
    if (
      response.data.headers &&
      typeof response.data.headers.total_count === "number"
    ) {
      totalCount = response.data.headers.total_count;
    } else if (
      response.data.headers &&
      typeof response.data.headers.results_count === "number"
    ) {
      // 备选：使用results_count乘以一个因子作为估计总数
      totalCount = response.data.headers.results_count * 10;
    } else {
      // 如果无法获取总数，则假设至少有100条数据
      totalCount = Math.max(100, response.data.results.length);
    }

    // 确保返回的总数至少比当前加载的结果数量多
    totalCount = Math.max(
      totalCount,
      offset + response.data.results.length + 1
    );

    return {
      tracks: response.data.results.map((track: any) => ({
        id: track.id,
        name: track.name,
        duration: track.duration,
        artist_name: track.artist_name,
        audiodownload: track.audiodownload,
        image: track.image,
      })),
      total: totalCount,
    };
  } catch (error) {
    console.error("获取标签音乐失败:", error);
    return { tracks: [], total: 0 };
  }
};
