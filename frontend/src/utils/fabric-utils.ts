import { fabric } from "fabric";
import { EditorElement, EffecType } from "../types";

// 滤镜效果映射表，避免switch语句的重复查找
const EFFECT_FILTER_MAP = new Map<EffecType, string>([
  ["blackAndWhite", "grayscale(100%)"],
  ["sepia", "sepia(100%)"],
  ["invert", "invert(100%)"],
  ["saturate", "saturate(100%)"],
  ["retro", "retro(0%)"],
]);

// 获取滤镜效果的优化函数
function getFilterFromEffectType(effectType: EffecType): string {
  return EFFECT_FILTER_MAP.get(effectType) || "none";
}

// 圆角矩形路径创建的工具函数
function createRoundedRectPath(
  x: number,
  y: number,
  width: number,
  height: number,
  rx: number,
  ry: number = rx
): Path2D {
  const path = new Path2D();
  path.moveTo(x + rx, y);
  path.lineTo(x + width - rx, y);
  path.quadraticCurveTo(x + width, y, x + width, y + ry);
  path.lineTo(x + width, y + height - ry);
  path.quadraticCurveTo(x + width, y + height, x + width - rx, y + height);
  path.lineTo(x + rx, y + height);
  path.quadraticCurveTo(x, y + height, x, y + height - ry);
  path.lineTo(x, y + ry);
  path.quadraticCurveTo(x, y, x + rx, y);
  path.closePath();
  return path;
}

export const TextboxWithPadding = fabric.util.createClass(fabric.Textbox, {
  type: "text",
  paintFirst: "fill", // 改为 "fill" 以避免阴影渲染问题
  splitByGrapheme: true,
  includeDefaultValues: true,
  intersectsWithFrame: true,
  textBaseline: "alphabetic", // 修复 textBaseline 值，使用正确的 "alphabetic" 而不是 "alphabetical"

  _renderText: function (ctx) {
    // 只为文本设置阴影，不影响背景
    this._setShadow(ctx);
    this.callSuper("_renderText", ctx);
  },

  _render: function (ctx) {
    // 先绘制背景（不受阴影影响）
    this._renderBackground(ctx);

    // 然后绘制文本（带阴影效果）
    const originalBackgroundColor = this.backgroundColor;
    this.backgroundColor = "";
    this.callSuper("_render", ctx);
    this.backgroundColor = originalBackgroundColor;
  },

  toObject: function () {
    const baseObj = this.callSuper("toObject");
    return {
      ...baseObj,
      rx: this.rx,
      ry: this.ry,
      padding: this.padding,
    };
  },

  _getNonTransformedDimensions: function () {
    const padding = this.padding || 0;
    return {
      x: (this.width || 0) + padding * 2,
      y: (this.height || 0) + padding * 2,
    };
  },

  _renderBackground: function (ctx) {
    if (!this.backgroundColor && !this.strokeColor) return;

    const padding = this.padding || 0;
    const totalWidth = (this.width || 0) + padding * 2;
    const totalHeight = (this.height || 0) + padding * 2;
    const rx = this.rx || 10;
    const ry = this.ry || 10;

    // 保存当前上下文状态
    ctx.save();

    // 完全清除任何阴影设置，确保背景和边框不受阴影影响
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // 使用优化的圆角矩形路径
    const path = createRoundedRectPath(
      -totalWidth / 2,
      -totalHeight / 2,
      totalWidth,
      totalHeight,
      rx,
      ry
    );

    // 绘制背景填充
    if (this.backgroundColor) {
      ctx.fillStyle = this.backgroundColor;
      ctx.fill(path);
    }

    // 绘制边框（如果存在）
    if (this.strokeColor) {
      ctx.strokeStyle = this.strokeColor;
      ctx.lineWidth = this.strokeWidth || 1;
      ctx.stroke(path);
    }

    // 恢复上下文状态
    ctx.restore();
  },
});

// 创建基类
const CoverElementBase = fabric.util.createClass(fabric.Image, {
  type: "coverElement",
  customFilter: "none",
  disableCrop: false,
  cropWidth: 0,
  cropHeight: 0,

  brightness: 0,
  contrast: 0,
  saturation: 0,
  hue: 0,
  blur: 0,
  noise: 50,

  imageBorderColor: "transparent",
  borderWidth: 0,
  borderStyle: "solid",
  borderRadius: 0,
  imageFlipX: false,
  imageFlipY: false,

  // 缓存相关属性
  _cachedClipPath: null,
  _lastWidth: 0,
  _lastHeight: 0,
  _lastRadius: 0,
  _cachedOriginalSize: null,

  getOriginalSize() {
    if (this._cachedOriginalSize) {
      return this._cachedOriginalSize;
    }

    let size = { width: 100, height: 100 };

    if (this._element) {
      if (this._element instanceof HTMLImageElement) {
        size = {
          width: this._element.naturalWidth || this._element.width,
          height: this._element.naturalHeight || this._element.height,
        };
      } else if (this._element instanceof HTMLVideoElement) {
        size = {
          width: this._element.videoWidth || this._element.width,
          height: this._element.videoHeight || this._element.height,
        };
      }
    }

    this._cachedOriginalSize = size;
    return size;
  },

  getCrop(
    image: { width: number; height: number },
    size: { width: number; height: number }
  ) {
    if (
      this.cropX !== undefined &&
      this.cropY !== undefined &&
      this.cropWidth &&
      this.cropHeight
    ) {
      return {
        cropX: this.cropX,
        cropY: this.cropY,
        cropWidth: this.cropWidth,
        cropHeight: this.cropHeight,
      };
    }

    const aspectRatio = size.width / size.height;
    const imageRatio = image.width / image.height;

    const { newWidth, newHeight } =
      aspectRatio >= imageRatio
        ? { newWidth: image.width, newHeight: image.width / aspectRatio }
        : { newWidth: image.height * aspectRatio, newHeight: image.height };

    return {
      cropX: (image.width - newWidth) / 2,
      cropY: (image.height - newHeight) / 2,
      cropWidth: newWidth,
      cropHeight: newHeight,
    };
  },

  _createClipPath() {
    const { width, height } = this;
    const r = this.borderRadius;

    if (
      this._cachedClipPath &&
      this._lastWidth === width &&
      this._lastHeight === height &&
      this._lastRadius === r
    ) {
      return this._cachedClipPath;
    }

    const path = new Path2D();
    const halfWidth = width / 2;
    const halfHeight = height / 2;

    path.moveTo(-halfWidth + r, -halfHeight);
    path.arcTo(halfWidth, -halfHeight, halfWidth, halfHeight, r);
    path.arcTo(halfWidth, halfHeight, -halfWidth, halfHeight, r);
    path.arcTo(-halfWidth, halfHeight, -halfWidth, -halfHeight, r);
    path.arcTo(-halfWidth, -halfHeight, halfWidth, -halfHeight, r);
    path.closePath();

    this._cachedClipPath = path;
    this._lastWidth = width;
    this._lastHeight = height;
    this._lastRadius = r;

    return path;
  },

  getCompositeFilter() {
    const filters = [
      getFilterFromEffectType(this.customFilter),
      `brightness(${100 + (this.brightness || 0)}%)`,
      `contrast(${100 + (this.contrast || 0)}%)`,
      `saturate(${100 + (this.saturation || 0)}%)`,
      `hue-rotate(${this.hue || 0}deg)`,
      `blur(${this.blur || 0}px)`,
    ];

    return (
      filters.filter((f) => f !== "none" && !f.includes("NaN")).join(" ") ||
      "none"
    );
  },

  applyImageFilters() {
    this.canvas?.requestRenderAll();
  },

  _setFilterProperty(property: string, value: number) {
    this[property] = value;
    this.applyImageFilters();
  },

  setBrightness(value: number) {
    this._setFilterProperty("brightness", value);
  },
  setContrast(value: number) {
    this._setFilterProperty("contrast", value);
  },
  setSaturation(value: number) {
    this._setFilterProperty("saturation", value);
  },
  setHue(value: number) {
    this._setFilterProperty("hue", value);
  },
  setBlur(value: number) {
    this._setFilterProperty("blur", value);
  },
  setNoise(value: number) {
    this._setFilterProperty("noise", value);
  },

  setBorder(color: string, width: number, style: string = "solid") {
    if (!color) {
      console.error("Invalid border color");
      return;
    }
    this.imageBorderColor = color;
    this.borderWidth = width;
    this.borderStyle = style;
    this.canvas?.requestRenderAll();
  },

  _renderWithCrop(ctx: CanvasRenderingContext2D) {
    if (this.disableCrop) {
      return this.callSuper("_render", ctx);
    }

    const { width, height } = this;
    const originalSize = this.getOriginalSize();
    const { cropX, cropY, cropWidth, cropHeight } = this.getCrop(originalSize, {
      width: this.getScaledWidth(),
      height: this.getScaledHeight(),
    });

    ctx.save();

    if (this.imageFlipX) ctx.scale(-1, 1);
    if (this.imageFlipY) ctx.scale(1, -1);

    const clipPath = this._createClipPath();
    ctx.clip(clipPath);

    const filter = this.getCompositeFilter();
    ctx.filter = filter;

    ctx.drawImage(
      this._element,
      Math.max(cropX, 0),
      Math.max(cropY, 0),
      Math.max(1, cropWidth),
      Math.max(1, cropHeight),
      -width / 2,
      -height / 2,
      width,
      height
    );
    ctx.filter = "none";

    this._renderBorder(ctx, clipPath);

    ctx.restore();
  },

  _renderBorder(ctx: CanvasRenderingContext2D, path: Path2D) {
    if (this.borderWidth <= 0) return;

    ctx.strokeStyle = this.imageBorderColor;
    ctx.lineWidth = this.borderWidth;

    const dashArray =
      this.borderStyle === "dashed"
        ? [this.borderWidth * 2, this.borderWidth]
        : this.borderStyle === "dotted"
        ? [this.borderWidth, this.borderWidth]
        : [];

    ctx.setLineDash(dashArray);
    ctx.stroke(path);
    ctx.setLineDash([]);
  },

  dispose() {
    this._cachedClipPath = null;
    this._cachedOriginalSize = null;
    this._lastWidth = 0;
    this._lastHeight = 0;
    this._lastRadius = 0;
    this.callSuper("dispose");
  },
});

// CoverImage 类
export const CoverImage = fabric.util.createClass(CoverElementBase, {
  type: "coverImage",

  initialize(element: HTMLImageElement, options: any = {}) {
    const defaultOptions = {
      cropHeight: this.height,
      cropWidth: this.width,
      ...options,
    };

    // this.imageFlipX = defaultOptions.flipX;
    // this.imageFlipY = defaultOptions.flipY;
    this.callSuper("initialize", element, defaultOptions);
    this.applyFilters();
  },

  _render(ctx: CanvasRenderingContext2D) {
    this._renderWithCrop(ctx);
  },
});

// CoverGif 类 - 专门处理GIF动画
export const CoverGif = fabric.util.createClass(CoverElementBase, {
  type: "coverGif",

  initialize(element: HTMLImageElement, options: any = {}) {
    const defaultOptions = {
      cropHeight: this.height,
      cropWidth: this.width,
      ...options,
    };

    // this.imageFlipX = defaultOptions.flipX;
    // this.imageFlipY = defaultOptions.flipY;

    // GIF动画相关属性
    this.isAnimated = options.isAnimated || false;
    this.frameData = options.frameData || null;
    this.currentFrame = 0;
    this.lastFrameTime = 0;
    this.isPlaying = true;

    this.callSuper("initialize", element, defaultOptions);
    this.applyFilters();

    // 如果有帧数据，启动动画
    if (this.frameData) {
      this._startAnimation();
    }
  },

  // 重写getOriginalSize以正确处理GIF帧数据
  getOriginalSize() {
    if (this._cachedOriginalSize) {
      return this._cachedOriginalSize;
    }

    let size = { width: 100, height: 100 }; // 默认值

    if (this.frameData && this.frameData.frameWidth) {
      // 对于GIF，使用单个帧的尺寸
      size = {
        width: this.frameData.frameWidth,
        height: this._element ? this._element.naturalHeight : 100,
      };
    } else if (this._element) {
      // 回退到基类实现
      if (this._element instanceof HTMLImageElement) {
        size = {
          width: this._element.naturalWidth || this._element.width,
          height: this._element.naturalHeight || this._element.height,
        };
      }
    }

    this._cachedOriginalSize = size;
    return size;
  },

  _startAnimation() {
    if (!this.frameData || !this.isAnimated) return;

    const animate = () => {
      if (!this.isPlaying || !this.canvas) return;

      const now = Date.now();
      const frameDelay = this.frameData.delay || 100; // 默认100ms延迟

      if (now - this.lastFrameTime >= frameDelay) {
        this.currentFrame =
          (this.currentFrame + 1) % this.frameData.framesLength;
        this.lastFrameTime = now;
        this.dirty = true;
        this.canvas.requestRenderAll();
      }

      requestAnimationFrame(animate);
    };

    requestAnimationFrame(animate);
  },

  _render(ctx: CanvasRenderingContext2D) {
    // 使用统一的crop渲染逻辑
    this._renderWithCrop(ctx);
  },

  // 重写_renderWithCrop以支持GIF帧
  _renderWithCrop(ctx: CanvasRenderingContext2D) {
    if (this.disableCrop) {
      return this._renderGifFrameNoCrop(ctx);
    }

    const { width, height } = this;
    const originalSize = this.getOriginalSize();
    const { cropX, cropY, cropWidth, cropHeight } = this.getCrop(originalSize, {
      width: this.getScaledWidth(),
      height: this.getScaledHeight(),
    });

    ctx.save();

    if (this.imageFlipX) ctx.scale(-1, 1);
    if (this.imageFlipY) ctx.scale(1, -1);

    const clipPath = this._createClipPath();
    ctx.clip(clipPath);

    const filter = this.getCompositeFilter();
    ctx.filter = filter;

    // GIF特有的帧绘制逻辑
    if (this.frameData && this.isAnimated) {
      const frameWidth = this.frameData.frameWidth;

      // 关键修复：正确计算源坐标
      const sourceX = frameWidth * this.currentFrame + Math.max(cropX, 0); // 当前帧的起始X + 裁剪偏移
      const sourceY = Math.max(cropY, 0); // 裁剪Y偏移
      const sourceWidth = Math.min(cropWidth, frameWidth - Math.max(cropX, 0)); // 确保不超出帧边界
      const sourceHeight = Math.min(
        cropHeight,
        this._element.naturalHeight - Math.max(cropY, 0)
      ); // 确保不超出图片边界

      ctx.drawImage(
        this._element,
        sourceX, // 源X：当前帧位置 + crop偏移
        sourceY, // 源Y：crop偏移
        Math.max(1, sourceWidth), // 源宽度：裁剪后的宽度
        Math.max(1, sourceHeight), // 源高度：裁剪后的高度
        -width / 2, // 目标X
        -height / 2, // 目标Y
        width, // 目标宽度
        height // 目标高度
      );
    } else {
      // 静态图片crop逻辑
      ctx.drawImage(
        this._element,
        Math.max(cropX, 0),
        Math.max(cropY, 0),
        Math.max(1, cropWidth),
        Math.max(1, cropHeight),
        -width / 2,
        -height / 2,
        width,
        height
      );
    }

    ctx.filter = "none";

    this._renderBorder(ctx, clipPath);

    ctx.restore();
  },

  // 无crop时的GIF帧渲染
  _renderGifFrameNoCrop(ctx: CanvasRenderingContext2D) {
    ctx.save();

    if (this.imageFlipX) ctx.scale(-1, 1);
    if (this.imageFlipY) ctx.scale(1, -1);

    const clipPath = this._createClipPath();
    if (clipPath) {
      ctx.clip(clipPath);
    }

    const filter = this.getCompositeFilter();
    ctx.filter = filter;

    if (this.frameData && this.isAnimated) {
      const frameWidth = this.frameData.frameWidth;
      // 绘制当前帧
      ctx.drawImage(
        this._element,
        frameWidth * this.currentFrame,
        0,
        frameWidth,
        this._element.height,
        -this.width / 2,
        -this.height / 2,
        this.width,
        this.height
      );
    } else {
      // 绘制静态图片
      ctx.drawImage(
        this._element,
        -this.width / 2,
        -this.height / 2,
        this.width,
        this.height
      );
    }

    ctx.filter = "none";

    if (this._renderBorder && clipPath) {
      this._renderBorder(ctx, clipPath);
    }

    ctx.restore();
  },

  play() {
    this.isPlaying = true;
    if (this.frameData && !this._animationStarted) {
      this._startAnimation();
      this._animationStarted = true;
    }
  },

  pause() {
    this.isPlaying = false;
  },

  stop() {
    this.isPlaying = false;
    this.currentFrame = 0;
    this.dirty = true;
    if (this.canvas) {
      this.canvas.requestRenderAll();
    }
  },
});

// CoverVideo 类
export const CoverVideo = fabric.util.createClass(CoverElementBase, {
  type: "coverVideo",

  initialize(element: HTMLVideoElement, options: any = {}) {
    const defaultOptions = {
      cropHeight: this.height,
      cropWidth: this.width,
      ...options,
    };

    this.callSuper("initialize", element, defaultOptions);
  },

  _render(ctx: CanvasRenderingContext2D) {
    this._renderWithCrop(ctx);
  },
});

// 优化的工具类
export class FabricUitls {
  static getClipMaskRect(
    editorElement: EditorElement,
    extraOffset: number
  ): fabric.Rect {
    const { placement } = editorElement;
    const extraOffsetX = extraOffset / placement.scaleX;
    const extraOffsetY = extraOffset / placement.scaleY;

    return new fabric.Rect({
      left: placement.x - extraOffsetX,
      top: placement.y - extraOffsetY,
      width: placement.width + extraOffsetX * 2,
      height: placement.height + extraOffsetY * 2,
      scaleX: placement.scaleX,
      scaleY: placement.scaleY,
      absolutePositioned: true,
      fill: "transparent",
      stroke: "transparent",
      opacity: 0.5,
      strokeWidth: 0,
    });
  }
}

// 类型声明（更新以包含新属性）
declare module "fabric" {
  namespace fabric {
    class CoverVideo extends Image {
      type: "coverVideo";
      disableCrop: boolean;
      cropX: number;
      cropY: number;
      cropWidth: number;
      cropHeight: number;
      brightness: number;
      contrast: number;
      saturation: number;
      hue: number;
      blur: number;
      noise: number;
      borderColor: string;
      borderWidth: number;
      borderStyle: string;
    }

    class CoverImage extends Image {
      type: "coverImage";
      disableCrop: boolean;
      cropX: number;
      cropY: number;
      cropWidth: number;
      cropHeight: number;
      brightness: number;
      contrast: number;
      saturation: number;
      hue: number;
      blur: number;
      noise: number;
      borderColor: string;
      borderWidth: number;
      borderStyle: string;
    }

    class CoverGif extends Image {
      type: "coverGif";
      disableCrop: boolean;
      cropX: number;
      cropY: number;
      cropWidth: number;
      cropHeight: number;
      brightness: number;
      contrast: number;
      saturation: number;
      hue: number;
      blur: number;
      noise: number;
      borderColor: string;
      borderWidth: number;
      borderStyle: string;
      isAnimated: boolean;
      frameData: any;
      currentFrame: number;
      lastFrameTime: number;
      isPlaying: boolean;
      play(): void;
      pause(): void;
      stop(): void;
    }

    class TextboxWithPadding extends Textbox {
      type: "text";
      padding: number;
      rx: number;
      ry: number;
    }
  }
}

// 注册类到 fabric 命名空间
fabric.CoverImage = CoverImage;
fabric.CoverGif = CoverGif;
fabric.CoverVideo = CoverVideo;
fabric.TextboxWithPadding = TextboxWithPadding;

// 导出性能监控工具
export {};
