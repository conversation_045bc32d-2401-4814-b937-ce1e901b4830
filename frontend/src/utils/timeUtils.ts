/**
 * Formats milliseconds into a string representation of time
 * @param ms - The number of milliseconds to format
 * @returns A string in the format:
 * - "Xs" for seconds (e.g., "5s")
 * - "Xm" for whole minutes (e.g., "1m")
 * - "X:YY" for non-whole minutes (e.g., "1:30")
 * - "Xh" for whole hours (e.g., "1h")
 * - "Xh:YY" for non-whole hours (e.g., "1h:20")
 */
export const formatTime = (ms: number): string => {
  const totalSeconds = Math.floor(ms / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  // 小于60秒，显示为"Xs"格式
  if (hours === 0 && minutes === 0) {
    return `${seconds}s`;
  }

  // 大于60秒，小于60分钟
  if (hours === 0) {
    // 整数分钟，显示为"Xm"格式
    if (seconds === 0) {
      return `${minutes}m`;
    }
    // 非整数分钟，显示为"X:YY"格式
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }

  // 大于60分钟
  // 整数小时，显示为"Xh"格式
  if (minutes === 0 && seconds === 0) {
    return `${hours}h`;
  }
  // 非整数小时，显示为"Xh:YY"格式
  return `${hours}h:${minutes.toString().padStart(2, "0")}`;
};

/**
 * Converts a time string in the format "HH:MM:SS" to milliseconds
 * @param timeString - A string in the format "HH:MM:SS"
 * @returns The number of milliseconds
 */
export const timeStringToMs = (timeString: string): number => {
  const [hours, minutes, seconds] = timeString.split(":").map(Number);
  return (hours * 3600 + minutes * 60 + seconds) * 1000;
};

/**
 * Formats milliseconds into a standard caption time format "HH:MM:SS"
 * This function is specifically for caption time formatting and ensures consistent format
 * @param ms - The number of milliseconds to format
 * @returns A string in the format "HH:MM:SS"
 */
export const formatCaptionTime = (ms: number): string => {
  // 确保输入是有效的数字
  const validMs = Math.max(0, Math.floor(ms));

  const totalSeconds = Math.floor(validMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  // 始终返回 HH:MM:SS 格式，确保两位数字
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
};

/**
 * Ensures that the start time is always less than or equal to the end time
 * @param start - The start time in milliseconds
 * @param end - The end time in milliseconds
 * @returns An object with normalized start and end times
 */
export const normalizeTimeFrame = (start: number, end: number) => {
  return start <= end ? { start, end } : { start: end, end: start };
};

// 时间线常量
export const TIMELINE_CONSTANTS = {
  HANDLE_WIDTH: 10, // 排序把手宽度
};

/**
 * 统一获取时间线容器宽度的函数
 * 所有组件都应使用此函数以确保一致的容器宽度计算
 * @returns 容器宽度（像素）或null
 */
export const getTimelineContainerWidth = () => {
  const containerRef = document.querySelector(".timeline-container");
  if (!containerRef) return null;

  // 统一减去排序把手宽度
  return containerRef.clientWidth - TIMELINE_CONSTANTS.HANDLE_WIDTH;
};

/**
 * 计算时间在时间线上的百分比位置，考虑平移偏移
 * 所有时间线组件（刻度、元素、指示器等）都应使用此函数以确保精确对齐
 * @param time 时间点（毫秒）
 * @param timelineDisplayDuration 时间线显示的总时长（毫秒）
 * @param offsetX 时间线平移偏移量（毫秒）
 * @param containerWidth 容器宽度（像素），用于精确计算绝对位置
 * @returns 百分比位置值（0-100）
 */
export const calculateTimelinePosition = (
  time,
  timelineDisplayDuration,
  offsetX,
  containerWidth = null
) => {
  // 确保参数为数字类型
  const timeValue = Number(time);
  const durationValue = Number(timelineDisplayDuration);
  const offsetValue = Math.max(0, Number(offsetX));

  // 基础百分比计算
  const percentage =
    (timeValue / durationValue) * 100 - (offsetValue / durationValue) * 100;

  // 如果提供了容器宽度，则进行精确像素计算以减少浮点累积误差
  if (containerWidth && containerWidth > 0) {
    // 计算精确的像素位置 - 使用Math.round确保像素对齐
    const pixelPosition = Math.round((percentage / 100) * containerWidth);
    // 再转回百分比，确保精确到像素
    return (pixelPosition / containerWidth) * 100;
  }

  // 如果没有提供容器宽度，至少保证小数精度一致以减少误差
  // 提高精度，从4位小数改为6位，以减少长时间线上的累积误差
  return Number(percentage.toFixed(6));
};
