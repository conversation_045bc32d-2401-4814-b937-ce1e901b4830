import {
  AudioEditorElement,
  EditorElement,
  ImageEditorElement,
  VideoEditorElement,
  GifEditorElement,
} from "../types";
export function getUid() {
  return Math.random().toString(36).substring(2, 9);
}

export function isHtmlVideoElement(
  element:
    | HTMLVideoElement
    | HTMLImageElement
    | HTMLCanvasElement
    | null
    | HTMLElement
): element is HTMLVideoElement {
  if (!element) return false;
  return element.tagName === "VIDEO";
}
export function isHtmlImageElement(
  element:
    | HTMLVideoElement
    | HTMLImageElement
    | HTMLCanvasElement
    | null
    | HTMLElement
): element is HTMLImageElement {
  if (!element) return false;
  return element.tagName === "IMG";
}

export function isHtmlAudioElement(
  element:
    | HTMLVideoElement
    | HTMLImageElement
    | HTMLCanvasElement
    | null
    | HTMLElement
): element is HTMLAudioElement {
  if (!element) return false;
  return element.tagName === "AUDIO";
}

export function formatTimeToMinSec(time: number) {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${appendZero(seconds, 2)}`;
}

export function formatTimeToMinSecMili(time: number) {
  const mili = Math.floor((time % 1000) / 10);
  return formatTimeToMinSec(time / 1000) + `.${appendZero(mili, 2)}`;
}

function appendZero(value: number, minDigits: number = 2) {
  return value.toString().padStart(minDigits, "0");
}
export function isEditorAudioElement(
  element: EditorElement
): element is AudioEditorElement {
  return element.type === "audio";
}
export function isEditorVideoElement(
  element: EditorElement
): element is VideoEditorElement {
  return element.type === "video";
}

export function isEditorImageElement(
  element: EditorElement
): element is ImageEditorElement {
  return element.type === "image";
}

export function isEditorGifElement(
  element: EditorElement
): element is GifEditorElement {
  return element.type === "gif";
}

export function isEditorMediaElement(
  element: EditorElement
): element is ImageEditorElement | VideoEditorElement | GifEditorElement {
  return (
    element.type === "image" ||
    element.type === "video" ||
    element.type === "gif"
  );
}
