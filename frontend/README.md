The app's live link to test in the browser is: https://fabric-video-editor.vercel.app/

Do you need a custom editor? Get in touch with me at [Linked In](https://www.linkedin.com/in/amit-digga/)
Other: [Website](https://www.amitdigga.dev/) | [Twitter](https://twitter.com/AmitDigga) |

This was a hobby project. I will add support for other features in the future. Looking for backend/ffmpeg developers to help me generate video from Canvas in the backend.

# Fabric Video Editor

Fabric Video Editor is a video editor that runs in the browser. It is built with fabric.js, React, Tailwindcss, Mobx, typescript, Material-UI, and anime.js.

## Live Demo

🚀 **Try the live demo**: [https://fabric-video-editor.vercel.app/](https://fabric-video-editor.vercel.app/)

## Tech Stack

This project is built with modern web technologies:

- **React 19.1.0** - UI framework with latest features
- **TypeScript 5.8.3** - Type-safe JavaScript development
- **Fabric.js 5.3.1** - Powerful canvas manipulation library
- **MobX 6.13.7** - Reactive state management
- **Material-UI 7.2.0** - Modern React component library
- **Anime.js 3.2.2** - Smooth animations and transitions
- **Wavesurfer.js 7.10.0** - Audio waveform visualization

## Features

- [x] **Media Support**
  - [x] Text with custom fonts and styling
  - [x] Images (PNG, JPG, GIF)
  - [x] Video files with playback controls
  - [x] Audio tracks with waveform visualization
  - [x] Shapes and graphics
- [x] **Canvas Editing**
  - [x] Background color and gradient support
  - [x] Element positioning, scaling, and rotation
  - [x] Layer management and ordering
  - [x] Alignment guides and snapping
- [x] **Timeline & Animation**
  - [x] Multi-track timeline interface
  - [x] Element duration control
  - [x] Transition animations between elements
  - [x] Custom animation effects
- [x] **Advanced Features**
  - [x] Video export with audio synchronization
  - [x] Visual filters and effects
  - [x] Undo/Redo functionality
  - [x] Project save/load capabilities
  - [x] Caption and subtitle support

## 🎉 v1.0.0 Major Release

Fabric Video Editor v1.0.0 is officially released! This milestone version includes complete video editing functionality and comprehensive performance optimization:

### 🚀 Core Features

- **Complete Video Editing**: Support for video, image, audio, text, and shape element editing
- **Multi-track Timeline**: Professional-grade multi-track timeline editing interface
- **Animation System**: Support for various entrance and exit animation effects
- **Caption Features**: Manual addition and AI-powered automatic caption generation
- **Real-time Preview**: Smooth real-time editing preview experience

### 🔧 Technical Architecture

- **Frontend**: React 19.1.0 + TypeScript 5.8.3 + MobX 6.13.7 + Fabric.js 5.3.1
- **Backend**: Node.js + Express 5.1.0 + FFmpeg integration
- **UI Framework**: Material-UI 7.2.0 + Anime.js 3.2.2
- **Media Processing**: Wavesurfer.js 7.10.0 audio waveform visualization

### Current Optimization Work

- **Frontend Performance**: Timeline rendering optimization, UI responsiveness improvements, memory usage optimization
- **Backend Processing**: FFmpeg command optimization, hardware acceleration utilization, resource management improvements
- **Code Architecture**: Store decomposition, component optimization, enhanced error handling
- **Testing Infrastructure**: Performance benchmarks, load testing, integration testing

### Latest Progress

- ✅ **Store Architecture**: Completed monolithic Store decomposition design
- ✅ **Performance Monitoring**: Implemented frontend/backend metrics collection
- ✅ **Documentation**: Enhanced technical documentation and comprehensive optimization guides
- 🔄 **Timeline Optimization**: Implementing virtualized scrolling and caching
- 🔄 **Media Processing**: Optimizing thumbnail generation and audio waveform rendering
- 📋 **Hardware Acceleration**: Planning FFmpeg hardware acceleration detection

### Performance Targets

- **Timeline rendering**: < 2 seconds for 50+ elements
- **UI responsiveness**: < 100ms for user interactions
- **Memory usage**: < 500MB for typical projects
- **Video processing**: 30% reduction in processing time
- **Bundle size**: < 2MB initial load
- **First contentful paint**: < 3 seconds

### Optimization Implementation Plan

The project adopts a phased optimization strategy:

1. **Phase 1 (1-2 weeks)**: Store decomposition, component optimization, basic monitoring
2. **Phase 2 (2-3 weeks)**: Timeline virtualization, media processing optimization, backend optimization
3. **Phase 3 (2-3 weeks)**: Caching system, concurrent processing, performance testing
4. **Phase 4 (Ongoing)**: Monitoring system, continuous optimization

### Latest Progress

- ✅ **Store Architecture**: Completed monolithic Store decomposition design
- ✅ **Performance Monitoring**: Implemented frontend/backend metrics collection
- ✅ **Documentation**: Enhanced technical documentation and comprehensive optimization guides
- 🔄 **Timeline Optimization**: Implementing virtualized scrolling and caching
- 🔄 **Media Processing**: Optimizing thumbnail generation and audio waveform rendering
- 📋 **Hardware Acceleration**: Planning FFmpeg hardware acceleration detection

For detailed information, see the [Performance Optimization Guide](../docs/PERFORMANCE_OPTIMIZATION.md).

## Getting Started

1. Clone the repo

2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Environment Setup

Create a `.env` file in the frontend directory with the following variables:

```env
REACT_APP_API_URL=http://localhost:3001
REACT_APP_JAMENDO_API_KEY=your_jamendo_api_key_here
```

### Jamendo API Integration

To enable the music library feature:

1. Register at [Jamendo Developer Portal](https://devportal.jamendo.com/)
2. Create an application and get your API key
3. Add the API key to your `.env` file
4. Restart the development server

## Project Structure

```
frontend/
├── public/                # Static assets and animations
├── src/
│   ├── components/        # Reusable UI components
│   ├── editor/           # Main editor components
│   │   ├── timeline/     # Timeline functionality
│   │   ├── control-item/ # Property controls
│   │   └── menu-item/    # Media library items
│   ├── store/            # MobX state management
│   ├── services/         # API integrations
│   └── utils/            # Helper functions
└── package.json
```

[The rest of the README remains unchanged]
