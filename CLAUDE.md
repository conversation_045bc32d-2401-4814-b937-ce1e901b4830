# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands for Development

### Frontend Development

```bash
# Install frontend dependencies
cd frontend && npm install

# Start frontend development server (default port: 3000)
cd frontend && npm start

# Build frontend for production
cd frontend && npm run build

# Run frontend tests
cd frontend && npm test

# Lint frontend code
cd frontend && npm run lint
```

### Backend Development

```bash
# Install backend dependencies
cd server && npm install

# Start backend development server with hot reload
cd server && npm run dev

# Build backend for production
cd server && npm run build

# Start backend production server
cd server && npm start

# Setup system fonts for text rendering (required once)
cd server && npm run setup-fonts

# Run backend tests
cd server && npm test

# Lint backend code
cd server && npm run lint

# Initialize sample templates
cd server && npm run init-templates
```

### Full Stack Development

To develop the full application:

```bash
# Terminal 1 - Backend
cd server && npm run dev

# Terminal 2 - Frontend
cd frontend && npm start
```

## Code Architecture

### Project Overview

Fabric Video Editor is a web-based video editing application that provides a canvas-based editing experience. Users can create, edit, and export video compositions by combining various media elements including videos, images, audio, text, and shapes.

### Core Architecture

The application follows a client-server architecture:

1. **Frontend**: React application with MobX state management and Fabric.js for canvas operations
2. **Backend**: Node.js server with Express.js and FFmpeg integration for video processing

### Key Technologies

- **Frontend**: React, TypeScript, MobX, Fabric.js, Anime.js, Material-UI
- **Backend**: Node.js, TypeScript, Express.js, FFmpeg, ImageMagick, AWS S3

### Frontend Structure

The frontend follows a well-organized structure:

1. **Store Layer**: MobX stores manage application state
   - `Store.ts`: Central state container
   - `ElementManager.ts`: Handles canvas elements (videos, images, text, etc.)
   - `AnimationManager.ts`: Manages animations and effects
   - `CanvasManager.ts`: Controls canvas rendering and interaction
   - `TimelineManager.ts`: Manages timeline and playback
   - `TrackManager.ts`: Manages track organization
   - `CaptionManager.ts`: Handles subtitles and captions
   - `HistoryManager.ts`: Handles undo/redo functionality
   
2. **Editor Layer**: Components for the video editor interface
   - `components/`: Reusable UI components
   - `editor/`: Editor-specific components
   - `editor/guide-lines/`: Alignment and centering guidelines
   - `editor/control-item/`: Property panels for different element types
   - `editor/menu-item/`: Left sidebar menu items
   - `editor/timeline/`: Timeline components and drag functionality

3. **Utils Layer**: Helper functions and utilities
   - `fabric-utils.ts`: Fabric.js helper functions
   - `ShapeFactory.ts`: Shape creation utilities
   - `timeUtils.ts`: Time calculation helpers

### Backend Structure

The backend is structured around:

1. **FFmpeg Command Generation**: Creates and executes FFmpeg commands for video processing
   - `ffmpeg/FFmpegCommandGenerator.ts`: Main command builder
   - `ffmpeg/filters/`: Filter generators for different media types
   - `ffmpeg/core/`: Core processing modules for different media types

2. **API Endpoints**: Handles requests from the frontend
   - `/api/generateVideo`: Creates videos from canvas state
   - `/api/progress/:taskId`: Provides progress updates
   - `/api/s3`: Handles media file uploads and storage
   - `/api/ai/chat`: AI assistant interactions using AWS Bedrock

3. **Services**: Business logic for video processing
   - `ProgressTracker.ts`: Tracks video generation progress
   - `S3Service.ts`: Manages file uploads to AWS S3
   - `BedrockService.ts`: Handles AWS Bedrock AI interactions
   - `ThumbnailService.ts`: Generates thumbnails for media files
   - `QuotaManagementService.ts`: Manages user quotas for resources

## Key Concepts

### 1. Canvas Elements

Canvas elements are the core building blocks:
- **Videos**: Video clips with timing controls
- **Images**: Static images or animated GIFs
- **Audio**: Sound tracks that play in sync with video
- **Text**: Text elements with styling options
- **Shapes**: Vector shapes with styling options
- **Captions**: Subtitle elements with timing controls

Elements are managed by `ElementManager.ts` which handles:
- Creating elements of different types
- Setting properties and placement
- Handling user interactions (move, scale, rotate)
- Supporting element alignment with guide lines

### 2. Timeline and Animation System

The timeline system:
- Manages playback and seeking
- Controls element visibility based on time
- Supports animation keyframes
- Handles track management for organizing elements

The animation system supports:
- Entry/exit animations (fade, slide, zoom)
- Custom animation durations
- Animation synchronization between frontend and backend
- Multi-track timeline organization

### 3. Export Process

When exporting a video:
1. Canvas state is sent to the backend
2. `FFmpegCommandGenerator` creates a command based on elements
3. FFmpeg processes the command to generate video
4. Progress updates are sent to the frontend
5. Final video is made available for download

### 4. AI Assistant

The application includes an AI assistant feature:
- Built on AWS Bedrock Claude Sonnet
- Provides editing suggestions based on current project
- Answers user questions about video editing
- Analyzes canvas state to provide contextual help

## External Service Integration

### AWS S3 Integration

The application uses AWS S3 for media storage:
- Media files are uploaded directly to S3 from the frontend
- Backend generates signed URLs for secure uploads
- Proper CORS configuration is required for browser uploads
- Media playback and thumbnails are served from S3

Required environment variables:
```
S3_BUCKET_NAME=your_bucket_name
S3_REGION=your_region
S3_ACCESS_KEY_ID=your_access_key_id
S3_SECRET_ACCESS_KEY=your_secret_key
```

### AWS Bedrock Integration

AI assistant functionality uses AWS Bedrock:
```
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
```

### Jamendo API Integration

The application integrates with Jamendo API for online audio library:
```
REACT_APP_JAMENDO_API_KEY=your_api_key
```

## Common Workflows

### Adding New Elements

1. User selects element type from the sidebar
2. Element is created and added to the canvas
3. Element is added to the appropriate track in the timeline
4. Properties panel updates to show element options

### Editing Elements

1. User selects an element on the canvas
2. Property panel shows options specific to that element type
3. Changes update both the canvas display and the element data

### Timeline Operations

1. Elements can be dragged to adjust timing
2. Duration can be modified by dragging element edges
3. Playhead position determines current frame view
4. Split operations can divide elements at the playhead
5. Multi-track organization allows for complex compositions

### Animation and Effects

1. Entry/exit animations can be applied to elements
2. Animation duration can be customized
3. Multiple animations can be sequenced
4. Animation previews are synchronized with final export

### Export and Rendering

1. Video resolution and quality settings can be configured
2. Progress tracking provides real-time export status
3. FFmpeg processes the composition with accurate timing
4. Final video maintains all animations, effects, and timing