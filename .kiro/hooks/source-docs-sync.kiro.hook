{"enabled": true, "name": "Source Code Documentation Sync", "description": "Monitors changes to source code files and prompts updates to relevant documentation in README or docs folder", "version": "1", "when": {"type": "fileEdited", "patterns": ["frontend/src/**/*.ts", "frontend/src/**/*.tsx", "server/src/**/*.ts", "*.md", "docs/**/*.md"]}, "then": {"type": "askAgent", "prompt": "I noticed changes to source code files. Please review these changes and update the relevant documentation in either the README.md file or the appropriate file in the docs/ directory. Consider:\n\n1. If the changes introduce new features, ensure they're documented\n2. If the changes modify existing functionality, update relevant documentation\n3. If the changes fix bugs or improve performance, consider noting this in changelogs\n4. Check if API documentation needs updating\n5. Ensure code examples in documentation remain accurate\n\nPlease suggest specific documentation updates based on the code changes."}}