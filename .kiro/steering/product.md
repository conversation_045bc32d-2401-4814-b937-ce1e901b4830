# Fabric Video Editor

Fabric Video Editor is a powerful web-based video editing application that leverages Fabric.js to provide a flexible and intuitive canvas-based editing experience. The project combines a React frontend with a Node.js backend to deliver a comprehensive video editing toolkit including timeline operations, element management, and animation controls.

## Core Features

- **Canvas-based editing** with intuitive controls
- **Timeline** for managing element duration and animations
- **Support for various media types** (video, image, audio, text)
- **Customizable animations and effects**
- **FFmpeg integration** for video processing and export
- **Multi-track timeline** for complex compositions
- **Text and caption support** with styling options
- **Shape and graphic elements**
- **Transition effects** between elements

## User Workflow

1. Users add media elements (videos, images, audio) to their composition using the left sidebar
2. Elements can be manipulated on the canvas (position, scale, rotation)
3. The timeline at the bottom allows adjusting element duration and adding animations
4. Effects and styles can be applied using the right sidebar controls
5. The composition can be exported using options in the top menu

## Media Sources

- Local media library
- Jamendo free music library (integrated API)
  - Search functionality
  - Browse popular tracks
  - CORS issues handled through backend proxy

## Data Flow

1. User interacts with UI components in the React frontend
2. Actions trigger state updates in MobX stores
3. Element and animation logic is handled by respective managers
4. Canvas updates are managed through Fabric.js
5. For video export, canvas state is sent to the backend
6. Backend generates FFmpeg commands
7. FFmpeg processes the video
8. Processed video is sent back to frontend for download

response in chinese
