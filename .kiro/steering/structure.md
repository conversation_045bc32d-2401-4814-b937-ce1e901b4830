# Project Structure

## Repository Organization

The project is organized into three main directories:

1. `frontend/`: Contains the React application
2. `server/`: Contains the Node.js backend
3. `docs/`: Contains project documentation

## Frontend Structure

```
frontend/
├── public/                # Static assets
│   ├── assets/            # Media assets
│   │   ├── animations/    # Animation GIFs
│   │   ├── icons/         # Icon images
│   │   └── timeline/      # Timeline assets
│   └── index.html         # HTML entry point
├── src/                   # Source code
│   ├── components/        # Reusable UI components
│   │   ├── common/        # Shared components
│   │   ├── media/         # Media-related components
│   │   └── demo/          # Demo components
│   ├── editor/            # Editor-specific components
│   │   ├── components/    # Editor UI components
│   │   ├── control-item/  # Control panel items
│   │   ├── entity/        # Entity components
│   │   ├── guide-lines/   # Alignment guides
│   │   ├── menu-item/     # Menu items
│   │   └── timeline/      # Timeline components
│   ├── store/             # MobX state management
│   │   ├── AnimationManager.ts
│   │   ├── CanvasManager.ts
│   │   ├── ElementManager.ts
│   │   ├── HistoryManager.ts
│   │   ├── Store.ts
│   │   └── TrackManager.ts
│   ├── services/          # API services
│   ├── utils/             # Utility functions
│   ├── hooks/             # Custom React hooks
│   ├── i18n/              # Internationalization
│   ├── theme/             # Theming
│   ├── interfaces/        # TypeScript interfaces
│   ├── App.tsx            # Main App component
│   └── index.tsx          # Entry point
└── package.json           # Dependencies and scripts
```

## Backend Structure

```
server/
├── data/                  # Static data files
│   ├── media.json         # Media library data
│   ├── templates/         # Template data
│   └── user-quotas.json   # User quota data
├── docs/                  # Backend documentation
├── logs/                  # Log files
├── scripts/               # Utility scripts
├── src/                   # Source code
│   ├── config/            # Configuration
│   ├── controllers/       # API controllers
│   ├── ffmpeg/            # FFmpeg integration
│   │   ├── core/          # Core FFmpeg functionality
│   │   │   ├── filters/   # FFmpeg filters
│   │   │   └── generators/# Media generators
│   │   └── utils/         # FFmpeg utilities
│   ├── middleware/        # Express middleware
│   ├── repositories/      # Data access
│   ├── routes/            # API routes
│   ├── services/          # Business logic
│   ├── types/             # TypeScript types
│   ├── utils/             # Utility functions
│   └── index.ts           # Entry point
└── package.json           # Dependencies and scripts
```

## Documentation Structure

```
docs/
├── API.md                 # API documentation
├── CONTRIBUTING.md        # Contribution guidelines
├── DEPLOYMENT.md          # Deployment instructions
├── FRONTEND_GUIDE.md      # Frontend development guide
├── TROUBLESHOOTING.md     # Troubleshooting guide
├── USER_GUIDE.md          # User guide
└── canvas-state-structure.md # Canvas state documentation
```

## Key Files

### Frontend

- `src/App.tsx`: Main application component
- `src/store/Store.ts`: Central state management
- `src/editor/Editor.tsx`: Main editor component
- `src/editor/timeline/TimeLine.tsx`: Timeline implementation
- `src/editor/CanvasContainer.tsx`: Canvas rendering

### Backend

- `src/index.ts`: Server entry point
- `src/ffmpeg/FFmpegCommandGenerator.ts`: FFmpeg command generation
- `src/controllers/VideoController.ts`: Video processing endpoints
- `src/services/S3Service.ts`: S3 integration
- `src/config/appConfig.ts`: Application configuration

## Code Organization Patterns

1. **Component Organization**:

   - Components are organized by feature and functionality
   - Common components are shared across features
   - Each component has its own directory with related files

2. **State Management**:

   - MobX stores are organized by domain
   - Manager classes handle specific functionality
   - Store provides access to all managers

3. **API Services**:

   - Each external API has its own service
   - Services handle API communication and data transformation
   - Controllers use services to implement business logic

4. **Utility Functions**:

   - Shared utilities are in the utils directory
   - Domain-specific utilities are co-located with their domain code

5. **Backend Organization**:
   - Controllers handle HTTP requests
   - Services implement business logic
   - Repositories handle data access
   - FFmpeg code is isolated in its own directory
