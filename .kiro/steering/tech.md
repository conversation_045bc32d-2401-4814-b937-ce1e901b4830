# Technology Stack

## Frontend

### Core Technologies

- **React 19.1.0**: UI framework
- **TypeScript 5.8.3**: Type-safe JavaScript
- **MobX 6.13.7**: Reactive state management
- **Fabric.js 5.3.1**: Canvas manipulation and graphics processing

### UI and Animation

- **Material-UI (MUI) 7.2.0**: UI component library
- **Anime.js 3.2.2**: Animation engine
- **Framer Motion 12.23.6**: React animation library
- **React Color**: Color pickers

### Utilities and Tools

- **@dnd-kit**: Drag and drop functionality
- **Axios 1.10.0**: HTTP client
- **React Router 7.6.3**: Routing management
- **Lodash 4.17.21**: Utility functions
- **Wavesurfer.js 7.10.0**: Audio waveform visualization

## Backend

### Core Technologies

- **Node.js**: JavaScript runtime
- **Express 5.1.0**: Web framework
- **TypeScript 5.8.3**: Type-safe JavaScript
- **FFmpeg**: Video processing (external dependency)

### AWS Integration

- **AWS SDK**: S3, Transcribe, Bedrock services
- **S3**: Media storage
- **Transcribe**: Speech-to-text for captions

### Utilities

- **Winston 3.17.0**: Logging
- **Multer 2.0.1**: File upload handling
- **Sharp 0.34.3**: Image processing
- **UUID 11.1.0**: Unique ID generation

## Build System

### Frontend

- **React Scripts 5.0.1**: Build tooling
- **ESLint 9.31.0**: Code linting
- **TypeScript 5.8.3**: Type checking

### Backend

- **ts-node-dev**: Development server with hot reload
- **TypeScript 5.8.3**: Type checking
- **Jest 29.7.0**: Testing framework

## Common Commands

### Frontend

```bash
# Install dependencies
cd frontend && npm install

# Start development server
cd frontend && npm start

# Build for production
cd frontend && npm run build

# Run tests
cd frontend && npm test
```

### Backend

```bash
# Install dependencies
cd server && npm install

# Start development server
cd server && npm run dev

# Build for production
cd server && npm run build

# Run tests
cd server && npm test

# Initialize templates
cd server && npm run init-templates

# Setup fonts
cd server && npm run setup-fonts
```

## Environment Setup

### Frontend (.env)

```
REACT_APP_API_URL=http://localhost:3001
REACT_APP_JAMENDO_API_KEY=your_jamendo_api_key
```

### Backend (.env)

```
PORT=3001
NODE_ENV=development
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
S3_BUCKET=your_bucket_name
```

## Data Models

The core data structure is the Canvas State, which includes:

- Background color, width, height
- Elements (videos, images, audio, text, shapes)
- Animations
- Captions
- Tracks
- Output format

Each element has:

- Unique ID
- Type (video, image, audio, text, shape)
- Placement (position, size, rotation)
- Time frame (start and end times)
- Element-specific properties

## Architecture Patterns

1. **MobX Store Architecture**:

   - Central Store with specialized managers
   - Reactive state updates
   - Observer pattern for UI updates

2. **Manager Pattern**:

   - ElementManager: Element lifecycle
   - AnimationManager: Animation system
   - CaptionManager: Caption handling
   - HistoryManager: Undo/redo functionality
   - TrackManager: Track system

3. **Backend Filter System**:
   - Hierarchical design pattern
   - Base filter interface
   - Specialized filter generators for different media types
