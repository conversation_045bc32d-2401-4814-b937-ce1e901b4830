---
description: 
globs: 
alwaysApply: false
---
# 代码风格指南

## 通用规范
1. 使用有意义的变量名和函数名，采用语义化命名
2. 保持代码简洁，避免重复代码，遵循 DRY 原则
3. 适当添加注释，特别是对复杂逻辑的解释
4. 使用 TypeScript 进行开发，确保类型安全
5. 遵循单一职责原则，保持代码模块化
6. 遵循 SOLID 设计原则
7. 使用 async/await 处理异步操作，避免回调地狱
8. 使用解构赋值和展开运算符简化代码
9. 优先使用 const，其次使用 let，避免使用 var

## 前端规范
1. 组件命名使用 PascalCase（如 `VideoEditor.tsx`）
2. 文件命名规范：
   - 组件文件：`ComponentName.tsx`
   - 样式文件：`ComponentName.module.css` 或 `ComponentName.module.scss`
   - 测试文件：`ComponentName.test.tsx`
   - 工具文件：`utils.ts`
   - Hook 文件：`useHookName.ts`
   - 类型文件：`types.ts`
3. 代码组织：
   - 组件内部逻辑按 hooks、状态、渲染函数顺序组织
   - 使用 React Hooks 进行状态管理和副作用处理
   - 使用 MobX 进行全局状态管理
   - 相关逻辑放在一起
4. 样式规范：
   - 使用 CSS Modules 或 SCSS Modules
   - 遵循 BEM 命名规范
   - 使用主题变量保持样式一致性
   - 使用 CSS-in-JS 时遵循组件化原则
   - 响应式设计使用媒体查询
5. 性能优化：
   - 使用 React.memo 优化渲染
   - 合理使用 useMemo 和 useCallback
   - 避免不必要的重渲染
   - 使用虚拟列表处理大量数据
   - 图片资源使用适当的格式和大小

## 后端规范
1. 文件命名规范：
   - 控制器：`controllerName.controller.ts`
   - 服务：`serviceName.service.ts`
   - 中间件：`middlewareName.middleware.ts`
   - 模型：`modelName.model.ts`
   - 工具：`utils.ts`
2. API 路由命名使用 kebab-case
3. 错误处理：
   - 使用统一的错误处理中间件
   - 定义清晰的错误类型和错误码
   - 使用 try-catch 包装异步操作
   - 记录详细的错误日志
4. 日志规范：
   - 使用统一的日志格式
   - 记录关键操作和错误信息
   - 使用不同的日志级别
   - 包含必要的上下文信息
5. 数据库操作：
   - 使用 TypeORM 进行数据库操作
   - 定义清晰的实体关系
   - 使用事务确保数据一致性
   - 优化查询性能
6. 安全性：
   - 使用参数化查询防止 SQL 注入
   - 实现请求速率限制
   - 使用 Helmet 增强安全性
   - 实现 CORS 策略

## 提交规范
1. 提交信息格式：
   ```
   <type>(<scope>): <subject>
   
   <body>
   
   <footer>
   ```
2. type 类型：
   - feat: 新功能
   - fix: 修复
   - docs: 文档更新
   - style: 代码格式
   - refactor: 重构
   - test: 测试
   - chore: 构建过程或辅助工具的变动
   - perf: 性能优化
   - ci: CI 配置变更
   - revert: 回退
3. scope 范围：
   - frontend: 前端相关
   - backend: 后端相关
   - editor: 编辑器相关
   - auth: 认证相关
   - video: 视频处理相关
4. 提交规范：
   - 每个提交应该是独立的、可测试的
   - 提交信息应该清晰描述改动
   - 重要改动需要更新文档和更新日志
   - 避免在一个提交中包含多个不相关的改动
