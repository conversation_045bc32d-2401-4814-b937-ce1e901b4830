# AWS S3配置指南

## 🚀 快速配置步骤

### 1. 创建AWS账户
如果您还没有AWS账户，请访问 [AWS官网](https://aws.amazon.com/) 注册。

### 2. 创建S3存储桶

1. 登录AWS控制台
2. 搜索并进入"S3"服务
3. 点击"创建存储桶"
4. 配置存储桶：
   - **存储桶名称**: `streampi` (或您喜欢的名称)
   - **AWS区域**: `ap-southeast-1` (新加坡)
   - **公共访问设置**: 保持默认（阻止所有公共访问）
5. 点击"创建存储桶"

### 3. 配置CORS策略

1. 进入您创建的S3存储桶
2. 点击"权限"标签
3. 滚动到"跨源资源共享(CORS)"部分
4. 点击"编辑"
5. 粘贴以下CORS配置：

```json
[
    {
        "AllowedHeaders": [
            "*"
        ],
        "AllowedMethods": [
            "GET",
            "PUT",
            "POST",
            "DELETE",
            "HEAD"
        ],
        "AllowedOrigins": [
            "http://localhost:3000",
            "http://localhost:8080",
            "https://yourdomain.com"
        ],
        "ExposeHeaders": [
            "ETag",
            "x-amz-meta-*"
        ],
        "MaxAgeSeconds": 3000
    }
]
```

6. 点击"保存更改"

### 4. 创建IAM用户和访问密钥

1. 在AWS控制台搜索并进入"IAM"服务
2. 点击左侧菜单的"用户"
3. 点击"创建用户"
4. 配置用户：
   - **用户名**: `streampi-s3-user`
   - **访问类型**: 选择"编程访问"
5. 点击"下一步：权限"
6. 选择"直接附加现有策略"
7. 搜索并选择以下策略：
   - `AmazonS3FullAccess` (或创建自定义策略，见下方)
8. 点击"下一步：标签"（可跳过）
9. 点击"下一步：审核"
10. 点击"创建用户"
11. **重要**: 复制并保存访问密钥ID和秘密访问密钥

### 5. 自定义IAM策略（推荐）

为了更好的安全性，建议创建自定义策略：

1. 在IAM控制台点击"策略"
2. 点击"创建策略"
3. 选择"JSON"标签
4. 粘贴以下策略（替换`your-bucket-name`为您的存储桶名称）：

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:GetObjectAcl",
                "s3:PutObjectAcl"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket",
                "s3:GetBucketLocation"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name"
        }
    ]
}
```

5. 点击"下一步：标签"
6. 点击"下一步：审核"
7. 输入策略名称：`StreampiS3Policy`
8. 点击"创建策略"
9. 将此策略附加到您创建的用户

### 6. 配置环境变量

编辑 `server/.env` 文件，替换以下值：

```env
# AWS S3配置
S3_BUCKET_NAME=streampi  # 您的存储桶名称
S3_REGION=ap-southeast-1  # 您选择的区域
S3_ACCESS_KEY_ID=AKIA...  # 您的访问密钥ID
S3_SECRET_ACCESS_KEY=...  # 您的秘密访问密钥
```

### 7. 重启服务

配置完成后，重启后端服务：

```bash
cd server
npm run dev
```

## 🔍 测试配置

### 测试API端点

```bash
# 测试签名URL生成
curl -X POST http://localhost:8080/api/s3/signed-url \
  -H "Content-Type: application/json" \
  -d '{
    "fileName": "test.jpg",
    "fileSize": 1024000,
    "mimeType": "image/jpeg"
  }'
```

### 测试文件上传

1. 打开前端应用：http://localhost:3000
2. 进入"Uploads"菜单
3. 尝试上传一个小图片文件
4. 检查是否成功上传到S3

## 🚨 常见问题

### 403 Forbidden错误
- 检查AWS凭证是否正确
- 确认IAM用户有足够的S3权限
- 验证存储桶名称是否正确

### CORS错误
- 确认CORS策略已正确配置
- 检查AllowedOrigins是否包含您的域名

### 认证错误
- 确认环境变量已正确设置
- 重启后端服务以加载新的环境变量

## 💰 成本估算

AWS S3的定价（ap-southeast-1区域）：
- 存储：$0.025/GB/月
- PUT请求：$0.0055/1000请求
- GET请求：$0.0004/1000请求

对于开发和测试，成本通常很低（每月几美元）。

## 🔒 安全建议

1. **最小权限原则**: 只授予必要的S3权限
2. **定期轮换密钥**: 定期更新访问密钥
3. **监控使用情况**: 设置CloudWatch警报监控异常活动
4. **启用日志记录**: 启用S3访问日志记录
5. **使用VPC端点**: 在生产环境中考虑使用VPC端点

## 📞 获取帮助

如果遇到问题，请检查：
1. AWS控制台中的CloudTrail日志
2. 后端服务的控制台输出
3. 浏览器开发者工具的网络标签

---

配置完成后，您就可以开始使用S3媒体上传功能了！🎉
