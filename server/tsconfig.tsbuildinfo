{"root": ["./src/index.ts", "./src/types.ts", "./src/controllers/templatecontroller.ts", "./src/controllers/transcribecontroller.ts", "./src/controllers/videocontroller.ts", "./src/ffmpeg/ffmpegcommandgenerator.ts", "./src/ffmpeg/config.ts", "./src/ffmpeg/filterutils.ts", "./src/ffmpeg/types.ts", "./src/ffmpeg/utils.ts", "./src/ffmpeg/core/audioprocessor.ts", "./src/ffmpeg/core/baseprocessor.ts", "./src/ffmpeg/core/commandbuilder.ts", "./src/ffmpeg/core/elementprocessor.ts", "./src/ffmpeg/core/types.ts", "./src/ffmpeg/core/filters/audiofiltergenerator.ts", "./src/ffmpeg/core/filters/basefilter.ts", "./src/ffmpeg/core/filters/imagefiltergenerator.ts", "./src/ffmpeg/core/filters/mediaoverlayfiltergenerator.ts", "./src/ffmpeg/core/filters/shapefiltergenerator.ts", "./src/ffmpeg/core/filters/textfiltergenerator.ts", "./src/ffmpeg/core/filters/videofiltergenerator.ts", "./src/ffmpeg/core/generators/shapeimagegenerator.ts", "./src/ffmpeg/utils/fontmanager.ts", "./src/ffmpeg/utils/asssubtitleutils.ts", "./src/ffmpeg/utils/textelementassutils.ts", "./src/routes/templateroutes.ts", "./src/scripts/initializetemplates.ts", "./src/services/progresstracker.ts", "./src/services/taskqueue.ts", "./src/services/transcribeservice.ts", "./src/tests/test-audio-command.ts", "./src/tests/test-caption-duration.ts", "./src/tests/test-complex-duration.ts", "./src/tests/test-delayed-split-video.ts", "./src/tests/test-duration-comprehensive.ts", "./src/tests/test-duration-fix.ts", "./src/tests/test-fade-in-out-comprehensive.ts", "./src/tests/test-layered-text.ts", "./src/tests/test-multiple-speed-videos.ts", "./src/tests/test-single-video.ts", "./src/tests/test-split-optimization.ts", "./src/tests/test-split-video-comprehensive.ts", "./src/tests/test-split-video-final-fix.ts", "./src/tests/test-split-video-fix.ts", "./src/tests/test-split-video-issue.ts", "./src/tests/test-split-video-with-speed.ts", "./src/tests/test-split-video.ts", "./src/tests/test-video-speed-audio-sync.ts", "./src/utils/logger.ts"], "version": "5.8.3"}