# S3公共访问配置指南

## 问题描述
上传文件到S3成功后，无法通过公共URL访问文件，返回403 Forbidden错误。

## 解决方案

### 1. 代码层面的修改
已在代码中添加了`ACL: "public-read"`设置：

- **单文件上传**：在`PutObjectCommand`中添加ACL设置
- **多部分上传**：在`CreateMultipartUploadCommand`中添加ACL设置

### 2. S3 Bucket配置

#### 2.1 禁用"阻止公共访问"设置
在AWS控制台中：
1. 进入S3服务
2. 选择您的bucket
3. 点击"权限"标签
4. 在"阻止公共访问(存储桶设置)"部分，点击"编辑"
5. 取消勾选以下选项：
   - ✅ 阻止通过新的访问控制列表(ACL)授予的对存储桶和对象的公共访问权限
   - ✅ 阻止通过任何访问控制列表(ACL)授予的对存储桶和对象的公共访问权限
6. 保存更改

#### 2.2 配置Bucket策略
在"权限"标签下的"存储桶策略"部分，添加以下策略：

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowDirectUpload",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::YOUR-ACCOUNT-ID:user/YOUR-USERNAME"
      },
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:GetObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::YOUR-BUCKET-NAME/*"
    },
    {
      "Sid": "AllowListBucket",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::YOUR-ACCOUNT-ID:user/YOUR-USERNAME"
      },
      "Action": "s3:ListBucket",
      "Resource": "arn:aws:s3:::YOUR-BUCKET-NAME"
    },
    {
      "Sid": "AllowPublicRead",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::YOUR-BUCKET-NAME/*"
    }
  ]
}
```

**注意**：请将以下占位符替换为实际值：
- `YOUR-ACCOUNT-ID`：您的AWS账户ID
- `YOUR-USERNAME`：您的IAM用户名
- `YOUR-BUCKET-NAME`：您的S3存储桶名称

#### 2.3 配置CORS
在"权限"标签下的"跨源资源共享(CORS)"部分，添加以下配置：

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
    "AllowedOrigins": [
      "http://localhost:3000",
      "http://localhost:3001",
      "https://your-domain.com"
    ],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3000
  }
]
```

### 3. IAM用户权限
确保您的IAM用户具有以下权限：

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:GetObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::YOUR-BUCKET-NAME",
        "arn:aws:s3:::YOUR-BUCKET-NAME/*"
      ]
    }
  ]
}
```

### 4. 验证配置
配置完成后，可以通过以下方式验证：

1. **上传测试文件**：使用应用程序上传一个文件
2. **检查ACL**：在S3控制台中查看上传的文件，确认其ACL设置为"public-read"
3. **访问测试**：直接在浏览器中访问文件的公共URL

### 5. 安全注意事项
- 公共读取权限意味着任何人都可以访问这些文件
- 建议只对需要公共访问的文件设置public-read ACL
- 考虑使用CloudFront CDN来提供更好的性能和安全性
- 定期审查bucket策略和权限设置

### 6. 故障排除
如果仍然无法访问文件：

1. **检查bucket策略**：确保策略语法正确且已保存
2. **检查ACL设置**：确认上传的文件具有public-read权限
3. **检查区域设置**：确保URL中的区域与bucket实际区域匹配
4. **检查文件路径**：确认文件键(key)路径正确

### 7. 环境变量配置
确保以下环境变量正确配置：

```bash
S3_BUCKET_NAME=your-bucket-name
S3_REGION=your-region
S3_ACCESS_KEY_ID=your-access-key
S3_SECRET_ACCESS_KEY=your-secret-key
```
