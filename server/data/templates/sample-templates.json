[{"id": "template-001", "name": "简约商务介绍", "description": "适合企业介绍和产品展示的简约风格模板，包含标题、副标题和内容区域。", "category": "business", "tags": ["商务", "简约", "企业", "介绍"], "thumbnail": "/api/templates/thumbnails/business-intro.jpg", "platforms": ["YouTube", "LinkedIn", "Facebook"], "formatDescription": "format_business_intro", "canvasState": {"width": 1280, "height": 720, "backgroundColor": "#f8f9fa", "elements": [{"id": "title-text", "type": "text", "name": "主标题", "properties": {"text": "您的企业名称", "fontSize": 48, "fontFamily": "<PERSON><PERSON>", "color": "#2c3e50", "fontWeight": "bold", "textAlign": "center"}, "placement": {"x": 640, "y": 200, "width": 600, "height": 80, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 0, "end": 5000}, "opacity": 1}, {"id": "subtitle-text", "type": "text", "name": "副标题", "properties": {"text": "专业 · 可靠 · 创新", "fontSize": 24, "fontFamily": "<PERSON><PERSON>", "color": "#7f8c8d", "textAlign": "center"}, "placement": {"x": 640, "y": 300, "width": 400, "height": 40, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 500, "end": 5000}, "opacity": 1}, {"id": "logo-placeholder", "type": "shape", "name": "Logo占位符", "properties": {"shapeType": "rectangle", "fill": "#3498db", "stroke": "#2980b9", "strokeWidth": 2}, "placement": {"x": 640, "y": 400, "width": 120, "height": 120, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 1000, "end": 5000}, "opacity": 1}], "animations": [{"id": "title-fade-in", "targetId": "title-text", "type": "fade", "direction": "in", "duration": 1000, "delay": 0}, {"id": "subtitle-slide-in", "targetId": "subtitle-text", "type": "slide", "direction": "up", "duration": 800, "delay": 500}], "captions": [], "duration": 5000}, "metadata": {"duration": 5000, "elementCount": 3, "hasVideo": false, "hasAudio": false, "hasText": true, "hasImages": false, "hasAnimations": true, "complexity": "simple", "aspectRatio": "16:9", "resolution": "1280x720"}, "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "isPublic": true, "authorName": "系统模板", "usageCount": 156, "rating": 4.5, "ratingCount": 32}, {"id": "template-002", "name": "社交媒体故事", "description": "适合Instagram、TikTok等社交媒体平台的竖屏故事模板，包含动态文字和背景。", "category": "social", "tags": ["社交媒体", "故事", "竖屏", "动态"], "thumbnail": "/api/templates/thumbnails/social-story.jpg", "platforms": ["Instagram", "TikTok", "Snapchat", "<PERSON><PERSON><PERSON>"], "formatDescription": "format_story", "canvasState": {"width": 720, "height": 1280, "backgroundColor": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "elements": [{"id": "story-title", "type": "text", "name": "故事标题", "properties": {"text": "今日分享", "fontSize": 36, "fontFamily": "<PERSON><PERSON>", "color": "#ffffff", "fontWeight": "bold", "textAlign": "center"}, "placement": {"x": 360, "y": 300, "width": 500, "height": 60, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 0, "end": 6000}, "opacity": 1}, {"id": "story-content", "type": "text", "name": "内容文字", "properties": {"text": "在这里添加您的内容...", "fontSize": 20, "fontFamily": "<PERSON><PERSON>", "color": "#ffffff", "textAlign": "center"}, "placement": {"x": 360, "y": 640, "width": 600, "height": 200, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 1000, "end": 6000}, "opacity": 1}, {"id": "decorative-circle", "type": "shape", "name": "装饰圆形", "properties": {"shapeType": "circle", "fill": "rgba(255, 255, 255, 0.2)", "stroke": "rgba(255, 255, 255, 0.5)", "strokeWidth": 2}, "placement": {"x": 360, "y": 500, "width": 100, "height": 100, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 500, "end": 6000}, "opacity": 1}], "animations": [{"id": "title-zoom-in", "targetId": "story-title", "type": "scale", "direction": "in", "duration": 1200, "delay": 0}, {"id": "circle-rotate", "targetId": "decorative-circle", "type": "rotate", "direction": "clockwise", "duration": 2000, "delay": 500}], "captions": [], "duration": 6000}, "metadata": {"duration": 6000, "elementCount": 3, "hasVideo": false, "hasAudio": false, "hasText": true, "hasImages": false, "hasAnimations": true, "complexity": "simple", "aspectRatio": "9:16", "resolution": "720x1280"}, "createdAt": "2024-01-01T01:00:00Z", "updatedAt": "2024-01-01T01:00:00Z", "isPublic": true, "authorName": "系统模板", "usageCount": 89, "rating": 4.2, "ratingCount": 18}, {"id": "template-003", "name": "教育课程介绍", "description": "适合在线教育和课程介绍的模板，包含课程标题、讲师信息和课程大纲。", "category": "education", "tags": ["教育", "课程", "在线学习", "介绍"], "thumbnail": "/api/templates/thumbnails/education-course.jpg", "canvasState": {"width": 1920, "height": 1080, "backgroundColor": "#ffffff", "elements": [{"id": "course-title", "type": "text", "name": "课程标题", "properties": {"text": "课程名称", "fontSize": 54, "fontFamily": "<PERSON><PERSON>", "color": "#2c3e50", "fontWeight": "bold", "textAlign": "left"}, "placement": {"x": 100, "y": 150, "width": 800, "height": 80, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 0, "end": 8000}, "opacity": 1}, {"id": "instructor-info", "type": "text", "name": "讲师信息", "properties": {"text": "讲师：张老师 | 10年教学经验", "fontSize": 24, "fontFamily": "<PERSON><PERSON>", "color": "#7f8c8d", "textAlign": "left"}, "placement": {"x": 100, "y": 250, "width": 600, "height": 40, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 1000, "end": 8000}, "opacity": 1}, {"id": "course-outline", "type": "text", "name": "课程大纲", "properties": {"text": "• 第一章：基础知识\n• 第二章：进阶技巧\n• 第三章：实战项目\n• 第四章：总结回顾", "fontSize": 20, "fontFamily": "<PERSON><PERSON>", "color": "#34495e", "textAlign": "left"}, "placement": {"x": 100, "y": 350, "width": 500, "height": 200, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 2000, "end": 8000}, "opacity": 1}, {"id": "accent-bar", "type": "shape", "name": "强调条", "properties": {"shapeType": "rectangle", "fill": "#3498db", "stroke": "none", "strokeWidth": 0}, "placement": {"x": 50, "y": 150, "width": 8, "height": 400, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 500, "end": 8000}, "opacity": 1}], "animations": [{"id": "title-slide-in", "targetId": "course-title", "type": "slide", "direction": "left", "duration": 1000, "delay": 0}, {"id": "bar-grow", "targetId": "accent-bar", "type": "scale", "direction": "up", "duration": 800, "delay": 500}], "captions": [], "duration": 8000}, "metadata": {"duration": 8000, "elementCount": 4, "hasVideo": false, "hasAudio": false, "hasText": true, "hasImages": false, "hasAnimations": true, "complexity": "medium", "aspectRatio": "16:9", "resolution": "1920x1080"}, "createdAt": "2024-01-01T02:00:00Z", "updatedAt": "2024-01-01T02:00:00Z", "isPublic": true, "authorName": "系统模板", "usageCount": 234, "rating": 4.7, "ratingCount": 45}, {"id": "template-004", "name": "产品推广视频", "description": "适合产品展示和营销推广的动态模板，包含产品特点展示和行动号召。", "category": "marketing", "tags": ["营销", "产品", "推广", "销售"], "thumbnail": "/api/templates/thumbnails/product-promo.jpg", "canvasState": {"width": 1280, "height": 720, "backgroundColor": "#1a1a1a", "elements": [{"id": "product-name", "type": "text", "name": "产品名称", "properties": {"text": "革命性产品", "fontSize": 42, "fontFamily": "<PERSON><PERSON>", "color": "#ffffff", "fontWeight": "bold", "textAlign": "center"}, "placement": {"x": 640, "y": 150, "width": 600, "height": 60, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 0, "end": 10000}, "opacity": 1}, {"id": "feature-1", "type": "text", "name": "特点1", "properties": {"text": "✓ 高效便捷", "fontSize": 24, "fontFamily": "<PERSON><PERSON>", "color": "#2ecc71", "textAlign": "left"}, "placement": {"x": 200, "y": 300, "width": 300, "height": 40, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 2000, "end": 10000}, "opacity": 1}, {"id": "feature-2", "type": "text", "name": "特点2", "properties": {"text": "✓ 安全可靠", "fontSize": 24, "fontFamily": "<PERSON><PERSON>", "color": "#2ecc71", "textAlign": "left"}, "placement": {"x": 200, "y": 360, "width": 300, "height": 40, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 3000, "end": 10000}, "opacity": 1}, {"id": "cta-button", "type": "shape", "name": "行动按钮", "properties": {"shapeType": "rectangle", "fill": "#e74c3c", "stroke": "none", "strokeWidth": 0}, "placement": {"x": 640, "y": 500, "width": 200, "height": 60, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 6000, "end": 10000}, "opacity": 1}, {"id": "cta-text", "type": "text", "name": "按钮文字", "properties": {"text": "立即购买", "fontSize": 20, "fontFamily": "<PERSON><PERSON>", "color": "#ffffff", "fontWeight": "bold", "textAlign": "center"}, "placement": {"x": 640, "y": 500, "width": 200, "height": 60, "scaleX": 1, "scaleY": 1}, "timeFrame": {"start": 6000, "end": 10000}, "opacity": 1}], "animations": [{"id": "title-glow", "targetId": "product-name", "type": "glow", "direction": "in", "duration": 1500, "delay": 0}, {"id": "feature1-pop", "targetId": "feature-1", "type": "scale", "direction": "in", "duration": 600, "delay": 2000}, {"id": "feature2-pop", "targetId": "feature-2", "type": "scale", "direction": "in", "duration": 600, "delay": 3000}, {"id": "cta-pulse", "targetId": "cta-button", "type": "pulse", "direction": "continuous", "duration": 1000, "delay": 6000}], "captions": [], "duration": 10000}, "metadata": {"duration": 10000, "elementCount": 5, "hasVideo": false, "hasAudio": false, "hasText": true, "hasImages": false, "hasAnimations": true, "complexity": "medium", "aspectRatio": "16:9", "resolution": "1280x720"}, "createdAt": "2024-01-01T03:00:00Z", "updatedAt": "2024-01-01T03:00:00Z", "isPublic": true, "authorName": "系统模板", "usageCount": 178, "rating": 4.3, "ratingCount": 29}]