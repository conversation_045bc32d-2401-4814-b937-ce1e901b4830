# Server Architecture Documentation

## Overview

This document describes the architecture of the video processing server, which provides a REST API for video generation and processing using FFmpeg.

## System Components

### 1. Express Server (index.ts)

The main server component handles HTTP requests and manages video processing:

- REST API endpoints for video generation and progress tracking
- Asynchronous video processing with real-time progress monitoring
- CORS support and large payload handling (50MB limit)
- Task management system with unique IDs

### 2. FFmpeg Command Generation System

A modular system for generating complex FFmpeg commands:

#### Core Components:

- `FFmpegCommandGenerator`: Main class for command generation
- Filter Generators:
  - `AudioFilterGenerator`
  - `ImageFilterGenerator`
  - `TextFilterGenerator`
  - `VideoFilterGenerator`

#### Filter System Architecture

The filter system uses a hierarchical design pattern:

1. **Base Filter Interface**

```typescript
interface BaseFilterGenerator {
  generateFilter(
    inputIndex: number, // Input stream index
    element: any, // Element to apply filter to
    startTime: number, // Start time
    duration: number, // Duration
    index: number, // Element index
    canvasWidth: number, // Canvas width
    canvasHeight: number // Canvas height
  ): string;
}
```

2. **Audio Processing Pipeline**

The AudioFilterGenerator implements sophisticated audio processing:

```typescript
class AudioFilterGenerator implements BaseFilterGenerator {
  generateFilter(...) {
    // 1. Stream selection
    // 2. Duration and timing control
    // 3. Fade effects
    // 4. Audio padding
  }
}
```

Key Audio Features:

- Precise timing control with atrim and asetpts
- Automatic fade in/out effects
- Delay management for synchronization
- Audio padding for duration matching
- Multi-track audio mixing

Audio Processing Steps:

1. **Timing Control**

   - Duration trimming
   - Timestamp reset
   - Delay addition

2. **Fade Effects**

   - Automatic fade in/out calculation
   - Dynamic duration based on clip length
   - Maximum 1-second fade duration

3. **Audio Synchronization**
   - Delay compensation
   - Stream padding
   - Duration matching

### 3. Media Processing Features

#### Video Processing

- Scale and crop operations
- Rotation handling
- Format conversion
- Flip operations
- Opacity adjustment

#### Audio Processing

- Timing control
- Fade effects
- Multi-track mixing
- Duration matching
- Volume control

#### Common Features

- Effect application
- Filter processing
- Border addition
- Format conversion

## API Endpoints

### POST /api/generateVideo

Initiates video generation process

- Input: Canvas state with media elements
- Output: Task ID for progress tracking

### GET /api/progress/:taskId

Retrieves progress information for a task

- Input: Task ID
- Output: Progress information including:
  - Overall progress percentage
  - Current frame
  - Total frames
  - FPS
  - Processing speed

## Data Flow

1. Client sends canvas state to /api/generateVideo
2. Server generates FFmpeg command
3. FFmpeg process is spawned
4. Progress is monitored through stderr
5. Client polls progress endpoint
6. Final video is generated

## Security Considerations

- Input validation for all parameters
- File size limits
- Process timeout limits
- Resource cleanup
- CORS configuration

## Performance Considerations

- Asynchronous processing
- Progress tracking with minimal overhead
- Resource limits:
  - Maximum video duration: 3600 seconds
  - Maximum dimension: 7680px
  - Maximum file size: 50MB

## Future Improvements

1. Error Handling

```typescript
interface FFmpegError extends Error {
  code: string;
  type: "INPUT" | "OUTPUT" | "PROCESS";
}
```

2. Audio Processing Enhancements

- Advanced audio effects (reverb, echo)
- Volume normalization
- Audio waveform visualization
- Multiple audio track mixing
- Audio format conversion

3. Resource Management

- Implement temporary file cleanup
- Add file upload size limits
- Add request timeouts
- Audio buffer management

4. Monitoring & Logging

- Add structured logging
- Add performance metrics
- Add health check endpoint
- Audio processing metrics

5. Scalability

- Queue system for multiple requests
- Worker processes for parallel processing
- Cloud storage integration
- Distributed audio processing

## Dependencies

- express: Web framework
- cors: Cross-origin resource sharing
- FFmpeg: Video processing
- TypeScript: Type safety and developer experience
