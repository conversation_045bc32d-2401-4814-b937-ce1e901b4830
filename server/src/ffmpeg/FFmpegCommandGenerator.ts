import { CanvasState } from "./types";
import { FFmpegUtils } from "./utils";
import { FFMPEG_CONFIG } from "./config";
import { CommandBuilder } from "./core/CommandBuilder";
import { ElementProcessor } from "./core/ElementProcessor";
import { AudioProcessor } from "./core/AudioProcessor";
import { ProcessingContext, ProgressTracker } from "./core/types";
import * as fs from "fs";
/**
 * Main class responsible for generating FFmpeg commands from canvas state
 * Coordinates the processing of media elements and building of the final command
 */
export class FFmpegCommandGenerator {
  /** Command builder for constructing the final FFmpeg command */
  private readonly commandBuilder: CommandBuilder;

  /** Processor for handling different types of media elements */
  private readonly elementProcessor: ElementProcessor;

  /** Processor for handling audio detection and processing */
  private readonly audioProcessor: AudioProcessor;

  /** Path to temporary subtitle file if created */
  private tempSubtitlePath: string | null = null;

  /** Array of all temporary files created during command generation */
  private tempFiles: string[] = [];

  /**
   * Creates a new FFmpegCommandGenerator instance
   */
  constructor() {
    this.commandBuilder = new CommandBuilder();
    this.elementProcessor = new ElementProcessor();
    this.audioProcessor = new AudioProcessor();
  }

  /**
   * Generates a complete FFmpeg command from the canvas state
   * @param canvasState - Canvas state object containing all media elements and output configuration
   * @param taskId - Optional task ID for progress tracking
   * @param progressTracker - Optional progress tracker
   * @returns Complete FFmpeg command string
   * @throws Error if the canvas state is invalid
   */
  async generateCommand(
    canvasState: CanvasState,
    taskId?: string,
    progressTracker?: ProgressTracker
  ): Promise<string> {
    try {
      // 确保在开始新的命令生成前清理之前的资源
      this.cleanup();

      // 记录任务开始
      console.log(`开始为任务 ${taskId || "unknown"} 生成FFmpeg命令`);

      this.validateCanvasState(canvasState);

      if (taskId && progressTracker) {
        progressTracker.updateCommandGenerationProgress(
          taskId,
          "initializing",
          0
        );
      }

      const {
        width: canvasWidth,
        height: canvasHeight,
        backgroundColor,
        elements: originalElements,
        outputFormat = this.commandBuilder.getDefaultOutputFormat(),
        captions,
        globalCaptionStyle,
      } = canvasState;

      // 不再反转元素数组，保持原始顺序
      // 这样可以确保元素按照用户指定的顺序处理
      const elements = [...originalElements];

      // Process elements in reverse order for proper layering
      // Audio and video elements will be processed separately

      // Calculate the total duration of the video in seconds
      // 同时考虑媒体元素和字幕的最大结束时间
      const duration = this.commandBuilder.calculateVideoDuration(
        elements,
        captions
      );
      console.log(`计算的视频总时长: ${duration}秒 (包含字幕的最大结束时间)`);

      if (taskId && progressTracker) {
        progressTracker.updateCommandGenerationProgress(
          taskId,
          "detecting_audio",
          5
        );
      }

      // Create background color input
      // Handle transparent background by using black@0 (transparent black)
      const backgroundColorValue =
        backgroundColor === "transparent" ? "black@0" : backgroundColor;
      const inputs: string[] = [
        `-f lavfi -i color=c=${backgroundColorValue}:s=${canvasWidth}x${canvasHeight}:d=${duration}`,
      ];

      // Initialize filter arrays
      // For transparent background, use RGBA format to preserve alpha channel
      const backgroundFormat =
        backgroundColor === "transparent" ? "rgba" : "yuv420p";
      const filterComplex: string[] = [`[0:v]format=${backgroundFormat}[bg]`];
      const audioFilters: string[] = [];
      let audioStreamCount = 0;
      let lastVideoLabel = "bg";

      // We'll process captions later after all elements are processed

      // 检测哪些元素包含音频
      if (taskId && progressTracker) {
        progressTracker.updateCommandGenerationProgress(
          taskId,
          "detecting_audio_elements",
          5
        );
      }

      const audioElements = await this.audioProcessor.detectAudioElements(
        elements,
        taskId,
        progressTracker
      );

      if (taskId && progressTracker) {
        progressTracker.updateCommandGenerationProgress(
          taskId,
          "processing_elements",
          10
        );
      }

      const hasAudioContent = audioElements.some((el: boolean) => el === true);

      // 第一步：处理所有元素的音频部分（包括视频元素中的音频）
      if (hasAudioContent) {
        for (const [index, element] of elements.entries()) {
          // 只处理音频元素或包含音频的视频元素
          if (
            element.type === "audio" ||
            (element.type === "video" && audioElements[index])
          ) {
            const { timeFrame } = element;
            const startTime = timeFrame.start / 1000;
            const elementDuration = (timeFrame.end - timeFrame.start) / 1000;

            const audioContext: ProcessingContext = {
              index,
              inputs,
              filterComplex,
              audioFilters,
              lastVideoLabel,
              audioStreamCount,
              startTime,
              elementDuration,
              canvasWidth,
              canvasHeight,
              duration,
              hasAudioContent,
              audioElements,
            };

            // 处理音频元素
            const result = this.elementProcessor.processAudioElement(
              element,
              audioContext
            );
            audioStreamCount = result.audioStreamCount;
          }

          if (taskId && progressTracker) {
            const progress =
              20 + Math.round(((index + 1) / elements.length) * 20);
            progressTracker.updateCommandGenerationProgress(
              taskId,
              "processing_audio_elements",
              progress
            );
          }
        }
      }

      // 第二步：处理所有元素的视频部分（忽略音频处理）
      for (const [index, element] of elements.entries()) {
        if (element.type !== "audio") {
          const { timeFrame } = element;
          const startTime = timeFrame.start / 1000;
          const elementDuration = (timeFrame.end - timeFrame.start) / 1000;

          const videoContext: ProcessingContext = {
            index,
            inputs,
            filterComplex,
            audioFilters,
            lastVideoLabel,
            audioStreamCount,
            startTime,
            elementDuration,
            canvasWidth,
            canvasHeight,
            duration,
            hasAudioContent,
            audioElements,
            skipAudioProcessing: true, // 添加标志，跳过音频处理
          };

          let result;
          switch (element.type) {
            case "video":
              result = this.elementProcessor.processVideoElement(
                element,
                videoContext
              );
              break;
            case "image":
              result = this.elementProcessor.processImageElement(
                element,
                videoContext
              );
              break;
            case "gif":
              result = this.elementProcessor.processGifElement(
                element,
                videoContext
              );
              break;
            case "text":
              result = this.elementProcessor.processTextElement(
                element,
                videoContext
              );
              break;
            case "shape":
              // Shape元素需要异步处理
              result = await this.elementProcessor.processShapeElementAsync(
                element,
                videoContext
              );
              break;
          }

          if (result) {
            lastVideoLabel = result.lastVideoLabel;
          }
        }

        if (taskId && progressTracker) {
          const progress =
            40 + Math.round(((index + 1) / elements.length) * 50);
          progressTracker.updateCommandGenerationProgress(
            taskId,
            "processing_visual_elements",
            progress
          );
        }
      }

      // Step 3: Process captions/subtitles
      if (captions && captions.length > 0) {
        if (taskId && progressTracker) {
          progressTracker.updateCommandGenerationProgress(
            taskId,
            "processing_captions",
            75
          );
        }

        // Create ASS subtitle file with styling
        const subtitlePath = this.createSubtitleFile(
          captions,
          globalCaptionStyle,
          canvasWidth,
          canvasHeight
        );

        // Apply subtitle filter
        if (subtitlePath) {
          const subtitleFilter = FFmpegUtils.generateSubtitleFilter(
            subtitlePath,
            canvasWidth,
            canvasHeight,
            globalCaptionStyle
          );

          // Add subtitle filter to video stream
          filterComplex.push(`[${lastVideoLabel}]${subtitleFilter}[subtitle]`);
          lastVideoLabel = "subtitle";
        }
      }

      // Step 4: Build final command
      if (taskId && progressTracker) {
        progressTracker.updateCommandGenerationProgress(
          taskId,
          "generating_command",
          95
        );
      }

      // 输出优化信息
      console.log(`总共使用了 ${inputs.length} 个输入源`);
      console.log(`总共处理了 ${elements.length} 个媒体元素`);
      console.log(
        `输入源比例: ${(inputs.length / elements.length).toFixed(2)}`
      );

      // 如果输入源比例小于1，说明有输入源被重用了，这是优化的结果
      if (inputs.length < elements.length) {
        console.log("检测到输入源重用，优化成功！");
      }

      // Generate final command
      const command = this.commandBuilder.buildFinalCommand(
        inputs,
        filterComplex,
        audioFilters,
        lastVideoLabel,
        outputFormat,
        backgroundColor
      );

      if (taskId && progressTracker) {
        progressTracker.updateCommandGenerationProgress(
          taskId,
          "command_generated",
          100
        );
      }

      return command;
    } catch (error) {
      // Make sure we clean up all resources if command generation fails
      console.error(
        `任务 ${taskId || "unknown"} 命令生成失败，正在清理资源...`
      );
      this.destroy();

      // 记录详细错误信息
      console.error("Error generating FFmpeg command:", error);
      if (error instanceof Error) {
        console.error(`错误类型: ${error.name}, 错误消息: ${error.message}`);
        console.error(`错误堆栈: ${error.stack}`);
      }

      // 如果有进度跟踪器，更新失败状态
      if (taskId && progressTracker) {
        try {
          // 只传递三个参数，符合接口定义
          progressTracker.updateCommandGenerationProgress(taskId, "failed", 0);
          // 错误信息通过日志记录，而不是通过进度跟踪器
          console.error(
            `任务 ${taskId} 失败: ${
              error instanceof Error ? error.message : "未知错误"
            }`
          );
        } catch (progressError) {
          console.error("更新进度状态失败:", progressError);
        }
      }

      // Re-throw the error for the caller to handle
      throw error;
    } finally {
      // 确保在任何情况下都记录命令生成的结束
      console.log(`任务 ${taskId || "unknown"} 的FFmpeg命令生成过程结束`);
    }
  }

  /**
   * Validates that the canvas state is valid for processing
   * @param canvasState - Canvas state to validate
   * @throws Error if the canvas state is invalid
   */
  private validateCanvasState(canvasState: CanvasState): void {
    // Log the canvas state for debugging
    console.log("Validating canvas state:", JSON.stringify(canvasState));

    // Validate using utility function
    FFmpegUtils.validateCanvasState(canvasState);

    // Additional validations
    if (canvasState.width <= 0 || canvasState.height <= 0) {
      throw new Error(
        `Invalid canvas dimensions: ${canvasState.width}x${canvasState.height}`
      );
    }

    if (
      canvasState.width > FFMPEG_CONFIG.MAX_DIMENSION ||
      canvasState.height > FFMPEG_CONFIG.MAX_DIMENSION
    ) {
      throw new Error(
        `Canvas dimensions exceed maximum allowed size of ${FFMPEG_CONFIG.MAX_DIMENSION}px`
      );
    }

    if (
      canvasState.elements.length === 0 &&
      (!canvasState.captions || canvasState.captions.length === 0)
    ) {
      throw new Error(
        "Canvas must contain at least one element or have captions"
      );
    }
  }
  /**
   * Creates a temporary subtitle file from captions
   * @param captions - Array of captions
   * @param globalCaptionStyle - Global caption style
   * @param canvasWidth - Canvas width
   * @param canvasHeight - Canvas height
   * @returns Path to the created subtitle file
   */
  private createSubtitleFile(
    captions: CanvasState["captions"],
    globalCaptionStyle?: CanvasState["globalCaptionStyle"],
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!captions || captions.length === 0) {
      return "";
    }

    // Create the subtitle file using utility function with styling if provided
    const subtitlePath = globalCaptionStyle
      ? FFmpegUtils.createStyledSubtitleFile(
          captions,
          globalCaptionStyle,
          canvasWidth,
          canvasHeight
        )
      : FFmpegUtils.createSubtitleFile(captions);

    // Register for cleanup
    this.tempSubtitlePath = subtitlePath;
    this.tempFiles.push(subtitlePath);

    return subtitlePath;
  }

  /**
   * Destroy and clean up resources used by this instance
   */
  destroy(): void {
    this.cleanup();
    this.elementProcessor.cleanup();
    this.audioProcessor.cleanup();
  }

  /**
   * Clean up temporary files created during command generation
   */
  cleanup(): void {
    let cleanedCount = 0;
    let errorCount = 0;

    // Clean up subtitle file
    if (this.tempSubtitlePath && fs.existsSync(this.tempSubtitlePath)) {
      try {
        fs.unlinkSync(this.tempSubtitlePath);
        console.log(
          `Removed temporary subtitle file: ${this.tempSubtitlePath}`
        );
        this.tempSubtitlePath = null;
        cleanedCount++;
      } catch (error) {
        console.error(`Error removing temporary subtitle file: ${error}`);
        errorCount++;
      }
    }

    // Clean up all other temporary files
    for (const filePath of this.tempFiles) {
      if (fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          console.log(`Removed temporary file: ${filePath}`);
          cleanedCount++;
        } catch (error) {
          console.error(`Error removing temporary file ${filePath}: ${error}`);
          errorCount++;

          // 如果删除失败，尝试使用异步方式延迟删除
          setTimeout(() => {
            try {
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                console.log(`延迟删除临时文件成功: ${filePath}`);
              }
            } catch (retryError) {
              console.error(`延迟删除临时文件失败 ${filePath}: ${retryError}`);
            }
          }, 1000); // 延迟1秒后尝试删除
        }
      }
    }

    // 记录清理结果
    if (cleanedCount > 0 || errorCount > 0) {
      console.log(`临时文件清理结果: 成功=${cleanedCount}, 失败=${errorCount}`);
    }

    // Clear the array
    this.tempFiles = [];
  }
}
