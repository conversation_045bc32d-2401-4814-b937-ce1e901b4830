import { OutputFormat, EncodingParams } from "./types";
import * as path from "path";
import * as os from "os";
import { execSync } from "child_process";

/**
 * Default system font path based on operating system
 */
const getDefaultFontPath = (): string => {
  switch (os.platform()) {
    case "darwin": // macOS
      return "/System/Library/Fonts/Arial.ttf";
    case "win32": // Windows
      return path.join(
        process.env.WINDIR || "C:\\Windows",
        "Fonts",
        "arial.ttf"
      );
    default: // Linux and others
      return "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf";
  }
};

/**
 * Default Chinese font path based on operating system
 */
const getDefaultChineseFontPath = (): string => {
  switch (os.platform()) {
    case "darwin": // macOS
      return "/System/Library/Fonts/PingFang.ttc";
    case "win32": // Windows
      return path.join(
        process.env.WINDIR || "C:\\Windows",
        "Fonts",
        "simhei.ttf"
      );
    default: // Linux and others
      return "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc";
  }
};

/**
 * Check if font file exists
 */
const fontExists = (fontPath: string): boolean => {
  try {
    return require("fs").existsSync(fontPath);
  } catch {
    return false;
  }
};

/**
 * Get available system fonts for Chinese text
 */
const getAvailableChineseFonts = (): string[] => {
  const candidateFonts: string[] = [];

  switch (os.platform()) {
    case "darwin": // macOS
      candidateFonts.push(
        "/System/Library/Fonts/PingFang.ttc",
        "/System/Library/Fonts/STHeiti Light.ttc",
        "/System/Library/Fonts/STHeiti Medium.ttc",
        "/Library/Fonts/Arial Unicode MS.ttf"
      );
      break;
    case "win32": // Windows
      const windir = process.env.WINDIR || "C:\\Windows";
      candidateFonts.push(
        path.join(windir, "Fonts", "simhei.ttf"),
        path.join(windir, "Fonts", "simsun.ttc"),
        path.join(windir, "Fonts", "msyh.ttc"),
        path.join(windir, "Fonts", "msyhbd.ttc")
      );
      break;
    default: // Linux
      candidateFonts.push(
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
        "/usr/share/fonts/truetype/arphic/ukai.ttc",
        "/usr/share/fonts/truetype/arphic/uming.ttc"
      );
      break;
  }

  return candidateFonts.filter(fontExists);
};

/**
 * FFmpeg configuration constants
 */
export const FFMPEG_CONFIG = {
  /** Maximum allowed video duration in seconds (1 hour) */
  MAX_DURATION: 3600,

  /** Maximum allowed dimension in pixels (8K resolution) */
  MAX_DIMENSION: 7680,

  /** Default transition duration in seconds */
  DEFAULT_TRANSITION_DURATION: 2,

  /** Default output format configuration */
  DEFAULT_OUTPUT_FORMAT: {
    codec: "libx264",
    format: "mp4",
    quality: "medium" as const,
    frameRate: 30,
  } satisfies OutputFormat,

  /** Encoding presets for different quality levels */
  ENCODING_PRESETS: {
    high: {
      preset: "slow",
      crf: 18,
      profile: "high",
    } satisfies EncodingParams,

    medium: {
      preset: "medium",
      crf: 23,
      profile: "main",
    } satisfies EncodingParams,

    low: {
      preset: "faster",
      crf: 28,
      profile: "baseline",
    } satisfies EncodingParams,
  },

  /** Default font configuration */
  DEFAULT_FONT: {
    path: getDefaultFontPath(),
    weight: "normal",
    style: "normal",
    align: "left",
  },

  /** Chinese font configuration */
  CHINESE_FONT: {
    path: getDefaultChineseFontPath(),
    availableFonts: getAvailableChineseFonts(),
    fallbackFonts: [
      getDefaultChineseFontPath(),
      getDefaultFontPath(), // 最后回退到默认字体
    ].filter(fontExists),
  },

  /** Hardware acceleration options */
  HW_ACCELERATION: {
    enabled: false,

    // 转场处理策略
    // "auto": 自动检测转场效果，有转场时使用软件编码，无转场时使用硬件加速
    // "software": 有转场效果时始终使用软件编码
    // "hardware": 有转场效果时使用高质量硬件编码参数
    // "always_software": 始终使用软件编码，无论是否有转场效果
    transitionStrategy: "auto" as
      | "auto"
      | "software"
      | "hardware"
      | "always_software",

    // 检测可用的硬件加速方法
    detect: (): { type: string; available: boolean }[] => {
      const accelerators = [
        { type: "nvidia", available: false },
        { type: "intel", available: false },
        { type: "amd", available: false },
        { type: "apple", available: false },
        { type: "vaapi", available: false },
      ];

      const platform = os.platform();

      try {
        // 检测 macOS 的 VideoToolbox
        if (platform === "darwin") {
          accelerators.find((a) => a.type === "apple")!.available = true;
          console.log("检测到 Apple VideoToolbox 硬件加速可用");
        }

        // 检测 NVIDIA GPU
        try {
          execSync("nvidia-smi -L", { stdio: "ignore" });
          accelerators.find((a) => a.type === "nvidia")!.available = true;
          console.log("检测到 NVIDIA GPU 硬件加速可用");
        } catch (e) {
          // NVIDIA 工具不可用
        }

        // 检测 Intel GPU
        if (platform === "win32") {
          try {
            // Windows 上检测 Intel GPU
            const output = execSync(
              "wmic path win32_VideoController get name",
              { encoding: "utf8" }
            );
            if (output.toLowerCase().includes("intel")) {
              accelerators.find((a) => a.type === "intel")!.available = true;
              console.log("检测到 Intel GPU 硬件加速可用");
            }
          } catch (e) {
            // 无法检测 Intel GPU
          }
        } else {
          // Linux 上检测 Intel/AMD GPU 和 VAAPI
          try {
            const output = execSync("lspci | grep -i vga", {
              encoding: "utf8",
            });
            if (output.toLowerCase().includes("intel")) {
              accelerators.find((a) => a.type === "intel")!.available = true;
              console.log("检测到 Intel GPU 硬件加速可用");
            }
            if (
              output.toLowerCase().includes("amd") ||
              output.toLowerCase().includes("radeon")
            ) {
              accelerators.find((a) => a.type === "amd")!.available = true;
              console.log("检测到 AMD GPU 硬件加速可用");
            }

            // 检测 VAAPI
            if (require("fs").existsSync("/dev/dri/renderD128")) {
              accelerators.find((a) => a.type === "vaapi")!.available = true;
              console.log("检测到 VAAPI 硬件加速可用");
            }
          } catch (e) {
            // 无法检测 GPU
          }
        }
      } catch (error) {
        console.warn("硬件加速检测失败:", error);
      }

      return accelerators;
    },

    // 获取当前系统最佳的硬件加速方法
    getBestAccelerator: (): string => {
      // 如果策略是始终使用软件编码，则直接返回空字符串
      if (
        FFMPEG_CONFIG.HW_ACCELERATION.transitionStrategy === "always_software"
      ) {
        console.log("策略设置为始终使用软件编码，将不使用硬件加速");
        return "";
      }

      const accelerators = FFMPEG_CONFIG.HW_ACCELERATION.detect();
      const available = accelerators.filter((a) => a.available);

      if (available.length === 0) {
        console.log("未检测到可用的硬件加速，将使用软件编码");
        return "";
      }

      // 优先级: NVIDIA > Apple > Intel > AMD > VAAPI
      const priorityOrder = ["nvidia", "apple", "intel", "amd", "vaapi"];

      for (const priority of priorityOrder) {
        const found = available.find((a) => a.type === priority);
        if (found) {
          console.log(`将使用 ${found.type} 硬件加速`);
          return found.type;
        }
      }

      return "";
    },
  },

  /** Temporary file management */
  TEMP_FILES: {
    directory: os.tmpdir(),
    prefix: "ffmpeg_",
  },
};

/**
 * ASS subtitle configuration
 */
export const ASS_CONFIG = {
  /** Default font size ratio relative to canvas height */
  DEFAULT_FONT_SIZE_RATIO: 0.035,

  /** Minimum font size in pixels */
  MIN_FONT_SIZE: 16,

  /** Default margin ratio relative to canvas dimensions */
  DEFAULT_MARGIN_RATIO: 0.02,

  /** Default stroke width for outlines */
  DEFAULT_STROKE_WIDTH: 2,

  /** Default shadow blur radius */
  DEFAULT_SHADOW_BLUR: 2,

  /** Supported ASS alignment values */
  ALIGNMENT: {
    LEFT: 1,
    CENTER: 2,
    RIGHT: 3,
    LEFT_TOP: 7,
    CENTER_TOP: 8,
    RIGHT_TOP: 9,
  } as const,

  /** Default ASS style template */
  DEFAULT_STYLE: {
    fontFamily: "Arial",
    fontSize: 20,
    fontColor: "#FFFFFF",
    fontWeight: 700,
    textAlign: "center" as const,
    lineHeight: 1.2,
    charSpacing: 0,
    styles: ["bold"],
    strokeWidth: 2,
    strokeColor: "#000000",
    shadowColor: "#000000",
    shadowBlur: 2,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    backgroundColor: "transparent",
    useGradient: false,
    gradientColors: ["#FFFFFF", "#000000"],
  },

  /** Color format conversion helpers */
  COLORS: {
    TRANSPARENT: "&H00000000",
    WHITE: "&HFFFFFF&",
    BLACK: "&H000000&",
    RED: "&H0000FF&",
    GREEN: "&H00FF00&",
    BLUE: "&HFF0000&",
  } as const,
} as const;
