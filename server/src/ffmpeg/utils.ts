import { FFMPEG_CONFIG, ASS_CONFIG } from "./config";
import { promisify } from "util";
import { exec } from "child_process";
import { CanvasState, MediaElement, Caption } from "./types";
import { ASSSubtitleUtils } from "./utils/assSubtitleUtils";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

// Promisified version of exec for async/await usage
const execAsync = promisify(exec);

/**
 * Utility class for FFmpeg-related operations
 * Provides helper methods for validation, file operations, and common FFmpeg tasks
 */
export class FFmpegUtils {
  /**
   * Validates and normalizes a CRF (Constant Rate Factor) value
   * @param crf - CRF value to validate
   * @returns Normalized CRF value between 0 and 51
   */
  static validateCrf(crf: number): number {
    return Math.max(0, Math.min(51, Math.round(crf)));
  }

  /**
   * Validates a canvas state object for processing
   * @param canvasState - Canvas state to validate
   * @throws Error if the canvas state is invalid
   */
  static validateCanvasState(canvasState: CanvasState): void {
    if (!canvasState.width || !canvasState.height) {
      throw new Error("Canvas dimensions must be specified");
    }
    if (!canvasState.elements || !Array.isArray(canvasState.elements)) {
      throw new Error("Elements array must be provided");
    }

    // Validate dimensions
    if (
      canvasState.width > FFMPEG_CONFIG.MAX_DIMENSION ||
      canvasState.height > FFMPEG_CONFIG.MAX_DIMENSION
    ) {
      throw new Error(
        `Canvas dimensions exceed maximum limit of ${FFMPEG_CONFIG.MAX_DIMENSION}px`
      );
    }

    // Validate duration
    const duration =
      Math.max(...canvasState.elements.map((el) => el.timeFrame.end)) / 1000;
    if (duration > FFMPEG_CONFIG.MAX_DURATION) {
      throw new Error(
        `Video duration exceeds maximum limit of ${FFMPEG_CONFIG.MAX_DURATION} seconds`
      );
    }

    // Validate elements
    canvasState.elements.forEach((element, index) => {
      if (!element.type || !element.properties) {
        throw new Error(
          `Invalid element at index ${index}: missing type or properties`
        );
      }
      if (
        element.type !== "text" &&
        element.type !== "audio" &&
        !element.placement
      ) {
        throw new Error(
          `Invalid element at index ${index}: missing placement data`
        );
      }
      if (
        ["image", "video", "audio"].includes(element.type) &&
        !element.properties.src
      ) {
        throw new Error(`Element at index ${index} missing src property`);
      }
    });
  }

  /**
   * Checks if a media file has an audio track
   * @param filePath - Path to the media file
   * @returns Promise resolving to true if the file has audio, false otherwise
   */
  static async hasAudioTrack(filePath: string): Promise<boolean> {
    try {
      // Use ffprobe to check for audio streams
      const command = `ffprobe -v quiet -select_streams a -show_streams "${filePath}"`;

      // Execute the command and get the result
      const { stdout } = await execAsync(command);

      // If stdout contains data, the file has audio streams
      return Boolean(stdout && stdout.trim().length > 0);
    } catch (error) {
      console.warn(`Failed to check audio tracks for ${filePath}:`, error);
      return false;
    }
  }

  /**
   * Gets encoding parameters based on quality setting
   * @param quality - Quality setting ("low", "medium", "high")
   * @returns Encoding parameters with preset, crf, and profile
   */
  static getEncodingParams(quality: string) {
    // Get preset from config or fall back to medium quality
    const preset =
      FFMPEG_CONFIG.ENCODING_PRESETS[
        quality as keyof typeof FFMPEG_CONFIG.ENCODING_PRESETS
      ] || FFMPEG_CONFIG.ENCODING_PRESETS.medium;

    // Return a copy with validated CRF value
    return {
      ...preset,
      crf: this.validateCrf(preset.crf),
    };
  }

  /**
   * Generates a timestamp string for unique filenames
   * @returns Formatted timestamp string
   */
  static generateTimestamp(): string {
    return new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .replace("T", "_")
      .replace("Z", "");
  }

  /**
   * Escapes a file path for use in shell commands
   * @param path - File path to escape
   * @returns Escaped file path
   */
  static escapeFilePath(path: string): string {
    return path.replace(/"/g, '\\"');
  }

  /**
   * Optimizes a filter chain by grouping similar operations
   * @param filters - Array of filter commands
   * @returns Optimized array of filter commands
   */
  static optimizeFilterChain(filters: string[]): string[] {
    // Group filters by type for more efficient processing
    const grouped: { [key: string]: string[] } = {
      scale: [], // Scaling operations
      rotate: [], // Rotation operations
      colorspace: [], // Format and colorspace operations
      effects: [], // Visual effects
      overlay: [], // Overlay operations
    };

    // Sort filters into groups
    filters.forEach((filter) => {
      if (filter.includes("scale=")) {
        grouped.scale.push(filter);
      } else if (filter.includes("rotate=")) {
        grouped.rotate.push(filter);
      } else if (filter.includes("format=") || filter.includes("colorspace=")) {
        grouped.colorspace.push(filter);
      } else if (filter.includes("overlay=")) {
        grouped.overlay.push(filter);
      } else {
        grouped.effects.push(filter);
      }
    });

    // Return filters in optimized order
    return [
      ...grouped.scale,
      ...grouped.rotate,
      ...grouped.colorspace,
      ...grouped.effects,
      ...grouped.overlay,
    ];
  }

  /**
   * Creates a temporary ASS file from captions with custom styling
   * @param captions Array of captions
   * @param globalCaptionStyle Global caption style configuration
   * @param canvasWidth Canvas width for responsive sizing
   * @param canvasHeight Canvas height for responsive sizing
   * @returns Path to the created ASS file
   */
  static createStyledSubtitleFile(
    captions: Caption[],
    globalCaptionStyle?: import("./types").CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!captions || captions.length === 0) {
      return "";
    }

    // 使用ASSSubtitleUtils创建字幕文件
    const subtitlePath = ASSSubtitleUtils.createASSFile(
      captions,
      globalCaptionStyle,
      canvasWidth,
      canvasHeight
    );

    return subtitlePath;
  }

  /**
   * Creates a temporary ASS file from captions
   * @param captions Array of captions
   * @returns Path to the created ASS file
   */
  static createSubtitleFile(captions: Caption[]): string {
    if (!captions || captions.length === 0) {
      return "";
    }

    // Create ASS header
    let assContent = `[Script Info]
Title: Generated Subtitles
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: None

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,20,&Hffffff,&Hffffff,&H0,&H0,0,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`;

    // Convert captions to ASS format
    captions.forEach((caption) => {
      // Convert HH:MM:SS format to H:MM:SS.CC format required by ASS
      const convertTimeToASS = (timeStr: string): string => {
        const parts = timeStr.split(":");
        if (parts.length === 3) {
          const hours = parseInt(parts[0], 10);
          const minutes = parts[1];
          const seconds = parts[2];
          return `${hours}:${minutes}:${seconds}.00`;
        }
        return timeStr + ".00";
      };

      const startTime = convertTimeToASS(caption.startTime);
      const endTime = convertTimeToASS(caption.endTime);

      // Escape text for ASS format
      const escapedText = caption.text
        .replace(/\\/g, "\\\\")
        .replace(/\{/g, "\\{")
        .replace(/\}/g, "\\}")
        .replace(/\n/g, "\\N");

      assContent += `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,${escapedText}\n`;
    });

    // Create a temporary file
    const tempDir = os.tmpdir();
    const subtitlePath = path.join(
      tempDir,
      `subtitles_${this.generateTimestamp()}.ass`
    );
    console.log("Generated ASS content:", assContent);
    fs.writeFileSync(subtitlePath, assContent, "utf8");

    return subtitlePath;
  }

  /**
   * Generate subtitle filter command for ASS files
   * @param subtitlePath Path to the ASS file
   * @param canvasWidth Canvas width
   * @param canvasHeight Canvas height
   * @param globalCaptionStyle Global caption style configuration (样式已在ASS文件中设置)
   * @returns FFmpeg subtitle filter string
   */
  static generateSubtitleFilter(
    subtitlePath: string,
    canvasWidth: number,
    canvasHeight: number,
    globalCaptionStyle?: import("./types").CaptionStyle
  ): string {
    if (!subtitlePath) {
      return "";
    }

    // 对于ASS文件，直接使用ass过滤器
    // 样式信息已经在ASS文件中定义，无需使用force_style参数
    return `ass='${this.escapeFilePath(subtitlePath)}'`;
  }

  /**
   * Map frontend font names to FFmpeg-compatible font names
   * @param fontFamily Frontend font family name
   * @returns FFmpeg-compatible font name
   */
  static mapFontFamily(fontFamily: string): string {
    const fontMap: Record<string, string> = {
      Arial: "Arial",
      Helvetica: "Helvetica",
      "Times New Roman": "Times New Roman",
      Georgia: "Georgia",
      Verdana: "Verdana",
      "Courier New": "Courier New",
      Impact: "Impact",
      "Comic Sans MS": "Comic Sans MS",
      "Trebuchet MS": "Trebuchet MS",
      "Arial Black": "Arial Black",
      Palatino: "Palatino",
      Garamond: "Garamond",
      Bookman: "Bookman",
      "Avant Garde": "Avant Garde",
      Roboto: "Roboto",
      "Open Sans": "Open Sans",
      Lato: "Lato",
      Montserrat: "Montserrat",
      "Source Sans Pro": "Source Sans Pro",
      Oswald: "Oswald",
      Raleway: "Raleway",
      "PT Sans": "PT Sans",
      Ubuntu: "Ubuntu",
      Merriweather: "Merriweather",
      "Playfair Display": "Playfair Display",
    };

    return fontMap[fontFamily] || "Arial"; // 默认使用Arial
  }
}
