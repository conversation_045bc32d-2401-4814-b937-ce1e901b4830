import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { FFMPEG_CONFIG } from "../config";

/**
 * 字体管理器 - 负责处理字体文件的查找、验证和选择
 */
export class FontManager {
  private static instance: FontManager;
  private fontCache: Map<string, string> = new Map();

  private constructor() {}

  public static getInstance(): FontManager {
    if (!FontManager.instance) {
      FontManager.instance = new FontManager();
    }
    return FontManager.instance;
  }

  /**
   * 检查文本是否包含中文字符
   */
  public containsChineseCharacters(text: string): boolean {
    return /[\u4e00-\u9fff]/.test(text);
  }

  /**
   * 检查文本是否包含日文字符
   */
  public containsJapaneseCharacters(text: string): boolean {
    return /[\u3040-\u309f\u30a0-\u30ff]/.test(text);
  }

  /**
   * 检查文本是否包含韩文字符
   */
  public containsKoreanCharacters(text: string): boolean {
    return /[\uac00-\ud7af]/.test(text);
  }

  /**
   * 检查文本是否包含CJK字符（中日韩）
   */
  public containsCJKCharacters(text: string): boolean {
    return (
      this.containsChineseCharacters(text) ||
      this.containsJapaneseCharacters(text) ||
      this.containsKoreanCharacters(text)
    );
  }

  /**
   * 验证字体文件是否存在
   */
  public validateFontFile(fontPath: string): boolean {
    try {
      return fs.existsSync(fontPath);
    } catch (error) {
      console.error(`验证字体文件失败: ${fontPath}`, error);
      return false;
    }
  }

  /**
   * 获取最佳字体路径
   */
  public getBestFontPath(
    text: string,
    fontFamily: string = "Arial",
    styles: string[] = [],
    customFontDir?: string
  ): string {
    const cacheKey = `${text}-${fontFamily}-${styles.join(",")}-${
      customFontDir || ""
    }`;

    // 检查缓存
    if (this.fontCache.has(cacheKey)) {
      return this.fontCache.get(cacheKey)!;
    }

    let bestFont: string;

    // 检查是否需要CJK字体支持
    const needsCJK = this.containsCJKCharacters(text);

    if (needsCJK) {
      bestFont = this.getCJKFontPath(fontFamily, styles, customFontDir);
    } else {
      bestFont = this.getRegularFontPath(fontFamily, styles, customFontDir);
    }

    // 缓存结果
    this.fontCache.set(cacheKey, bestFont);

    console.log(`为文本 "${text.substring(0, 20)}..." 选择字体: ${bestFont}`);
    return bestFont;
  }

  /**
   * 获取CJK字体路径
   */
  private getCJKFontPath(
    fontFamily: string,
    styles: string[] = [],
    customFontDir?: string
  ): string {
    const fontDir = customFontDir || process.env.FONT_DIR || "./assets/fonts";

    // CJK字体映射表
    const cjkFontMap: { [key: string]: string[] } = {
      Arial: [
        "PingFang.ttc",
        "SimHei.ttf",
        "Microsoft YaHei.ttf",
        "NotoSansCJK.ttc",
      ],
      Helvetica: ["PingFang.ttc", "SimHei.ttf", "Microsoft YaHei.ttf"],
      Times: ["STSong.ttf", "SimSun.ttf", "NotoSerifCJK.ttc"],
      Courier: ["STKaiti.ttf", "KaiTi.ttf", "NotoSansMonoCJK.ttc"],
      default: [
        "PingFang.ttc",
        "SimHei.ttf",
        "Microsoft YaHei.ttf",
        "STSong.ttf",
      ],
    };

    const candidateFonts = cjkFontMap[fontFamily] || cjkFontMap["default"];

    // 1. 尝试自定义字体目录
    for (const fontFile of candidateFonts) {
      const fontPath = path.join(fontDir, fontFile);
      if (this.validateFontFile(fontPath)) {
        console.log(`找到CJK字体: ${fontPath}`);
        return fontPath;
      }
    }

    // 2. 尝试系统CJK字体
    const systemCJKFonts = this.getSystemCJKFonts();
    for (const systemFont of systemCJKFonts) {
      if (this.validateFontFile(systemFont)) {
        console.log(`使用系统CJK字体: ${systemFont}`);
        return systemFont;
      }
    }

    // 3. 回退到配置的中文字体
    if (FFMPEG_CONFIG.CHINESE_FONT?.fallbackFonts) {
      for (const fallbackFont of FFMPEG_CONFIG.CHINESE_FONT.fallbackFonts) {
        if (this.validateFontFile(fallbackFont)) {
          console.log(`使用回退CJK字体: ${fallbackFont}`);
          return fallbackFont;
        }
      }
    }

    // 4. 最后回退到系统默认字体
    console.warn("未找到合适的CJK字体，使用系统默认字体");
    return this.getSystemDefaultFont(true);
  }

  /**
   * 获取常规字体路径
   */
  private getRegularFontPath(
    fontFamily: string,
    styles: string[] = [],
    customFontDir?: string
  ): string {
    const fontDir = customFontDir || process.env.FONT_DIR || "./assets/fonts";

    // 尝试多种字体文件命名方式
    const fontVariants = this.generateFontVariants(fontFamily, styles);

    // 首先尝试自定义字体目录
    for (const variant of fontVariants) {
      const fontPath = path.join(fontDir, variant);
      if (this.validateFontFile(fontPath)) {
        console.log(`找到字体文件: ${fontPath}`);
        return fontPath;
      }
    }

    // 尝试系统字体目录
    const systemFontPaths = this.getSystemFontPaths(fontFamily, styles);
    for (const systemPath of systemFontPaths) {
      if (this.validateFontFile(systemPath)) {
        console.log(`使用系统字体: ${systemPath}`);
        return systemPath;
      }
    }

    // 回退到系统默认字体
    console.warn(
      `未找到字体 ${fontFamily} (样式: ${styles.join(", ")})，回退到系统字体`
    );
    return this.getSystemDefaultFont(false);
  }

  /**
   * 生成字体文件名变体
   */
  private generateFontVariants(fontFamily: string, styles: string[]): string[] {
    const variants: string[] = [];
    const isBold = styles.includes("bold");
    const isItalic = styles.includes("italic");

    // 标准命名方式
    if (isBold && isItalic) {
      variants.push(`${fontFamily} Bold Italic.ttf`);
      variants.push(`${fontFamily}-BoldItalic.ttf`);
      variants.push(`${fontFamily}BoldItalic.ttf`);
    } else if (isBold) {
      variants.push(`${fontFamily} Bold.ttf`);
      variants.push(`${fontFamily}-Bold.ttf`);
      variants.push(`${fontFamily}Bold.ttf`);
    } else if (isItalic) {
      variants.push(`${fontFamily} Italic.ttf`);
      variants.push(`${fontFamily}-Italic.ttf`);
      variants.push(`${fontFamily}Italic.ttf`);
    } else {
      variants.push(`${fontFamily}.ttf`);
      variants.push(`${fontFamily} Regular.ttf`);
      variants.push(`${fontFamily}-Regular.ttf`);
    }

    // 添加其他格式
    const extensions = [".ttf", ".otf", ".ttc"];
    const baseVariants = [...variants];

    for (const variant of baseVariants) {
      for (const ext of extensions) {
        if (!variant.endsWith(ext)) {
          variants.push(variant.replace(/\.(ttf|otf|ttc)$/, ext));
        }
      }
    }

    return variants;
  }

  /**
   * 获取系统字体路径
   */
  private getSystemFontPaths(fontFamily: string, styles: string[]): string[] {
    const platform = os.platform();
    const paths: string[] = [];
    const isBold = styles.includes("bold");
    const isItalic = styles.includes("italic");

    switch (platform) {
      case "darwin": // macOS
        const macFontDirs = ["/System/Library/Fonts/", "/Library/Fonts/"];
        for (const dir of macFontDirs) {
          const variants = this.generateFontVariants(fontFamily, styles);
          for (const variant of variants) {
            paths.push(path.join(dir, variant));
          }
        }
        break;

      case "win32": // Windows
        const windir = process.env.WINDIR || "C:\\Windows";
        const winFontDir = path.join(windir, "Fonts");
        const variants = this.generateFontVariants(fontFamily, styles);
        for (const variant of variants) {
          paths.push(path.join(winFontDir, variant));
        }
        break;

      default: // Linux
        const linuxFontDirs = [
          "/usr/share/fonts/truetype/",
          "/usr/local/share/fonts/",
          "/home/" + (process.env.USER || "user") + "/.fonts/",
        ];
        for (const dir of linuxFontDirs) {
          const variants = this.generateFontVariants(fontFamily, styles);
          for (const variant of variants) {
            paths.push(path.join(dir, variant));
          }
        }
        break;
    }

    return paths;
  }

  /**
   * 获取系统CJK字体列表
   */
  private getSystemCJKFonts(): string[] {
    const platform = os.platform();
    const fonts: string[] = [];

    switch (platform) {
      case "darwin": // macOS
        fonts.push(
          "/System/Library/Fonts/PingFang.ttc",
          "/System/Library/Fonts/STHeiti Light.ttc",
          "/System/Library/Fonts/STHeiti Medium.ttc",
          "/Library/Fonts/Arial Unicode MS.ttf",
          "/System/Library/Fonts/Hiragino Sans GB.ttc"
        );
        break;
      case "win32": // Windows
        const windir = process.env.WINDIR || "C:\\Windows";
        fonts.push(
          path.join(windir, "Fonts", "simhei.ttf"),
          path.join(windir, "Fonts", "simsun.ttc"),
          path.join(windir, "Fonts", "msyh.ttc"),
          path.join(windir, "Fonts", "msyhbd.ttc"),
          path.join(windir, "Fonts", "malgun.ttf")
        );
        break;
      default: // Linux
        fonts.push(
          "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
          "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
          "/usr/share/fonts/truetype/arphic/ukai.ttc",
          "/usr/share/fonts/truetype/arphic/uming.ttc",
          "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc"
        );
        break;
    }

    return fonts.filter((font) => this.validateFontFile(font));
  }

  /**
   * 获取系统默认字体
   */
  private getSystemDefaultFont(needsCJK: boolean = false): string {
    const platform = os.platform();

    if (needsCJK) {
      // 返回支持CJK的系统字体
      switch (platform) {
        case "darwin":
          return "/System/Library/Fonts/PingFang.ttc";
        case "win32":
          return path.join(
            process.env.WINDIR || "C:\\Windows",
            "Fonts",
            "simhei.ttf"
          );
        default:
          return "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc";
      }
    } else {
      // 返回默认英文字体
      switch (platform) {
        case "darwin":
          return "/System/Library/Fonts/Arial.ttf";
        case "win32":
          return path.join(
            process.env.WINDIR || "C:\\Windows",
            "Fonts",
            "arial.ttf"
          );
        default:
          return "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf";
      }
    }
  }

  /**
   * 清除字体缓存
   */
  public clearCache(): void {
    this.fontCache.clear();
    console.log("字体缓存已清除");
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.fontCache.size,
      keys: Array.from(this.fontCache.keys()),
    };
  }
}
