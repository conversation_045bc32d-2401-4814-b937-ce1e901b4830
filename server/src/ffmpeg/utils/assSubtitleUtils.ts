import { Caption, CaptionStyle } from "../types";
import { ASS_CONFIG } from "../config";
import {
  BaseASSUtils,
  COMMON_ASS_CONSTANTS,
  ASS_ALIGNMENT_MAP,
} from "./baseASSUtils";

/**
 * ASS字幕工具类
 * 提供高级的ASS字幕生成和处理功能
 */
export class ASSSubtitleUtils {
  /**
   * 转换颜色格式：从 #RRGGBB 到 &HBBGGRR& (ASS格式)
   */
  static convertColorToASS(hexColor: string): string {
    return BaseASSUtils.convertColorToASS(hexColor);
  }

  /**
   * 转换对齐方式到ASS格式
   */
  static getASSAlignment(textAlign: string): number {
    return BaseASSUtils.getASSAlignment(textAlign);
  }

  /**
   * 转换时间格式到ASS格式
   */
  static convertTimeToASS(timeStr: string): string {
    return BaseASSUtils.convertTimeToASS(timeStr);
  }

  /**
   * 转义ASS文本
   */
  static escapeASSText(text: string): string {
    return BaseASSUtils.escapeASSText(text);
  }

  /**
   * 计算边距
   */
  private static calculateMargins(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): { sideMargin: number; bottomMargin: number } {
    // 计算底部边距
    let bottomMargin = canvasHeight
      ? Math.round(canvasHeight * ASS_CONFIG.DEFAULT_MARGIN_RATIO)
      : 10;

    if (style.positionY !== undefined && canvasHeight) {
      bottomMargin = Math.max(0, bottomMargin - style.positionY);
    }

    // 计算侧边距
    let sideMargin = canvasWidth
      ? Math.round(canvasWidth * ASS_CONFIG.DEFAULT_MARGIN_RATIO)
      : 10;

    if (canvasWidth) {
      const effectiveFrontendTextAreaWidth = Math.max(
        0,
        canvasWidth * COMMON_ASS_CONSTANTS.FRONTEND_TEXTBOX_WIDTH_RATIO -
          2 * COMMON_ASS_CONSTANTS.FRONTEND_TEXTBOX_PADDING
      );

      sideMargin = Math.round(
        (canvasWidth - effectiveFrontendTextAreaWidth) / 2
      );
    }

    return { sideMargin, bottomMargin };
  }

  /**
   * 生成ASS样式字符串 - 支持scale缩放
   */
  static generateASSStyle(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    const isBold = style.styles.includes("bold") || style.fontWeight >= 700;
    const isItalic = style.styles.includes("italic");
    const hasUnderline =
      style.styles.includes("underline") || style.styles.includes("underlined");
    const isStrikethrough =
      style.styles.includes("strikethrough") ||
      style.styles.includes("strikeOut");

    // 使用 -1 来实现连续下划线效果，这比普通下划线(1)效果更好
    const underlineValue = hasUnderline ? -1 : 0;

    const { sideMargin, bottomMargin } = this.calculateMargins(
      style,
      canvasWidth,
      canvasHeight
    );

    // 应用scale到字体大小 - 与text元素保持一致
    const baseFontSize = style.fontSize || ASS_CONFIG.DEFAULT_STYLE.fontSize;
    const scaleY = style.scaleY || 1;
    const scaledFontSize = Math.round(baseFontSize * scaleY);

    // 应用scale到描边宽度 - 与text元素保持一致
    const baseStrokeWidth = style.strokeWidth || 0;
    const scaleX = style.scaleX || 1;
    const scaledStrokeWidth = Math.max(0, Math.round(baseStrokeWidth * scaleX));

    console.log(
      `字幕scale应用: 字体大小 ${baseFontSize} -> ${scaledFontSize} (scaleY: ${scaleY}), 描边宽度 ${baseStrokeWidth} -> ${scaledStrokeWidth} (scaleX: ${scaleX})`
    );

    const styleComponents = [
      style.fontFamily || ASS_CONFIG.DEFAULT_STYLE.fontFamily,
      scaledFontSize, // 使用缩放后的字体大小
      BaseASSUtils.convertColorToASS(style.fontColor),
      BaseASSUtils.convertColorToASS(style.fontColor),
      BaseASSUtils.convertColorToASS(style.strokeColor),
      BaseASSUtils.convertColorToASS(style.backgroundColor),
      isBold ? 1 : 0,
      isItalic ? 1 : 0,
      underlineValue, // 使用 -1 实现连续下划线效果
      isStrikethrough ? 1 : 0, // 修复：正确处理删除线
      COMMON_ASS_CONSTANTS.SCALE_VALUES.X,
      COMMON_ASS_CONSTANTS.SCALE_VALUES.Y,
      Math.round(
        (style.charSpacing || 0) * COMMON_ASS_CONSTANTS.CHAR_SPACING_RATIO
      ),
      0, // Angle
      COMMON_ASS_CONSTANTS.BORDER_STYLE,
      scaledStrokeWidth, // 使用缩放后的描边宽度
      0, // Shadow - 设置为0避免文字模糊，阴影效果通过标签实现
      5, // 使用中心对齐作为默认值，实际对齐通过位置标签控制
      sideMargin, // MarginL
      sideMargin, // MarginR
      bottomMargin, // MarginV
      COMMON_ASS_CONSTANTS.ENCODING,
    ];

    console.log(
      `ASS样式生成 - 下划线参数: ${underlineValue} (${
        hasUnderline ? "启用连续下划线" : "无下划线"
      })`
    );

    return styleComponents.join(",");
  }

  /**
   * 获取ASS对齐值 - 改为左上角对齐系统
   */
  private static getASSAlignmentValue(
    originX?: string,
    originY?: string
  ): number {
    // 改为默认使用左上角对齐
    const vertical = originY || "top";
    const horizontal = originX || "left";
    const key = `${vertical}-${horizontal}` as keyof typeof ASS_ALIGNMENT_MAP;

    return ASS_ALIGNMENT_MAP[key] || ASS_ALIGNMENT_MAP["top-left"];
  }

  /**
   * 计算背景框尺寸 - 支持scale缩放和多行文本
   */
  private static calculateBackgroundDimensions(
    style: CaptionStyle,
    text?: string
  ): {
    backgroundWidth: number;
    backgroundHeight: number;
  } {
    // 获取缩放因子
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;

    // 计算基础宽度
    const baseWidth = style.width || 400; // 默认宽度

    // 计算基础高度 - 与前端逻辑保持一致
    const baseFontSize = style.fontSize || 35;
    const lineHeight = style.lineHeight || 1.2;

    // 计算行数 - 如果有文本则使用实际行数，否则使用默认值1
    const lineCount = text ? text.split("\n").length : 1;

    // 与前端calculateCaptionBackgroundHeight保持一致的计算逻辑
    const textHeight = baseFontSize * lineHeight * lineCount;
    const padding = 20; // 上下内边距，与前端保持一致
    const baseHeight = textHeight + padding;

    // 应用缩放因子
    const backgroundWidth = baseWidth * scaleX;
    const backgroundHeight = baseHeight * scaleY;

    console.log(
      `字幕背景框尺寸计算: 文本行数(${lineCount}) 基础(${baseWidth}x${baseHeight}) -> 缩放后(${backgroundWidth}x${backgroundHeight}) [scale: ${scaleX}x${scaleY}]`
    );

    return { backgroundWidth, backgroundHeight };
  }

  /**
   * 计算位置坐标 - 改为左上角坐标系统
   * 注意：这里计算的是背景框左上角的坐标，与前端保持一致
   */
  private static calculatePosition(
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number,
    text?: string
  ): { xPos: number; yPos: number } {
    // 使用新的背景框尺寸计算方法（包含scale和多行文本）
    const { backgroundWidth, backgroundHeight } =
      this.calculateBackgroundDimensions(style, text);

    // 计算水平位置 - 以背景框左上角为基准
    let xPos = 0;
    switch (style.originX) {
      case "left":
        xPos = 0 + (style.positionX || 0);
        break;
      case "right":
        xPos = canvasWidth - backgroundWidth + (style.positionX || 0);
        break;
      case "center":
      default:
        xPos = (canvasWidth - backgroundWidth) / 2 + (style.positionX || 0);
        break;
    }

    // 计算垂直位置 - 以背景框左上角为基准
    let yPos = 0;
    const defaultBottomMarginRatio = ASS_CONFIG.DEFAULT_MARGIN_RATIO;

    switch (style.originY) {
      case "top":
        yPos = 0 + (style.positionY || 0);
        break;
      case "center":
        yPos = (canvasHeight - backgroundHeight) / 2 + (style.positionY || 0);
        break;
      case "bottom":
      default:
        const bottomMargin = Math.round(
          canvasHeight * defaultBottomMarginRatio
        );
        yPos =
          canvasHeight -
          bottomMargin -
          backgroundHeight +
          (style.positionY || 0);
        break;
    }

    return { xPos: Math.round(xPos), yPos: Math.round(yPos) };
  }

  /**
   * 生成位置标签 - 支持背景框内文本对齐和scale缩放
   */
  private static generatePositionTags(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number,
    text?: string
  ): string {
    if (!canvasWidth || !canvasHeight) {
      return "";
    }

    // 使用新的背景框尺寸计算方法（包含scale和多行文本）
    const { backgroundWidth, backgroundHeight } =
      this.calculateBackgroundDimensions(style, text);

    // 计算背景框位置（左上角坐标）
    const { xPos: backgroundX, yPos: backgroundY } = this.calculatePosition(
      style,
      canvasWidth,
      canvasHeight,
      text
    );

    // 计算文本在背景框内的位置
    const { textX, textY } = this.calculateTextPositionInBackground(
      backgroundX,
      backgroundY,
      backgroundWidth,
      backgroundHeight,
      style.textAlign || "center"
    );

    // 根据textAlign确定ASS对齐方式
    let alignmentValue = 4; // 默认为左中对齐
    switch (style.textAlign) {
      case "left":
        alignmentValue = 4; // 左中对齐
        break;
      case "right":
        alignmentValue = 6; // 右中对齐
        break;
      case "center":
      default:
        alignmentValue = 5; // 中心对齐
        break;
    }

    console.log(
      `字幕位置标签生成: 背景框(${backgroundX},${backgroundY},${backgroundWidth}x${backgroundHeight}) -> 文本位置(${textX},${textY}) [对齐: \\an${alignmentValue}]`
    );

    return `{\\\\an${alignmentValue}\\\\pos(${Math.round(textX)},${Math.round(
      textY
    )})}`;
  }

  /**
   * 计算文本在背景框内的位置
   */
  private static calculateTextPositionInBackground(
    backgroundX: number,
    backgroundY: number,
    backgroundWidth: number,
    backgroundHeight: number,
    textAlign: "left" | "center" | "right"
  ): { textX: number; textY: number } {
    // 垂直居中
    const textY = backgroundY + backgroundHeight / 2;

    // 文本边距
    const textMarginX = 10;

    // 水平位置根据对齐方式确定
    let textX: number;
    switch (textAlign) {
      case "left":
        textX = backgroundX + textMarginX;
        break;
      case "right":
        textX = backgroundX + backgroundWidth - textMarginX;
        break;
      default: // center
        textX = backgroundX + backgroundWidth / 2;
        break;
    }

    return { textX, textY };
  }

  /**
   * 生成渐变标签
   */
  private static generateGradientTags(style: CaptionStyle): string {
    if (
      !style.useGradient ||
      !style.gradientColors ||
      style.gradientColors.length < 2
    ) {
      return "";
    }

    const color1 = this.convertColorToASS(style.gradientColors[0]);
    const color2 = this.convertColorToASS(style.gradientColors[1]);
    return `{\\1c${color1}\\3c${color2}}`;
  }

  /**
   * 生成字符间距标签 - 支持scale缩放
   */
  private static generateCharSpacingTags(style: CaptionStyle): string {
    if (!style.charSpacing || style.charSpacing === 0) {
      return "";
    }

    // 应用scale到字符间距
    const scaleX = style.scaleX || 1;
    const scaledCharSpacing = style.charSpacing * scaleX;
    const assCharSpacing = Math.round(
      scaledCharSpacing * COMMON_ASS_CONSTANTS.CHAR_SPACING_RATIO
    );

    console.log(
      `字符间距scale应用: 原始(${style.charSpacing}) -> 缩放后(${scaledCharSpacing}) -> ASS值(${assCharSpacing}) [scaleX: ${scaleX}]`
    );

    return `{\\fsp${assCharSpacing}}`;
  }

  /**
   * 生成阴影标签
   */
  private static generateShadowTags(style: CaptionStyle): string {
    const scaleX = (style as any).scaleX || 1;
    const scaleY = (style as any).scaleY || 1;
    return BaseASSUtils.generateShadowTags(style, scaleX, scaleY);
  }

  /**
   * 处理单个字幕文本
   */
  private static processSubtitleText(
    caption: Caption,
    finalStyle: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    let escapedText = this.escapeASSText(caption.text);

    // 按优先级添加各种标签
    const positionTags = this.generatePositionTags(
      finalStyle,
      canvasWidth,
      canvasHeight,
      caption.text
    );
    const gradientTags = this.generateGradientTags(finalStyle);
    const charSpacingTags = this.generateCharSpacingTags(finalStyle);
    const shadowTags = this.generateShadowTags(finalStyle);

    // 组合所有标签
    return (
      positionTags + gradientTags + charSpacingTags + shadowTags + escapedText
    );
  }

  /**
   * 生成ASS文件头部
   */
  private static generateASSHeader(
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    return BaseASSUtils.generateASSHeader(
      canvasWidth || 1920,
      canvasHeight || 1080,
      "Generated Subtitles"
    );
  }

  /**
   * 判断是否应该渲染背景
   */
  private static shouldRenderBackground(style: CaptionStyle): boolean {
    return !!(style.backgroundColor && style.backgroundColor !== "transparent");
  }

  /**
   * 生成背景矩形
   * 使用ASS的绘图功能(\p标签)绘制圆角矩形背景
   */
  private static generateBackgroundRectangle(
    backgroundX: number,
    backgroundY: number,
    backgroundWidth: number,
    backgroundHeight: number,
    style: CaptionStyle
  ): string {
    const bgColor = BaseASSUtils.convertColorToASS(style.backgroundColor);

    // 获取缩放因子
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;

    // 生成圆角矩形路径，考虑缩放因子
    const roundedRectPath = this.generateRoundedRectanglePath(
      backgroundWidth,
      backgroundHeight,
      COMMON_ASS_CONSTANTS.DEFAULT_BORDER_RADIUS,
      scaleX,
      scaleY
    );

    // 使用an7对齐方式（左上角对齐）和pos定位到背景框位置
    return `{\\an7\\pos(${backgroundX},${backgroundY})\\1c${bgColor}\\3c${bgColor}\\p1}${roundedRectPath}{\\p0}`;
  }

  /**
   * 生成圆角矩形路径
   * 使用ASS的绘图功能(\p标签)绘制圆角矩形背景
   */
  private static generateRoundedRectanglePath(
    width: number,
    height: number,
    radius: number = COMMON_ASS_CONSTANTS.DEFAULT_BORDER_RADIUS,
    scaleX: number = 1,
    scaleY: number = 1
  ): string {
    // 根据缩放因子调整圆角半径
    // 使用较小的缩放值来保持圆角比例的协调性
    const scale = Math.min(scaleX, scaleY);
    const adjustedRadius = radius * scale;

    // 确保圆角半径不超过矩形的一半
    const r = Math.min(adjustedRadius, Math.min(width, height) / 2);

    console.log(
      `字幕圆角半径缩放: 原始(${radius}) -> 缩放后(${adjustedRadius}) -> 最终(${r}) [缩放:${scaleX}x${scaleY}, 使用:${scale}x]`
    );

    if (r <= 0) {
      // 如果圆角半径为0或负数，绘制普通矩形
      return `m 0 0 l ${width} 0 l ${width} ${height} l 0 ${height}`;
    }

    // 生成圆角矩形路径
    return [
      `m ${r} 0`,
      `l ${width - r} 0`,
      `b ${width - r} 0 ${width} 0 ${width} ${r}`,
      `l ${width} ${height - r}`,
      `b ${width} ${height - r} ${width} ${height} ${width - r} ${height}`,
      `l ${r} ${height}`,
      `b ${r} ${height} 0 ${height} 0 ${height - r}`,
      `l 0 ${r}`,
      `b 0 ${r} 0 0 ${r} 0`,
    ].join(" ");
  }

  /**
   * 生成完整的ASS文件内容
   */
  static generateASSContent(
    captions: Caption[],
    style?: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!captions?.length) {
      throw new Error("字幕数据不能为空");
    }

    const defaultStyle: CaptionStyle = {
      ...ASS_CONFIG.DEFAULT_STYLE,
      styles: [...ASS_CONFIG.DEFAULT_STYLE.styles],
      gradientColors: [...ASS_CONFIG.DEFAULT_STYLE.gradientColors],
      fontSize: canvasHeight
        ? Math.max(
            ASS_CONFIG.MIN_FONT_SIZE,
            Math.round(canvasHeight * ASS_CONFIG.DEFAULT_FONT_SIZE_RATIO)
          )
        : ASS_CONFIG.DEFAULT_STYLE.fontSize,
    };

    const finalStyle: CaptionStyle = style
      ? { ...defaultStyle, ...style }
      : defaultStyle;

    // 生成ASS文件头部
    let assContent = this.generateASSHeader(canvasWidth, canvasHeight);

    // 添加样式定义
    assContent += `Style: Default,${this.generateASSStyle(
      finalStyle,
      canvasWidth,
      canvasHeight
    )}\n\n`;

    // 添加事件格式
    assContent += `[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n`;

    // 处理每个字幕
    captions.forEach((caption) => {
      try {
        const startTime = this.convertTimeToASS(caption.startTime);
        const endTime = this.convertTimeToASS(caption.endTime);

        // 如果需要背景框，先添加背景层
        if (this.shouldRenderBackground(finalStyle)) {
          const backgroundDialogue = this.generateBackgroundDialogue(
            caption,
            finalStyle,
            startTime,
            endTime,
            canvasWidth,
            canvasHeight
          );
          assContent += backgroundDialogue;
        }

        // 添加文字层
        const processedText = this.processSubtitleText(
          caption,
          finalStyle,
          canvasWidth,
          canvasHeight
        );

        assContent += `Dialogue: ${COMMON_ASS_CONSTANTS.TEXT_LAYER},${startTime},${endTime},Default,,0,0,0,,${processedText}\n`;
      } catch (error) {
        console.warn(`处理字幕时出错: ${caption.text}`, error);
      }
    });

    return assContent;
  }

  /**
   * 生成背景对话行
   */
  private static generateBackgroundDialogue(
    caption: Caption,
    style: CaptionStyle,
    startTime: string,
    endTime: string,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!canvasWidth || !canvasHeight) {
      return "";
    }

    // 计算背景框尺寸和位置 - 传入字幕文本以正确计算多行高度
    const { backgroundWidth, backgroundHeight } =
      this.calculateBackgroundDimensions(style, caption.text);
    const { xPos: backgroundX, yPos: backgroundY } = this.calculatePosition(
      style,
      canvasWidth,
      canvasHeight,
      caption.text
    );

    // 生成背景矩形
    const backgroundRect = this.generateBackgroundRectangle(
      backgroundX,
      backgroundY,
      backgroundWidth,
      backgroundHeight,
      style
    );

    return `Dialogue: ${COMMON_ASS_CONSTANTS.BACKGROUND_LAYER},${startTime},${endTime},Default,,0,0,0,,${backgroundRect}\n`;
  }

  /**
   * 创建ASS字幕文件
   */
  static createASSFile(
    captions: Caption[],
    style?: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    try {
      const assContent = this.generateASSContent(
        captions,
        style,
        canvasWidth,
        canvasHeight
      );

      const subtitlePath = BaseASSUtils.createTempFile(
        "ass_subtitles",
        assContent
      );
      console.log("Generated ASS subtitle file:", subtitlePath);

      return subtitlePath;
    } catch (error) {
      console.error("创建ASS文件失败:", error);
      throw new Error(
        `创建ASS文件失败: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * 验证ASS文件格式
   */
  static validateASSFile(filePath: string): boolean {
    return BaseASSUtils.validateASSFile(filePath);
  }

  /**
   * 获取ASS文件的字幕数量
   */
  static getSubtitleCount(filePath: string): number {
    return BaseASSUtils.getSubtitleCount(filePath);
  }

  /**
   * 清理临时文件
   */
  static cleanupTempFile(filePath: string): void {
    BaseASSUtils.cleanupTempFile(filePath);
  }
}
