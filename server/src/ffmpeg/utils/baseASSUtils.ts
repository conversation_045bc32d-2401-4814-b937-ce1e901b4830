import { Caption, CaptionStyle } from "../types";
import { ASS_CONFIG } from "../config";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

/**
 * 公共ASS配置常量
 * 统一管理所有ASS相关的常量配置
 */
export const COMMON_ASS_CONSTANTS = {
  // 转换比例
  CHAR_SPACING_RATIO: 0.5,
  SHADOW_DISTANCE_RATIO: 0.5,
  BLUR_RATIO: 0.33,
  FRONTEND_TEXTBOX_WIDTH_RATIO: 0.9,
  FRONTEND_TEXTBOX_PADDING: 10,
  DEFAULT_ALPHA_VALUE: 160,
  SCALE_VALUES: { X: 100, Y: 100 },
  BORDER_STYLE: 1,
  ENCODING: 1,

  // DPI适配 - 禁用以确保前后端阴影效果一致
  DPI_SCALE_FACTOR: 2, // 默认2x DPI适配，匹配大多数现代设备
  ENABLE_DPI_SCALING: false, // 禁用DPI适配以确保前端Canvas和后端ASS阴影效果一致

  // 字体设置
  MIN_FONT_SIZE: 8,
  DEFAULT_FONT_SIZE: 24,
  DEFAULT_LINE_HEIGHT: 1.2,

  // 间距设置
  LINE_SPACING_MULTIPLIER: 0.2,
  SHADOW_ALPHA: 160,

  // 圆角设置
  DEFAULT_BORDER_RADIUS: 10,

  // 图层
  BACKGROUND_LAYER: 0,
  TEXT_LAYER: 1,

  // 对齐值映射
  ALIGNMENT_MAP: {
    left: 4,
    center: 5,
    right: 6,
  } as const,
} as const;

/**
 * ASS对齐映射
 */
export const ASS_ALIGNMENT_MAP = {
  "top-left": 7,
  "top-center": 8,
  "top-right": 9,
  "center-left": 4,
  "center-center": 5,
  "center-right": 6,
  "bottom-left": 1,
  "bottom-center": 2,
  "bottom-right": 3,
} as const;

/**
 * 基础ASS工具类
 * 提供所有ASS字幕处理的公共功能
 */
export class BaseASSUtils {
  /**
   * 转换颜色格式：从 #RRGGBB 到 &HBBGGRR& (ASS格式)
   */
  static convertColorToASS(hexColor: string): string {
    if (!hexColor || hexColor === "transparent") {
      return ASS_CONFIG.COLORS.TRANSPARENT;
    }

    const hex = hexColor.replace("#", "");
    if (hex.length !== 6) {
      return ASS_CONFIG.COLORS.WHITE;
    }

    const r = hex.substring(0, 2);
    const g = hex.substring(2, 4);
    const b = hex.substring(4, 6);
    return `&H${b}${g}${r}&`;
  }

  /**
   * 转换对齐方式到ASS格式
   */
  static getASSAlignment(textAlign: string): number {
    const alignmentMap: Record<string, number> = {
      left: ASS_CONFIG.ALIGNMENT.LEFT,
      right: ASS_CONFIG.ALIGNMENT.RIGHT,
      center: ASS_CONFIG.ALIGNMENT.CENTER,
    };

    return alignmentMap[textAlign] || ASS_CONFIG.ALIGNMENT.CENTER;
  }

  /**
   * 转换时间格式到ASS格式
   */
  static convertTimeToASS(timeStr: string): string {
    const parts = timeStr.split(":");
    if (parts.length !== 3) {
      return timeStr + ".00";
    }

    const hours = parseInt(parts[0], 10);
    const minutes = parts[1];
    const seconds = parts[2];
    return `${hours}:${minutes}:${seconds}.00`;
  }

  /**
   * 转义ASS文本
   */
  static escapeASSText(text: string): string {
    if (!text) return "";

    return text
      .replace(/\\/g, "\\\\")
      .replace(/\{/g, "\\{")
      .replace(/\}/g, "\\}")
      .replace(/\n/g, "\\N");
  }

  /**
   * 解析时间字符串为毫秒
   */
  static parseTimeToMs(timeStr: string): number {
    const parts = timeStr.split(":");
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseInt(parts[2]) || 0;

    return (hours * 3600 + minutes * 60 + seconds) * 1000;
  }

  /**
   * 生成ASS文件头部
   */
  static generateASSHeader(
    canvasWidth: number,
    canvasHeight: number,
    title: string = "Generated Subtitles"
  ): string {
    return `[Script Info]
Title: ${title}
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: None
PlayResX: ${canvasWidth || 1920}
PlayResY: ${canvasHeight || 1080}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
`;
  }

  /**
   * 生成字符间距标签
   */
  static generateCharSpacingTags(
    style: CaptionStyle,
    useRatio: boolean = true
  ): string {
    if (!style.charSpacing || style.charSpacing === 0) {
      return "";
    }

    const assCharSpacing = useRatio
      ? Math.round(style.charSpacing * COMMON_ASS_CONSTANTS.CHAR_SPACING_RATIO)
      : Math.round(style.charSpacing);

    return `{\\fsp${assCharSpacing}}`;
  }

  /**
   * 生成阴影标签（通用版本）
   */
  static generateShadowTags(
    style: CaptionStyle,
    scaleX: number = 1,
    scaleY: number = 1
  ): string {
    let shadowTags = "";

    // 检查是否有自定义阴影偏移
    const hasCustomOffset =
      style.shadowOffsetX !== 0 || style.shadowOffsetY !== 0;

    // 如果有自定义偏移，使用 \xshad 和 \yshad；否则使用 \shad
    if (hasCustomOffset) {
      let adjustedOffsetX = (style.shadowOffsetX || 0) * scaleX;
      let adjustedOffsetY = (style.shadowOffsetY || 0) * scaleY;

      // DPI适配：为了匹配前端在高DPI显示器上的视觉效果
      if (COMMON_ASS_CONSTANTS.ENABLE_DPI_SCALING) {
        adjustedOffsetX = Math.round(
          adjustedOffsetX * COMMON_ASS_CONSTANTS.DPI_SCALE_FACTOR
        );
        adjustedOffsetY = Math.round(
          adjustedOffsetY * COMMON_ASS_CONSTANTS.DPI_SCALE_FACTOR
        );

        console.log(
          `阴影偏移缩放和DPI适配: 原始(${style.shadowOffsetX}, ${
            style.shadowOffsetY
          }) -> 缩放后(${(style.shadowOffsetX || 0) * scaleX}, ${
            (style.shadowOffsetY || 0) * scaleY
          }) -> 最终(${adjustedOffsetX}, ${adjustedOffsetY}) [缩放:${scaleX}x${scaleY}, DPI:${
            COMMON_ASS_CONSTANTS.DPI_SCALE_FACTOR
          }x]`
        );
      } else {
        adjustedOffsetX = Math.round(adjustedOffsetX);
        adjustedOffsetY = Math.round(adjustedOffsetY);
        console.log(
          `阴影偏移缩放: 原始(${style.shadowOffsetX}, ${style.shadowOffsetY}) -> 缩放后(${adjustedOffsetX}, ${adjustedOffsetY}) [${scaleX}x${scaleY}]`
        );
      }

      shadowTags += `\\xshad${adjustedOffsetX}`;
      shadowTags += `\\yshad${adjustedOffsetY}`;
    } else if (style.shadowBlur > 0) {
      // 没有自定义偏移时，根据shadowBlur计算阴影距离
      const scale = Math.max(scaleX, scaleY);
      const shadowDistance = Math.max(
        1,
        Math.round(
          style.shadowBlur * COMMON_ASS_CONSTANTS.SHADOW_DISTANCE_RATIO * scale
        )
      );
      shadowTags += `\\shad${shadowDistance}`;
    }

    // 阴影颜色和透明度
    if (style.shadowColor && style.shadowColor !== "transparent") {
      const shadowColor = this.convertColorToASS(style.shadowColor);
      shadowTags += `\\4c${shadowColor}`;

      // 根据shadowBlur调整阴影透明度来模拟模糊效果
      // shadowBlur越大，阴影越透明，视觉上更像模糊效果
      const baseAlpha = COMMON_ASS_CONSTANTS.DEFAULT_ALPHA_VALUE;
      const blurAdjustment = Math.min(style.shadowBlur * 15, 100); // 限制最大调整量
      const adjustedAlpha = Math.min(baseAlpha + blurAdjustment, 200);

      const alphaValue = (255 - adjustedAlpha).toString(16).padStart(2, "0");
      shadowTags += `\\4a&H${alphaValue}&`;
    }

    return shadowTags ? `{${shadowTags}}` : "";
  }

  /**
   * 生成透明度标签
   */
  static generateOpacityTags(
    style: CaptionStyle,
    elementOpacity?: number
  ): string {
    const finalOpacity = this.calculateFinalOpacity(style, elementOpacity);

    if (finalOpacity >= 1) {
      return ""; // 完全不透明，不需要透明度标签
    }

    // ASS透明度值：0 = 完全不透明，255 = 完全透明
    const alphaValue = Math.round((1 - finalOpacity) * 255);
    const alphaHex = alphaValue.toString(16).padStart(2, "0").toUpperCase();

    return `{\\1a&H${alphaHex}&}`;
  }

  /**
   * 计算最终透明度
   */
  static calculateFinalOpacity(
    style: CaptionStyle,
    elementOpacity?: number
  ): number {
    const styleOpacity = style.opacity ?? 1;
    return elementOpacity !== undefined
      ? styleOpacity * elementOpacity
      : styleOpacity;
  }

  /**
   * 生成渐变标签
   */
  static generateGradientTags(style: CaptionStyle): string {
    if (
      !style.useGradient ||
      !style.gradientColors ||
      style.gradientColors.length < 2
    ) {
      return "";
    }

    const color1 = this.convertColorToASS(style.gradientColors[0]);
    const color2 = this.convertColorToASS(style.gradientColors[1]);
    return `{\\1c${color1}\\3c${color2}}`;
  }

  /**
   * 生成颜色标签（支持渐变和普通颜色）
   */
  static generateColorTags(style: CaptionStyle): string {
    if (
      style.useGradient &&
      style.gradientColors &&
      style.gradientColors.length >= 2
    ) {
      // 渐变色
      return this.generateGradientTags(style);
    } else {
      // 普通颜色
      const fontColor = this.convertColorToASS(style.fontColor);
      return `{\\1c${fontColor}}`;
    }
  }

  /**
   * 生成字体样式标签（粗体、斜体等）
   */
  static generateFontStyleTags(style: CaptionStyle): string {
    let styleTags = "";

    if (style.styles && style.styles.length > 0) {
      // 粗体
      if (style.styles.includes("bold") || style.fontWeight >= 700) {
        styleTags += "\\b1";
      }

      // 斜体
      if (style.styles.includes("italic")) {
        styleTags += "\\i1";
      }
    }

    return styleTags ? `{${styleTags}}` : "";
  }

  /**
   * 生成描边标签
   */
  static generateStrokeTags(
    style: CaptionStyle,
    scaleX: number = 1,
    scaleY: number = 1
  ): string {
    if (!style.strokeWidth || style.strokeWidth <= 0) {
      return "";
    }

    const scale = Math.max(scaleX, scaleY); // 使用较大的缩放值来保持描边比例
    const adjustedStrokeWidth = Math.round(style.strokeWidth * scale);
    const strokeColor = this.convertColorToASS(style.strokeColor);

    console.log(
      `描边宽度缩放: 原始(${style.strokeWidth}) -> 缩放后(${adjustedStrokeWidth}) [${scale}x]`
    );

    return `{\\3c${strokeColor}\\bord${adjustedStrokeWidth}}`;
  }

  /**
   * 获取ASS对齐值
   */
  static getASSAlignmentValue(originX?: string, originY?: string): number {
    const vertical = originY || "bottom";
    const horizontal = originX || "center";
    const key = `${vertical}-${horizontal}` as keyof typeof ASS_ALIGNMENT_MAP;

    return ASS_ALIGNMENT_MAP[key] || ASS_ALIGNMENT_MAP["bottom-center"];
  }

  /**
   * 创建临时文件
   */
  static createTempFile(
    prefix: string,
    content: string,
    suffix: string = ""
  ): string {
    const tempDir = os.tmpdir();
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .replace("T", "_")
      .replace("Z", "");

    const filename = suffix
      ? `${prefix}_${timestamp}_${suffix}.ass`
      : `${prefix}_${timestamp}.ass`;

    const subtitlePath = path.join(tempDir, filename);

    fs.writeFileSync(subtitlePath, content, "utf8");
    return subtitlePath;
  }

  /**
   * 验证ASS文件格式
   */
  static validateASSFile(filePath: string): boolean {
    try {
      if (!fs.existsSync(filePath)) {
        return false;
      }

      const content = fs.readFileSync(filePath, "utf8");
      const requiredSections = ["[Script Info]", "[V4+ Styles]", "[Events]"];

      return requiredSections.every((section) => content.includes(section));
    } catch (error) {
      console.error("验证ASS文件时出错:", error);
      return false;
    }
  }

  /**
   * 获取ASS文件的字幕数量
   */
  static getSubtitleCount(filePath: string): number {
    try {
      if (!fs.existsSync(filePath)) {
        return 0;
      }

      const content = fs.readFileSync(filePath, "utf8");
      const dialogueLines = content
        .split("\n")
        .filter((line) => line.trim().startsWith("Dialogue:"));

      return dialogueLines.length;
    } catch (error) {
      console.error("统计字幕数量时出错:", error);
      return 0;
    }
  }

  /**
   * 清理临时文件
   */
  static cleanupTempFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log("清理临时文件:", filePath);
      }
    } catch (error) {
      console.warn("清理临时文件失败:", error);
    }
  }

  /**
   * 检测文本是否包含中文字符
   */
  static isChineseText(text: string): boolean {
    if (!text) return false;
    // 检测中文字符的正则表达式
    const chineseRegex = /[\u4e00-\u9fff]/;
    return chineseRegex.test(text);
  }

  /**
   * 生成淡入淡出动画标签
   */
  static generateFadeAnimation(
    animation: any,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const duration = animation.duration || 1000; // 默认1秒

    if (direction === "in") {
      // 淡入动画：从完全透明到不透明
      return `\\fad(${duration},0)`;
    } else {
      // 淡出动画：从不透明到完全透明
      return `\\fad(0,${duration})`;
    }
  }
}
