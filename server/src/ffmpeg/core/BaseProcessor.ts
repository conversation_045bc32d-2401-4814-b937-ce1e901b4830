import { FFmpegUtils } from "../utils";
import { FFMPEG_CONFIG } from "../config";
import { MediaElement, ProcessingContext, ProcessingResult } from "./types";
import * as path from "path";
import * as fs from "fs";
import * as os from "os";

/**
 * 输入源缓存接口，用于存储媒体源路径和对应的输入索引
 */
interface InputSourceCache {
  [sourcePath: string]: number;
}

/**
 * Base class for all media processors in the FFmpeg pipeline
 * Provides common utility methods for handling media elements
 */
export abstract class BaseProcessor {
  /** Array of temporary files created by this processor */
  private tempFiles: string[] = [];

  /** 缓存已添加的媒体输入源，避免重复添加相同的源 */
  private inputSourceCache: InputSourceCache = {};
  /**
   * Validates that a media element has the required properties
   * @param element - The media element to validate
   * @param index - The index of the element in the timeline
   * @throws Error if the element is missing required properties
   */
  protected validateElement(element: MediaElement, index: number): void {
    // Check if element has required placement data
    if (
      !element.placement &&
      element.type !== "audio" &&
      element.type !== "text"
    ) {
      throw new Error(
        `${element.type} element at index ${index} missing placement data`
      );
    }

    // Check if media elements have source path
    if (
      ["image", "video", "audio"].includes(element.type) &&
      !element.properties.src
    ) {
      throw new Error(
        `${element.type} element at index ${index} missing src property`
      );
    }

    // Validate timeFrame
    if (element.timeFrame.start < 0) {
      throw new Error(
        `${element.type} element at index ${index} has negative start time`
      );
    }

    if (element.timeFrame.end <= element.timeFrame.start) {
      throw new Error(
        `${element.type} element at index ${index} has end time <= start time`
      );
    }

    // Validate that duration is within limits
    const durationMs = element.timeFrame.end - element.timeFrame.start;
    if (durationMs / 1000 > FFMPEG_CONFIG.MAX_DURATION) {
      throw new Error(
        `${element.type} element at index ${index} exceeds maximum duration of ${FFMPEG_CONFIG.MAX_DURATION} seconds`
      );
    }
  }

  /**
   * Gets the escaped file path for a media element
   * @param element - The media element
   * @returns The escaped file path
   */
  protected getMediaInputPath(element: MediaElement): string {
    const srcPath = (element.properties.src as string) || "";
    return FFmpegUtils.escapeFilePath(srcPath);
  }

  /**
   * Adds a media input to the inputs array and returns its index
   * 优化版本：检查是否已经添加过相同的媒体源，如果是则直接返回缓存的索引
   * 支持硬件加速解码和GIF循环播放
   *
   * @param inputs - The array of input commands
   * @param element - The media element to add
   * @returns The index of the added input
   */
  protected addMediaInput(inputs: string[], element: MediaElement): number {
    const mediaInputPath = this.getMediaInputPath(element);

    // 为GIF元素创建唯一的缓存键，包含循环信息
    let cacheKey = mediaInputPath;
    if (element.type === "gif") {
      const duration = (element.timeFrame.end - element.timeFrame.start) / 1000;
      cacheKey = `${mediaInputPath}:gif:duration:${duration}`;
    }

    // 检查是否已经添加过相同的媒体源（对于GIF，包含相同的循环设置）
    if (this.inputSourceCache[cacheKey] !== undefined) {
      console.log(
        `重用已存在的媒体输入源: ${mediaInputPath}, 索引: ${this.inputSourceCache[cacheKey]}`
      );
      return this.inputSourceCache[cacheKey];
    }

    // 检查是否启用了硬件加速
    const useHwAccel = FFMPEG_CONFIG.HW_ACCELERATION.enabled;
    let hwAccelOptions = "";
    let streamLoopOptions = "";

    // 为GIF元素添加循环播放选项
    if (element.type === "gif") {
      const duration = (element.timeFrame.end - element.timeFrame.start) / 1000;
      // 假设GIF原始长度约为2-3秒，计算需要循环的次数
      // 使用-1表示无限循环，FFmpeg会根据需要的duration自动停止
      streamLoopOptions = "-stream_loop -1 ";
      console.log(
        `为GIF元素 ${element.id} 添加循环播放选项，目标时长: ${duration}秒`
      );
    }

    if (useHwAccel && (element.type === "video" || element.type === "image")) {
      // 获取最佳的硬件加速器类型
      const acceleratorType =
        FFMPEG_CONFIG.HW_ACCELERATION.getBestAccelerator();

      if (acceleratorType) {
        // 根据不同的硬件加速器类型设置解码选项
        switch (acceleratorType) {
          case "nvidia":
            hwAccelOptions = "-hwaccel cuda -hwaccel_output_format cuda ";
            break;
          case "apple":
            hwAccelOptions = "-hwaccel videotoolbox ";
            break;
          case "intel":
            hwAccelOptions = "-hwaccel qsv -hwaccel_output_format qsv ";
            break;
          case "amd":
            hwAccelOptions = "-hwaccel amf ";
            break;
          case "vaapi":
            hwAccelOptions =
              "-hwaccel vaapi -hwaccel_device /dev/dri/renderD128 -hwaccel_output_format vaapi ";
            break;
        }

        if (hwAccelOptions) {
          console.log(
            `为媒体输入源 ${mediaInputPath} 启用硬件加速解码: ${acceleratorType}`
          );
        }
      }
    }

    // 添加新的媒体输入源，包含可能的硬件加速选项和GIF循环选项
    inputs.push(`${streamLoopOptions}${hwAccelOptions}-i "${mediaInputPath}"`);
    const inputIndex = inputs.length - 1;

    // 缓存媒体源路径和对应的输入索引（使用包含GIF信息的缓存键）
    this.inputSourceCache[cacheKey] = inputIndex;

    const optionsInfo = [];
    if (streamLoopOptions) optionsInfo.push("GIF循环播放");
    if (hwAccelOptions) optionsInfo.push("硬件加速");

    console.log(
      `添加新的媒体输入源: ${mediaInputPath}, 索引: ${inputIndex}${
        optionsInfo.length > 0 ? `, 选项: ${optionsInfo.join(", ")}` : ""
      }`
    );

    return inputIndex;
  }

  /**
   * Calculates the duration of a media element in seconds
   * @param element - The media element
   * @returns The duration in seconds
   */
  protected calculateElementDuration(element: MediaElement): number {
    const durationMs = element.timeFrame.end - element.timeFrame.start;
    const durationSec = durationMs / 1000;

    // If playback speed is specified, adjust the duration
    if (element.playbackSpeed && element.playbackSpeed > 0) {
      return durationSec / element.playbackSpeed;
    }

    return durationSec;
  }

  /**
   * Creates a temporary file and registers it for cleanup
   * @param prefix - Prefix for the temporary file name
   * @param extension - File extension
   * @returns Path to the created temporary file
   */
  protected createTempFile(prefix: string, extension: string): string {
    const tempDir = FFMPEG_CONFIG.TEMP_FILES.directory;
    const fileName = `${
      FFMPEG_CONFIG.TEMP_FILES.prefix
    }${prefix}_${FFmpegUtils.generateTimestamp()}.${extension}`;
    const filePath = path.join(tempDir, fileName);

    // Register for cleanup
    this.tempFiles.push(filePath);

    return filePath;
  }

  /**
   * Cleans up all temporary files created by this processor
   * 同时清理输入源缓存
   */
  public cleanup(): void {
    let cleanedCount = 0;
    let errorCount = 0;

    for (const filePath of this.tempFiles) {
      if (fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          cleanedCount++;
        } catch (error) {
          console.error(`Failed to remove temporary file ${filePath}:`, error);
          errorCount++;

          // 如果删除失败，尝试使用异步方式延迟删除
          setTimeout(() => {
            try {
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                console.log(`延迟删除处理器临时文件成功: ${filePath}`);
              }
            } catch (retryError) {
              console.error(
                `延迟删除处理器临时文件失败 ${filePath}: ${retryError}`
              );
            }
          }, 1000); // 延迟1秒后尝试删除
        }
      }
    }

    // 记录清理结果
    if (cleanedCount > 0 || errorCount > 0) {
      console.log(
        `处理器临时文件清理结果: 成功=${cleanedCount}, 失败=${errorCount}`
      );
    }

    // Clear the arrays and cache
    this.tempFiles = [];
    this.inputSourceCache = {};
    console.log("已清理输入源缓存");
  }

  /**
   * Process a media element and generate the appropriate FFmpeg filters
   * @param element - The media element to process
   * @param context - The processing context
   * @returns The processing result
   */
  protected abstract processElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult;
}
