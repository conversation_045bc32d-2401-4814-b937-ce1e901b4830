import * as path from "path";
import * as fs from "fs";
import { tmpdir } from "os";
import { promisify } from "util";
import logger from "../../../utils/logger";

// 动态导入imagemagick以避免类型错误
const imagemagick = require("imagemagick");

// 将imagemagick的convert方法转换为Promise
const convert = promisify(imagemagick.convert);

/**
 * ShapeImageGenerator - 使用ImageMagick生成shape图片
 * 支持各种形状的精确渲染，确保与前端Canvas显示一致
 */
export class ShapeImageGenerator {
  private tempFiles: string[] = [];
  private tempDir: string;

  // 超采样倍数，用于提高图片清晰度
  // 2倍超采样在质量和性能之间取得平衡
  private readonly SUPER_SAMPLING_FACTOR = 2;

  // 质量设置
  private qualitySettings = {
    density: 300, // DPI设置
    quality: 100, // 图片质量 (0-100)
    enableSuperSampling: true, // 是否启用超采样
    filter: "Lanczos", // 缩放滤镜类型
    imageScaleFactor: 1, // 图片尺寸放大倍数 (1-8)
    enableImageScaling: false, // 是否启用图片尺寸放大
  };

  constructor() {
    // 设置临时目录为server/temp
    this.tempDir = path.resolve(process.cwd(), "server", "temp");
    this.ensureTempDirExists();
  }

  /**
   * 确保临时目录存在
   */
  private ensureTempDirExists(): void {
    if (!fs.existsSync(this.tempDir)) {
      try {
        fs.mkdirSync(this.tempDir, { recursive: true });
        logger.info(`已创建临时目录: ${this.tempDir}`);
      } catch (error) {
        logger.error(`创建临时目录失败: ${this.tempDir}`, error);
        // 如果创建失败，回退到系统临时目录
        this.tempDir = tmpdir();
        logger.info(`回退使用系统临时目录: ${this.tempDir}`);
      }
    }
  }

  /**
   * 设置图片质量参数
   */
  setQualitySettings(
    settings: Partial<{
      density: number;
      quality: number;
      enableSuperSampling: boolean;
      filter: string;
      imageScaleFactor: number;
      enableImageScaling: boolean;
    }>
  ): void {
    // 验证图片放大倍数范围
    if (settings.imageScaleFactor !== undefined) {
      if (settings.imageScaleFactor < 1 || settings.imageScaleFactor > 8) {
        logger.warn(
          `图片放大倍数超出范围 (1-8): ${settings.imageScaleFactor}，将限制在有效范围内`
        );
        settings.imageScaleFactor = Math.max(
          1,
          Math.min(8, settings.imageScaleFactor)
        );
      }
    }

    this.qualitySettings = { ...this.qualitySettings, ...settings };
    logger.info(`已更新质量设置:`, this.qualitySettings);
  }

  /**
   * 生成shape图片
   * @param shapeType 形状类型
   * @param width 宽度
   * @param height 高度
   * @param fill 填充颜色
   * @param stroke 边框颜色
   * @param strokeWidth 边框宽度
   * @param borderRadius 圆角半径（仅用于roundedRect）
   * @param opacity 透明度 (0-1)
   * @returns 生成的图片文件路径
   */
  async generateShapeImage(
    shapeType: string,
    width: number,
    height: number,
    fill: string = "#9e9e9e",
    stroke: string = "#757575",
    strokeWidth: number = 1,
    borderRadius: number = 0,
    opacity: number = 1
  ): Promise<string> {
    try {
      // 生成唯一的临时文件路径
      const tempImagePath = path.join(
        this.tempDir,
        `shape_${shapeType}_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}.png`
      );

      // 记录临时文件，用于后续清理
      this.tempFiles.push(tempImagePath);

      logger.info(
        `生成${shapeType}形状图片: ${width}x${height}, 输出到: ${tempImagePath}`
      );

      // 构建ImageMagick命令参数
      const args = this.buildImageMagickArgs(
        shapeType,
        width,
        height,
        fill,
        stroke,
        strokeWidth,
        borderRadius,
        opacity,
        tempImagePath
      );

      // 执行ImageMagick命令
      await convert(args);

      // 验证文件是否生成成功
      if (!fs.existsSync(tempImagePath)) {
        throw new Error(`生成的图片文件不存在: ${tempImagePath}`);
      }

      logger.info(`成功生成${shapeType}形状图片: ${tempImagePath}`);
      return tempImagePath;
    } catch (error) {
      logger.error(`生成${shapeType}形状图片失败:`, error);
      throw new Error(
        `生成形状图片失败: ${
          error instanceof Error ? error.message : "未知错误"
        }`
      );
    }
  }

  /**
   * 构建ImageMagick命令参数
   */
  private buildImageMagickArgs(
    shapeType: string,
    width: number,
    height: number,
    fill: string,
    stroke: string,
    strokeWidth: number,
    borderRadius: number,
    opacity: number,
    outputPath: string
  ): string[] {
    const args: string[] = [];

    // 设置高分辨率密度以提高清晰度
    args.push("-density", this.qualitySettings.density.toString());

    // 计算考虑边框的实际画布尺寸
    // 边框会向内绘制，所以需要增加画布尺寸来容纳完整的边框
    const strokePadding = strokeWidth > 0 ? Math.ceil(strokeWidth) : 0;
    const baseCanvasWidth = width + strokePadding * 2;
    const baseCanvasHeight = height + strokePadding * 2;

    // 计算总的放大倍数（超采样 + 图片尺寸放大）
    const superSamplingFactor = this.qualitySettings.enableSuperSampling
      ? this.SUPER_SAMPLING_FACTOR
      : 1;
    const imageScaleFactor = this.qualitySettings.enableImageScaling
      ? this.qualitySettings.imageScaleFactor
      : 1;

    // 总放大倍数 = 超采样倍数 × 图片放大倍数
    const totalScaleFactor = superSamplingFactor * imageScaleFactor;

    const canvasWidth = baseCanvasWidth * totalScaleFactor;
    const canvasHeight = baseCanvasHeight * totalScaleFactor;
    const scaledStrokeWidth = strokeWidth * totalScaleFactor;
    const scaledStrokePadding = strokePadding * totalScaleFactor;

    logger.info(
      `图片尺寸计算: 基础=${baseCanvasWidth}x${baseCanvasHeight}, 超采样=${superSamplingFactor}x, 图片放大=${imageScaleFactor}x, 最终=${canvasWidth}x${canvasHeight}`
    );

    // 创建透明背景，使用超采样后的尺寸
    args.push("-size", `${canvasWidth}x${canvasHeight}`, "xc:transparent");

    // 设置抗锯齿以获得更平滑的边缘
    args.push("-antialias");

    // 根据形状类型设置填充和边框颜色
    // 对于线条形状（叉号、波浪形），使用stroke而不是fill
    const isLineBasedShape =
      shapeType === "cross" || shapeType === "wave" || shapeType === "line";

    if (isLineBasedShape) {
      // 线条形状：设置stroke为主颜色，fill为none
      args.push("-fill", "none");
      if (fill && fill !== "transparent") {
        args.push("-stroke", this.convertColor(fill));
        // 对于线条形状，使用较粗的线宽以匹配前端效果
        const lineStrokeWidth = Math.max(
          scaledStrokeWidth,
          6 * totalScaleFactor
        );
        args.push("-strokewidth", lineStrokeWidth.toString());
      } else {
        args.push("-stroke", "none");
      }
    } else {
      // 填充形状：正常设置fill和stroke
      if (fill && fill !== "transparent") {
        args.push("-fill", this.convertColor(fill));
      } else {
        args.push("-fill", "none");
      }

      if (stroke && stroke !== "transparent" && strokeWidth > 0) {
        args.push("-stroke", this.convertColor(stroke));
        args.push("-strokewidth", scaledStrokeWidth.toString());
      } else {
        args.push("-stroke", "none");
      }
    }

    // 根据形状类型添加绘制命令（使用总放大倍数）
    const drawCommand = this.getDrawCommand(
      shapeType,
      width * totalScaleFactor,
      height * totalScaleFactor,
      borderRadius * totalScaleFactor,
      scaledStrokePadding
    );
    if (drawCommand) {
      args.push("-draw", drawCommand);
    }

    // 应用透明度
    if (opacity < 1) {
      const alphaValue = Math.round(opacity * 100);
      args.push(
        "-alpha",
        "set",
        "-channel",
        "A",
        "-evaluate",
        "multiply",
        `${alphaValue}%`,
        "+channel"
      );
    }

    // 根据设置决定最终输出尺寸
    let finalWidth = baseCanvasWidth;
    let finalHeight = baseCanvasHeight;

    if (this.qualitySettings.enableImageScaling) {
      // 如果启用图片尺寸放大，最终尺寸应该是放大后的尺寸
      finalWidth = baseCanvasWidth * imageScaleFactor;
      finalHeight = baseCanvasHeight * imageScaleFactor;
    }

    // 如果需要缩放（超采样或图片尺寸不匹配）
    if (
      totalScaleFactor > 1 &&
      (this.qualitySettings.enableSuperSampling ||
        canvasWidth !== finalWidth ||
        canvasHeight !== finalHeight)
    ) {
      args.push("-filter", this.qualitySettings.filter); // 使用配置的滤镜进行高质量缩放
      args.push("-resize", `${finalWidth}x${finalHeight}`);

      logger.info(
        `应用缩放: ${canvasWidth}x${canvasHeight} → ${finalWidth}x${finalHeight}`
      );
    }

    // 设置高质量输出参数
    args.push("-quality", this.qualitySettings.quality.toString());
    args.push("-compress", "None"); // 无压缩以保持最高质量
    args.push("-depth", "8"); // 8位色深

    // 设置颜色空间为sRGB以确保颜色准确性
    args.push("-colorspace", "sRGB");

    // 输出文件路径
    args.push(outputPath);

    return args;
  }

  /**
   * 获取绘制命令
   */
  private getDrawCommand(
    shapeType: string,
    width: number,
    height: number,
    borderRadius: number,
    strokePadding: number = 0
  ): string {
    // 考虑边框偏移，使形状在扩展画布中居中
    const offsetX = strokePadding;
    const offsetY = strokePadding;
    const centerX = offsetX + width / 2;
    const centerY = offsetY + height / 2;

    switch (shapeType) {
      case "rect":
        // 矩形应该有轻微的圆角，与界面中的borderRadius: 1保持一致
        // 确保宽高比例为40:30
        return `roundrectangle ${offsetX},${offsetY} ${offsetX + width},${
          offsetY + height
        } 1,1`;

      case "roundedRect":
        // 圆角矩形改为无圆角的正方形
        return `rectangle ${offsetX},${offsetY} ${offsetX + width},${
          offsetY + height
        }`;

      case "circle":
        // 使用较小的尺寸作为半径，确保圆形在矩形内
        const radius_circle = Math.min(width, height) / 2;
        return `circle ${centerX},${centerY} ${centerX},${
          centerY - radius_circle
        }`;

      case "ellipse":
        const radiusX = width / 2;
        const radiusY = height / 2;
        return `ellipse ${centerX},${centerY} ${radiusX},${radiusY} 0,360`;

      case "line":
        return `line ${offsetX},${centerY} ${offsetX + width},${centerY}`;

      case "triangle":
        // 等边三角形，考虑偏移
        const topX = centerX;
        const topY = offsetY + height * 0.1;
        const leftX = offsetX + width * 0.1;
        const leftY = offsetY + height * 0.9;
        const rightX = offsetX + width * 0.9;
        const rightY = offsetY + height * 0.9;
        return `polygon ${topX},${topY} ${leftX},${leftY} ${rightX},${rightY}`;

      case "pentagon":
        return this.generatePolygonPoints(
          5,
          centerX,
          centerY,
          (Math.min(width, height) / 2) * 0.8
        );

      case "hexagon":
        return this.generatePolygonPoints(
          6,
          centerX,
          centerY,
          (Math.min(width, height) / 2) * 0.8
        );

      case "octagon":
        return this.generatePolygonPoints(
          8,
          centerX,
          centerY,
          (Math.min(width, height) / 2) * 0.8
        );

      case "parallelogram":
        const offset = width / 4;
        return `polygon ${offsetX + offset},${offsetY} ${
          offsetX + width
        },${offsetY} ${offsetX + width - offset},${
          offsetY + height
        } ${offsetX},${offsetY + height}`;

      case "arch":
        // 拱形使用路径绘制，考虑偏移
        return `path 'M ${offsetX},${offsetY + height} L ${offsetX},${
          offsetY + height / 2
        } Q ${centerX},${offsetY} ${offsetX + width},${
          offsetY + height / 2
        } L ${offsetX + width},${offsetY + height} Z'`;

      case "diamond":
        // 菱形 - 旋转45度的正方形
        return `polygon ${centerX},${offsetY} ${
          offsetX + width
        },${centerY} ${centerX},${offsetY + height} ${offsetX},${centerY}`;

      case "rightArrow":
        // 右箭头
        const arrowBodyWidth = width * 0.6;
        const arrowHeadWidth = width * 0.4;
        const arrowBodyHeight = height * 0.4;
        const arrowBodyTop = centerY - arrowBodyHeight / 2;
        const arrowBodyBottom = centerY + arrowBodyHeight / 2;
        return `polygon ${offsetX},${arrowBodyTop} ${
          offsetX + arrowBodyWidth
        },${arrowBodyTop} ${offsetX + arrowBodyWidth},${offsetY} ${
          offsetX + width
        },${centerY} ${offsetX + arrowBodyWidth},${offsetY + height} ${
          offsetX + arrowBodyWidth
        },${arrowBodyBottom} ${offsetX},${arrowBodyBottom}`;

      case "upArrow":
        // 上箭头
        const upArrowBodyHeight = height * 0.6;
        const upArrowHeadHeight = height * 0.4;
        const upArrowBodyWidth = width * 0.4;
        const upArrowBodyLeft = centerX - upArrowBodyWidth / 2;
        const upArrowBodyRight = centerX + upArrowBodyWidth / 2;
        return `polygon ${upArrowBodyLeft},${
          offsetY + upArrowHeadHeight
        } ${offsetX},${offsetY + upArrowHeadHeight} ${centerX},${offsetY} ${
          offsetX + width
        },${offsetY + upArrowHeadHeight} ${upArrowBodyRight},${
          offsetY + upArrowHeadHeight
        } ${upArrowBodyRight},${offsetY + height} ${upArrowBodyLeft},${
          offsetY + height
        }`;

      case "downArrow":
        // 下箭头
        const downArrowBodyHeight = height * 0.6;
        const downArrowHeadHeight = height * 0.4;
        const downArrowBodyWidth = width * 0.4;
        const downArrowBodyLeft = centerX - downArrowBodyWidth / 2;
        const downArrowBodyRight = centerX + downArrowBodyWidth / 2;
        return `polygon ${downArrowBodyLeft},${offsetY} ${downArrowBodyRight},${offsetY} ${downArrowBodyRight},${
          offsetY + downArrowBodyHeight
        } ${offsetX + width},${offsetY + downArrowBodyHeight} ${centerX},${
          offsetY + height
        } ${offsetX},${offsetY + downArrowBodyHeight} ${downArrowBodyLeft},${
          offsetY + downArrowBodyHeight
        }`;

      case "cross":
        // 叉号 - 使用两条相交的线条
        const crossSize = Math.min(width, height) * 0.7;
        const crossLeft = centerX - crossSize / 2;
        const crossRight = centerX + crossSize / 2;
        const crossTop = centerY - crossSize / 2;
        const crossBottom = centerY + crossSize / 2;
        // 由于ImageMagick的polygon命令画线条不太理想，我们用path命令
        return `path 'M ${crossLeft},${crossTop} L ${crossRight},${crossBottom} M ${crossRight},${crossTop} L ${crossLeft},${crossBottom}'`;

      case "wave":
        // 波浪形 - 使用贝塞尔曲线
        const waveHeight = height * 0.3;
        const waveY = centerY;
        const controlPointY1 = waveY - waveHeight;
        const controlPointY2 = waveY + waveHeight;
        return `path 'M ${offsetX},${waveY} Q ${
          offsetX + width * 0.25
        },${controlPointY1} ${offsetX + width * 0.5},${waveY} T ${
          offsetX + width
        },${waveY}'`;

      case "star":
        // 五角星
        return this.generateStarPoints(
          5,
          centerX,
          centerY,
          (Math.min(width, height) / 2) * 0.9,
          (Math.min(width, height) / 2) * 0.4
        );

      case "fourPointStar":
        // 四角星
        return this.generateStarPoints(
          4,
          centerX,
          centerY,
          (Math.min(width, height) / 2) * 0.9,
          (Math.min(width, height) / 2) * 0.3
        );

      case "sixPointStar":
        // 六角星
        return this.generateStarPoints(
          6,
          centerX,
          centerY,
          (Math.min(width, height) / 2) * 0.9,
          (Math.min(width, height) / 2) * 0.4
        );

      case "eightPointStar":
        // 八角星
        return this.generateStarPoints(
          8,
          centerX,
          centerY,
          (Math.min(width, height) / 2) * 0.9,
          (Math.min(width, height) / 2) * 0.4
        );

      case "sunBurst":
        // 太阳形 - 中心圆加射线
        const sunInnerRadius = Math.min(width, height) / 4;
        const sunOuterRadius = (Math.min(width, height) / 2) * 0.9;
        let sunPath = `circle ${centerX},${centerY} ${centerX},${
          centerY - sunInnerRadius
        }`;
        // 添加射线
        for (let i = 0; i < 16; i++) {
          const angle = (i * Math.PI) / 8;
          const x1 = centerX + Math.cos(angle) * sunInnerRadius;
          const y1 = centerY + Math.sin(angle) * sunInnerRadius;
          const x2 = centerX + Math.cos(angle) * sunOuterRadius;
          const y2 = centerY + Math.sin(angle) * sunOuterRadius;
          sunPath += ` line ${x1},${y1} ${x2},${y2}`;
        }
        return sunPath;

      case "semicircle":
        // 半圆
        const semiRadius = width / 2;
        return `path 'M ${offsetX},${
          offsetY + height
        } A ${semiRadius},${semiRadius} 0 0,1 ${offsetX + width},${
          offsetY + height
        } Z'`;

      case "quarterCircle":
        // 四分之一圆 - 与前端Canvas保持一致
        // 前端路径：M 0 0 L 0 radius A radius radius 0 0 0 radius 0 Z
        // 创建左下角的四分之一圆：从左上角到左下角，然后弧线到右上角，闭合
        const quarterRadius = Math.min(width, height);
        return `path 'M ${offsetX},${offsetY} L ${offsetX},${
          offsetY + quarterRadius
        } A ${quarterRadius},${quarterRadius} 0 0,0 ${
          offsetX + quarterRadius
        },${offsetY} Z'`;

      case "ring":
        // 环形 - 外圆减内圆，使用正确的填充规则
        const ringOuterRadius = (Math.min(width, height) / 2) * 0.9;
        const ringInnerRadius = ringOuterRadius * 0.5;
        return `path 'M ${
          centerX + ringOuterRadius
        },${centerY} A ${ringOuterRadius},${ringOuterRadius} 0 1,1 ${
          centerX - ringOuterRadius
        },${centerY} A ${ringOuterRadius},${ringOuterRadius} 0 1,1 ${
          centerX + ringOuterRadius
        },${centerY} M ${
          centerX + ringInnerRadius
        },${centerY} A ${ringInnerRadius},${ringInnerRadius} 0 1,0 ${
          centerX - ringInnerRadius
        },${centerY} A ${ringInnerRadius},${ringInnerRadius} 0 1,0 ${
          centerX + ringInnerRadius
        },${centerY} Z'`;

      case "halfRing":
        // 半环
        const halfRingOuterRadius = width / 2;
        const halfRingInnerRadius = halfRingOuterRadius * 0.6;
        return `path 'M ${offsetX},${
          offsetY + height
        } A ${halfRingOuterRadius},${halfRingOuterRadius} 0 0,1 ${
          offsetX + width
        },${offsetY + height} L ${
          offsetX + width - (halfRingOuterRadius - halfRingInnerRadius)
        },${
          offsetY + height
        } A ${halfRingInnerRadius},${halfRingInnerRadius} 0 0,0 ${
          offsetX + (halfRingOuterRadius - halfRingInnerRadius)
        },${offsetY + height} Z'`;

      case "plus":
        // 加号 - 两个矩形相交
        const plusThickness = Math.min(width, height) / 6;
        const plusArmLength = Math.min(width, height) * 0.8;
        const plusOffsetX = centerX - plusArmLength / 2;
        const plusOffsetY = centerY - plusThickness / 2;
        const plusOffsetX2 = centerX - plusThickness / 2;
        const plusOffsetY2 = centerY - plusArmLength / 2;
        return `rectangle ${plusOffsetX},${plusOffsetY} ${
          plusOffsetX + plusArmLength
        },${
          plusOffsetY + plusThickness
        } rectangle ${plusOffsetX2},${plusOffsetY2} ${
          plusOffsetX2 + plusThickness
        },${plusOffsetY2 + plusArmLength}`;

      default:
        // 默认绘制矩形，考虑偏移
        return `rectangle ${offsetX},${offsetY} ${offsetX + width},${
          offsetY + height
        }`;
    }
  }

  /**
   * 生成正多边形的点坐标
   */
  private generatePolygonPoints(
    sides: number,
    centerX: number,
    centerY: number,
    radius: number
  ): string {
    const points: string[] = [];
    const angleStep = (2 * Math.PI) / sides;

    for (let i = 0; i < sides; i++) {
      const angle = i * angleStep - Math.PI / 2; // 从顶部开始
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      points.push(`${x.toFixed(1)},${y.toFixed(1)}`);
    }

    return `polygon ${points.join(" ")}`;
  }

  /**
   * 生成星形的点坐标
   */
  private generateStarPoints(
    points: number,
    centerX: number,
    centerY: number,
    outerRadius: number,
    innerRadius: number
  ): string {
    const starPoints: string[] = [];
    const totalPoints = points * 2;

    for (let i = 0; i < totalPoints; i++) {
      const angle = (i * Math.PI) / points;
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const x = centerX + Math.cos(angle - Math.PI / 2) * radius;
      const y = centerY + Math.sin(angle - Math.PI / 2) * radius;
      starPoints.push(`${x.toFixed(1)},${y.toFixed(1)}`);
    }

    return `polygon ${starPoints.join(" ")}`;
  }

  /**
   * 转换颜色格式
   * 将CSS颜色格式转换为ImageMagick支持的格式
   */
  private convertColor(color: string): string {
    // 如果已经是十六进制格式，直接返回
    if (color.startsWith("#")) {
      return color;
    }

    // 处理rgb/rgba格式
    if (color.startsWith("rgb")) {
      // 简单处理，实际项目中可能需要更复杂的颜色解析
      return color;
    }

    // 处理命名颜色
    const namedColors: { [key: string]: string } = {
      transparent: "none",
      black: "#000000",
      white: "#ffffff",
      red: "#ff0000",
      green: "#00ff00",
      blue: "#0000ff",
      // 可以添加更多命名颜色
    };

    return namedColors[color.toLowerCase()] || color;
  }

  /**
   * 清理临时文件
   */
  cleanup(): void {
    this.tempFiles.forEach((filePath) => {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          logger.debug(`已删除临时shape图片文件: ${filePath}`);
        }
      } catch (error) {
        logger.warn(`删除临时shape图片文件失败: ${filePath}`, error);
      }
    });

    this.tempFiles = [];
    logger.info("已清理所有临时shape图片文件");
  }

  /**
   * 获取临时文件列表（用于调试）
   */
  getTempFiles(): string[] {
    return [...this.tempFiles];
  }
}
