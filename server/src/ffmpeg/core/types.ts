import { MediaElement as BaseMediaElement } from "../types";

/**
 * Re-export MediaElement from the main types file
 * This allows for future extension of the MediaElement interface
 * specifically for processing purposes if needed
 */
export type MediaElement = BaseMediaElement;

/**
 * Context object passed during the processing of media elements
 * Contains all the information needed to process an element
 */
export interface ProcessingContext {
  /** Index of the current element being processed */
  index: number;
  /** Array of FFmpeg input commands */
  inputs: string[];
  /** Array of video filter commands for the filter_complex */
  filterComplex: string[];
  /** Array of audio filter commands */
  audioFilters: string[];
  /** Label of the last processed video stream */
  lastVideoLabel: string;
  /** Count of audio streams processed so far */
  audioStreamCount: number;
  /** Start time of the current element in seconds */
  startTime: number;
  /** Duration of the current element in seconds */
  elementDuration: number;
  /** Canvas width in pixels */
  canvasWidth: number;
  /** Canvas height in pixels */
  canvasHeight: number;
  /** Total duration of the video in seconds */
  duration: number;
  /** Whether the video has any audio content */
  hasAudioContent: boolean;
  /** Array indicating which elements have audio */
  audioElements?: boolean[];
  /** Whether to skip audio processing for this element */
  skipAudioProcessing?: boolean;
}

/**
 * Result of processing a media element
 * Contains updated state information after processing
 */
export interface ProcessingResult {
  /** Updated label of the last processed video stream */
  lastVideoLabel: string;
  /** Updated count of audio streams */
  audioStreamCount: number;
}

/**
 * Interface for tracking progress during command generation
 */
export interface ProgressTracker {
  /**
   * Update the progress of command generation
   * @param taskId - Unique identifier for the task
   * @param stage - Current stage of processing
   * @param progress - Progress percentage (0-100)
   */
  updateCommandGenerationProgress: (
    taskId: string,
    stage: string,
    progress: number
  ) => void;
}
