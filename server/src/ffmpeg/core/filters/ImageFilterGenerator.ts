import { CanvasState } from "../../types";
import { AbstractFilterGenerator } from "./BaseFilter";

/**
 * Filter generator for image elements
 * Handles image-specific operations and transformations
 */
export class ImageFilterGenerator extends AbstractFilterGenerator {
  /**
   * Generates an FFmpeg filter string for image elements
   *
   * @param inputIndex Index of the input stream or -1 for original
   * @param element Canvas element to generate filter for
   * @param startTime Start time in milliseconds
   * @param duration Duration in seconds
   * @param index Element index for labeling
   * @param outputPrefix Optional prefix for output label (default: "img")
   * @returns FFmpeg filter string for the image element
   */
  generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    startTime: number,
    duration: number,
    index: number,
    outputPrefix: string = "img"
  ): string {
    const { placement, properties } = element;
    const { filters, effect, border } = properties;

    if (!placement) {
      throw new Error(`Image element at index ${index} missing placement data`);
    }

    // Use common method to get initial filter string
    let filterString = this.getInitialFilterString(inputIndex, index);

    // Scale and crop using common method
    const targetWidth = Math.round(placement.width);
    const targetHeight = Math.round(placement.height);

    // 检查是否有自定义剪裁参数
    const cropParams =
      placement.cropX !== undefined &&
      placement.cropY !== undefined &&
      placement.cropWidth !== undefined &&
      placement.cropHeight !== undefined
        ? {
            cropX: placement.cropX,
            cropY: placement.cropY,
            cropWidth: placement.cropWidth,
            cropHeight: placement.cropHeight,
          }
        : undefined;

    filterString = this.applyScaleAndCrop(
      filterString,
      targetWidth,
      targetHeight,
      cropParams
    );

    // Apply rotation using common method
    if (placement.rotation) {
      filterString = this.applyRotation(
        filterString,
        placement.rotation,
        targetWidth,
        targetHeight
      );
    }

    // Apply format using common method
    filterString = this.applyFormat(filterString);

    // Apply opacity using common method
    filterString = this.applyOpacity(filterString, element.opacity);

    // Apply flips using common method
    filterString = this.applyFlips(
      filterString,
      placement.flipX,
      placement.flipY
    );

    // Apply common filters
    filterString = this.applyEffects(filterString, effect as any);
    filterString = this.applyFilters(filterString, filters);
    filterString = this.applyBorder(filterString, border as any);

    return filterString + `[${outputPrefix}${index}]`;
  }
}
