import { CanvasState } from "../../types";
import { AbstractFilterGenerator } from "./BaseFilter";
import { FontManager } from "../../utils/FontManager";

/**
 * Interface for text element properties
 */
interface TextProperties {
  text?: string;
  fontFamily?: string;
  fontSize?: number;
  fontColor?: string;
  fontWeight?: string;
  fontStyle?: string;
  textAlign?: string;
  lineHeight?: number;
  styles?: string[];
  backgroundColor?: string;
  backgroundPaddingX?: number;
  backgroundPaddingY?: number;
  strokeWidth?: number;
  strokeColor?: string;
  shadowBlur?: number;
  shadowOffsetX?: number;
  shadowOffsetY?: number;
  shadowColor?: string;
}

/**
 * Filter generator for text elements
 * Handles text-specific operations like font styling, alignment, and effects
 */
export class TextFilterGenerator extends AbstractFilterGenerator {
  /**
   * Directory containing font files
   */
  private fontDir: string = process.env.FONT_DIR || "./assets/fonts";

  /**
   * Font manager instance
   */
  private fontManager: FontManager = FontManager.getInstance();

  /**
   * Generates an FFmpeg filter string for text elements
   *
   * @param inputIndex Index of the input stream or -1 for original
   * @param element Canvas element to generate filter for
   * @param startTime Start time in milliseconds
   * @param duration Duration in seconds
   * @param index Element index for labeling
   * @returns FFmpeg filter string for the text element
   */
  generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    startTime: number,
    duration: number,
    index: number
  ): string {
    const { properties, placement } = element;
    const textProps = properties as TextProperties;

    if (!textProps.text || !placement) {
      throw new Error("Text element missing required properties");
    }

    // Check if text contains newlines
    const hasNewlines = textProps.text.includes("\n");

    if (hasNewlines) {
      return this.generateMultilineTextFilter(
        textProps,
        placement,
        element.opacity ?? 1,
        index
      );
    }

    // Escape special characters in text
    const text = this.escapeText(textProps.text);

    // Determine font path based on font family and styles
    const fontPath = this.determineFontPath(textProps);

    // Extract and normalize text styling properties
    const scaleX = placement.scaleX || 1;
    const scaleY = placement.scaleY || 1;
    const fontSize = Math.round((textProps.fontSize || 16) * scaleX);
    const fontColor = textProps.fontColor || "#ffffff";
    const colorHex = fontColor.replace("#", "");
    const opacity = element.opacity ?? 1;
    const textAlign = textProps.textAlign || "left";
    const styles = textProps.styles || [];

    // Background properties
    const hasBackground = Boolean(
      textProps.backgroundColor && textProps.backgroundColor !== ""
    );
    const bgColorHex =
      hasBackground && textProps.backgroundColor
        ? textProps.backgroundColor.replace("#", "")
        : "000000";
    const paddingX = textProps.backgroundPaddingX || 10;
    const paddingY = textProps.backgroundPaddingY || 5;

    // Stroke (border) properties - 考虑缩放因子
    const hasStroke = Boolean(
      textProps.strokeWidth && textProps.strokeWidth > 0
    );
    const strokeWidth = hasStroke
      ? Math.round(textProps.strokeWidth! * scaleX)
      : 0;
    const strokeColorHex = textProps.strokeColor
      ? textProps.strokeColor.replace("#", "")
      : "000000";

    // Shadow properties - 考虑缩放因子
    const hasShadow = Boolean(textProps.shadowBlur && textProps.shadowBlur > 0);
    const shadowX = Math.round((textProps.shadowOffsetX || 0) * scaleX);
    const shadowY = Math.round((textProps.shadowOffsetY || 0) * scaleY);
    const shadowBlur = Math.round(
      (textProps.shadowBlur || 0) * Math.max(scaleX, scaleY)
    );
    const shadowColorHex = textProps.shadowColor
      ? textProps.shadowColor.replace("#", "")
      : "000000";

    // Check if we need to create composite filter for underline/strikethrough
    const needsDecorations =
      styles.includes("underlined") || styles.includes("strikethrough");

    if (needsDecorations) {
      return this.generateDecoratedTextFilter(
        text,
        fontPath,
        fontSize,
        colorHex,
        opacity,
        placement,
        styles,
        hasBackground,
        bgColorHex,
        hasStroke,
        strokeWidth || 0,
        strokeColorHex,
        hasShadow,
        shadowX,
        shadowY,
        shadowColorHex,
        shadowBlur,
        textAlign,
        index
      );
    }

    // Build the base filter string for simple text
    let filterString =
      "drawtext=" +
      `text='${text}':` +
      `fontfile='${fontPath}':` +
      `fontsize=${fontSize}:` +
      `fontcolor=0x${colorHex}@${opacity}:` +
      `x=${placement.x}:` +
      `y=${placement.y}`;

    // Apply text styles (bold, italic are handled by font selection)
    filterString = this.applyTextStyles(filterString, styles);

    // Add background if specified
    if (hasBackground) {
      filterString = this.applyBackground(
        filterString,
        placement.y,
        bgColorHex,
        opacity,
        fontSize
      );
    }

    // Add border/stroke if specified
    if (hasStroke) {
      // FFmpeg描边渲染比fabric.js细，需要放大约1.5倍来匹配视觉效果
      const adjustedStrokeWidth = strokeWidth;
      filterString += `:borderw=${adjustedStrokeWidth}:bordercolor=0x${strokeColorHex}@${opacity}`;
    }

    // Add shadow if specified
    if (hasShadow) {
      filterString = this.applyShadow(
        filterString,
        shadowX,
        shadowY,
        shadowColorHex,
        shadowBlur,
        opacity
      );
    }

    // Apply text alignment
    filterString = this.applyTextAlignment(
      filterString,
      textAlign,
      placement.x
    );

    // Apply rotation if specified
    if (placement.rotation && placement.rotation !== 0) {
      filterString += `:box=1:boxcolor=0x00000000`;
      filterString += `:angle=${placement.rotation}`;
    }

    return filterString;
  }

  /**
   * Escapes special characters in text for FFmpeg
   *
   * @param text Raw text input
   * @returns Escaped text safe for FFmpeg filters
   */
  private escapeText(text: string): string {
    // 确保文本是UTF-8编码
    const utf8Text = Buffer.from(text, "utf8").toString("utf8");

    return utf8Text
      .replace(/'/g, "'\\''")
      .replace(/[\[\]]/g, "\\$&")
      .replace(/:/g, "\\:")
      .replace(/\r/g, "") // 移除回车符
      .replace(/\t/g, " "); // 将制表符替换为空格
    // 注意：不在这里处理换行符，因为需要特殊处理
  }

  /**
   * Generates a decorated text filter with underline and/or strikethrough
   */
  private generateDecoratedTextFilter(
    text: string,
    fontPath: string,
    fontSize: number,
    colorHex: string,
    opacity: number,
    placement: any,
    styles: string[],
    hasBackground: boolean,
    bgColorHex: string,
    hasStroke: boolean,
    strokeWidth: number,
    strokeColorHex: string,
    hasShadow: boolean,
    shadowX: number,
    shadowY: number,
    shadowColorHex: string,
    shadowBlur: number,
    textAlign: string,
    index: number
  ): string {
    console.log(`生成装饰文字滤镜: 样式=${styles.join(", ")}`);

    // Base text filter
    let baseTextFilter =
      "drawtext=" +
      `text='${text}':` +
      `fontfile='${fontPath}':` +
      `fontsize=${fontSize}:` +
      `fontcolor=0x${colorHex}@${opacity}:` +
      `x=${placement.x}:` +
      `y=${placement.y}`;

    // Apply basic styles
    if (hasBackground) {
      baseTextFilter = this.applyBackground(
        baseTextFilter,
        placement.y,
        bgColorHex,
        opacity,
        fontSize
      );
    }

    if (hasStroke) {
      // FFmpeg描边渲染比fabric.js细，需要放大约1.5倍来匹配视觉效果
      const adjustedStrokeWidth = strokeWidth * 1.5;
      baseTextFilter += `:borderw=${adjustedStrokeWidth}:bordercolor=0x${strokeColorHex}@${opacity}`;
    }

    if (hasShadow) {
      baseTextFilter = this.applyShadow(
        baseTextFilter,
        shadowX,
        shadowY,
        shadowColorHex,
        shadowBlur,
        opacity
      );
    }

    baseTextFilter = this.applyTextAlignment(
      baseTextFilter,
      textAlign,
      placement.x
    );

    // For now, return the base text filter
    // TODO: Implement underline and strikethrough as separate drawing operations
    // This would require calculating text metrics and drawing lines

    console.log(`装饰文字滤镜生成完成，当前仅支持基础文字渲染`);
    console.log(`下划线和删除线功能需要额外的开发工作来实现`);

    return baseTextFilter;
  }

  /**
   * Determines the font file path based on font family and styles
   *
   * @param props Text properties
   * @returns Path to the font file
   */
  private determineFontPath(props: TextProperties): string {
    const fontFamily = props.fontFamily || "Arial";
    const text = props.text || "";
    const styles = props.styles || [];

    // 使用FontManager获取最佳字体路径
    return this.fontManager.getBestFontPath(
      text,
      fontFamily,
      styles,
      this.fontDir
    );
  }

  /**
   * Applies background to text
   *
   * @param filterString Current filter string
   * @param y Y position
   * @param bgColorHex Background color in hex
   * @param opacity Opacity value
   * @param fontSize Font size
   * @returns Updated filter string with background
   */
  private applyBackground(
    filterString: string,
    y: number,
    bgColorHex: string,
    opacity: number,
    fontSize: number
  ): string {
    const padding = 40; // Unified padding value

    // Adjust y position to account for padding
    filterString = filterString.replace(
      `y=${y}`,
      `y=${y}+${padding}` // Offset by padding distance
    );

    // Add box properties
    filterString +=
      `:box=1:boxcolor=0x${bgColorHex}@${opacity}:` +
      `boxborderw=${padding}:` +
      `boxh=${fontSize}`;

    return filterString;
  }

  /**
   * Applies shadow effect to text
   *
   * @param filterString Current filter string
   * @param shadowX X offset of shadow
   * @param shadowY Y offset of shadow
   * @param shadowColorHex Shadow color in hex
   * @param shadowBlur Blur amount (used for opacity adjustment, not actual blur)
   * @param opacity Base opacity
   * @returns Updated filter string with shadow
   */
  private applyShadow(
    filterString: string,
    shadowX: number,
    shadowY: number,
    shadowColorHex: string,
    shadowBlur: number,
    opacity: number
  ): string {
    // 添加阴影位置和颜色，但不使用模糊效果避免文字变模糊
    filterString += `:shadowx=${shadowX}:shadowy=${shadowY}:shadowcolor=0x${shadowColorHex}@${opacity}`;

    // 通过调整阴影透明度来模拟模糊效果，而不是实际的模糊
    // shadowBlur越大，阴影越透明，视觉上更像模糊的阴影
    if (shadowBlur > 0) {
      // 计算调整后的透明度 - shadowBlur越大，阴影越淡
      const blurOpacity = Math.max(
        0.1, // 最小透明度，确保阴影仍然可见
        Math.min(1, opacity * (1 - shadowBlur / 15)) // 使用15作为分母以获得更好的效果
      );
      filterString = filterString.replace(
        `shadowcolor=0x${shadowColorHex}@${opacity}`,
        `shadowcolor=0x${shadowColorHex}@${blurOpacity}`
      );
    }

    return filterString;
  }

  /**
   * Applies text styles like bold, italic, underline, strikethrough
   *
   * @param filterString Current filter string
   * @param styles Array of style strings
   * @returns Updated filter string with styles applied
   */
  private applyTextStyles(filterString: string, styles: string[]): string {
    // Note: Bold and italic are handled by font file selection in FontManager
    // Here we handle decorative styles that can be applied via FFmpeg parameters

    // Log the styles being applied for debugging
    if (styles.length > 0) {
      console.log(`应用文字样式: ${styles.join(", ")}`);
    }

    // FFmpeg drawtext filter doesn't support underline/strikethrough natively
    // We'll need to handle these with additional drawing operations
    // For now, we'll add metadata to track these styles

    return filterString;
  }

  /**
   * Applies text alignment
   *
   * @param filterString Current filter string
   * @param textAlign Alignment type (left, center, right)
   * @param x X position
   * @returns Updated filter string with alignment
   */
  private applyTextAlignment(
    filterString: string,
    textAlign: string,
    x: number
  ): string {
    if (textAlign === "center") {
      return filterString.replace(`x=${x}`, `x=(w-tw)/2+${x}`);
    } else if (textAlign === "right") {
      return filterString.replace(`x=${x}`, `x=w-tw-${x}`);
    }

    return filterString;
  }

  /**
   * Generates a filter for multiline text using multiple drawtext filters
   *
   * @param textProps Text properties
   * @param placement Placement information
   * @param opacity Element opacity
   * @param index Element index for labeling
   * @returns FFmpeg filter string for multiline text
   */
  private generateMultilineTextFilter(
    textProps: TextProperties,
    placement: any,
    opacity: number,
    index: number
  ): string {
    console.log(`生成多行文字滤镜: 文本包含换行符`);

    // Split text by newlines
    const lines = textProps.text!.split("\n");

    // Determine font path based on font family and styles
    const fontPath = this.determineFontPath(textProps);

    // Extract and normalize text styling properties
    const scaleX = placement.scaleX || 1;
    const scaleY = placement.scaleY || 1;
    const fontSize = Math.round((textProps.fontSize || 16) * scaleX);
    const fontColor = textProps.fontColor || "#ffffff";
    const colorHex = fontColor.replace("#", "");
    const textAlign = textProps.textAlign || "left";
    const styles = textProps.styles || [];

    // Calculate line height (use lineHeight property if available, otherwise default to 1.2)
    const lineHeight = (textProps.lineHeight || 1.2) * fontSize;

    // Background properties
    const hasBackground = Boolean(
      textProps.backgroundColor && textProps.backgroundColor !== ""
    );
    const bgColorHex =
      hasBackground && textProps.backgroundColor
        ? textProps.backgroundColor.replace("#", "")
        : "000000";

    // Stroke properties - 考虑缩放因子
    const hasStroke = Boolean(
      textProps.strokeWidth && textProps.strokeWidth > 0
    );
    const strokeWidth = hasStroke
      ? Math.round(textProps.strokeWidth! * scaleX)
      : 0;
    const strokeColorHex = textProps.strokeColor
      ? textProps.strokeColor.replace("#", "")
      : "000000";

    // Shadow properties - 考虑缩放因子
    const hasShadow = Boolean(textProps.shadowBlur && textProps.shadowBlur > 0);
    const shadowX = Math.round((textProps.shadowOffsetX || 0) * scaleX);
    const shadowY = Math.round((textProps.shadowOffsetY || 0) * scaleY);
    const shadowBlur = Math.round(
      (textProps.shadowBlur || 0) * Math.max(scaleX, scaleY)
    );
    const shadowColorHex = textProps.shadowColor
      ? textProps.shadowColor.replace("#", "")
      : "000000";

    // Generate filter for each line
    const lineFilters: string[] = [];

    lines.forEach((line, lineIndex) => {
      if (line.trim() === "") {
        // Skip empty lines but account for spacing
        return;
      }

      // Escape special characters for this line
      const escapedLine = this.escapeText(line);

      // Calculate Y position for this line
      const lineY = placement.y + lineIndex * lineHeight;

      // Build filter string for this line
      let lineFilter =
        "drawtext=" +
        `text='${escapedLine}':` +
        `fontfile='${fontPath}':` +
        `fontsize=${fontSize}:` +
        `fontcolor=0x${colorHex}@${opacity}:` +
        `x=${placement.x}:` +
        `y=${lineY}`;

      // Apply text styles
      lineFilter = this.applyTextStyles(lineFilter, styles);

      // Add background if specified (only for the first line to avoid overlap)
      if (hasBackground && lineIndex === 0) {
        lineFilter = this.applyBackground(
          lineFilter,
          lineY,
          bgColorHex,
          opacity,
          fontSize * lines.length + lineHeight * (lines.length - 1)
        );
      }

      // Add border/stroke if specified
      if (hasStroke) {
        // FFmpeg描边渲染比fabric.js细，需要放大约1.5倍来匹配视觉效果
        const adjustedStrokeWidth = strokeWidth * 1.5;
        lineFilter += `:borderw=${adjustedStrokeWidth}:bordercolor=0x${strokeColorHex}@${opacity}`;
      }

      // Add shadow if specified
      if (hasShadow) {
        lineFilter = this.applyShadow(
          lineFilter,
          shadowX,
          shadowY,
          shadowColorHex,
          shadowBlur,
          opacity
        );
      }

      // Apply text alignment
      lineFilter = this.applyTextAlignment(lineFilter, textAlign, placement.x);

      // Apply rotation if specified
      if (placement.rotation && placement.rotation !== 0) {
        lineFilter += `:box=1:boxcolor=0x00000000`;
        lineFilter += `:angle=${placement.rotation}`;
      }

      lineFilters.push(lineFilter);
    });

    // Combine all line filters
    const combinedFilter = lineFilters.join(",");

    console.log(`多行文字滤镜生成完成，共 ${lineFilters.length} 行`);

    return combinedFilter;
  }
}
