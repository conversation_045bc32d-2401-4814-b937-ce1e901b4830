import { FFmpegUtils } from "../utils";
import { MediaElement } from "../types";
import { ProcessingContext, ProcessingResult, ProgressTracker } from "./types";
import { BaseProcessor } from "./BaseProcessor";

/**
 * AudioProcessor handles the detection and processing of audio elements in the FFmpeg pipeline
 */
export class AudioProcessor extends BaseProcessor {
  /**
   * Detects which media elements contain audio tracks
   * @param elements - Array of media elements to check
   * @param taskId - Optional task ID for progress tracking
   * @param progressTracker - Optional progress tracker
   * @returns Array of booleans indicating which elements have audio
   */
  async detectAudioElements(
    elements: MediaElement[],
    taskId?: string,
    progressTracker?: ProgressTracker
  ): Promise<boolean[]> {
    const totalElements = elements.length;
    const audioElements: boolean[] = [];

    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      const hasAudio = await this.detectElementAudio(element);
      audioElements.push(hasAudio);

      if (taskId && progressTracker) {
        const progress = Math.round(((i + 1) / totalElements) * 30) + 10;
        progressTracker.updateCommandGenerationProgress(
          taskId,
          "detecting_audio_elements",
          progress
        );
      }
    }

    return audioElements;
  }

  /**
   * Detects if a single element contains audio
   * @param element - The media element to check
   * @returns True if the element has audio, false otherwise
   */
  private async detectElementAudio(element: MediaElement): Promise<boolean> {
    if (element.type === "audio") {
      return true;
    }

    if (element.type === "video") {
      return await FFmpegUtils.hasAudioTrack(element.properties.src as string);
    }

    return false;
  }

  /**
   * Process a media element and generate the appropriate FFmpeg filters
   * This is a placeholder implementation as AudioProcessor primarily handles detection
   * @param element - The media element to process
   * @param context - The processing context
   * @returns The processing result
   */
  protected processElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    // This is a placeholder implementation as AudioProcessor primarily handles detection
    return {
      lastVideoLabel: context.lastVideoLabel,
      audioStreamCount: context.audioStreamCount,
    };
  }
}
