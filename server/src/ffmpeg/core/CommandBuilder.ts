import { FFmpegUtils } from "../utils";
import { FFMPEG_CONFIG } from "../config";
import { OutputFormat, MediaElement } from "../types";
import * as os from "os";
import * as fs from "fs";
import * as path from "path";

/**
 * CommandBuilder is responsible for constructing FFmpeg command strings
 * from the processed filter chains and input sources
 */
export class CommandBuilder {
  /** Default output directory */
  private outputDir: string = process.env.OUTPUT_DIR || "./output";

  /** Whether to use hardware acceleration if available */
  private useHardwareAcceleration: boolean =
    FFMPEG_CONFIG.HW_ACCELERATION.enabled;

  /** 存储当前的滤镜复杂链，用于检测转场效果 */
  private currentFilterComplex: string[] = [];
  /**
   * Builds the final FFmpeg command string from all components
   * @param inputs - Array of input source commands
   * @param filterComplex - Array of video filter commands
   * @param audioFilters - Array of audio filter commands
   * @param lastVideoLabel - Label of the last video stream
   * @param outputFormat - Output format configuration
   * @returns Complete FFmpeg command string
   */
  buildFinalCommand(
    inputs: string[],
    filterComplex: string[],
    audioFilters: string[],
    lastVideoLabel: string,
    outputFormat: OutputFormat,
    backgroundColor?: string
  ): string {
    // 保存当前的滤镜复杂链，用于检测转场效果
    this.currentFilterComplex = [...filterComplex];

    // 检测是否为MP3音频导出
    const isAudioOnlyExport = outputFormat.format.toLowerCase() === "mp3";

    if (isAudioOnlyExport) {
      console.log("检测到MP3格式，生成纯音频导出命令");
      return this.buildAudioOnlyCommand(inputs, audioFilters, outputFormat);
    }

    // 检测是否存在复杂转场效果
    const hasTransitions = this.hasComplexTransitions();
    if (hasTransitions) {
      console.log("检测到复杂转场效果，将使用软件编码以保证质量");
    }

    // Optimize filter chains before building command
    const optimizedFilterComplex = this.optimizeFilterChain(filterComplex);

    const hasAudio = audioFilters.length > 0;
    const commandParts = this.buildCommandParts(
      inputs,
      optimizedFilterComplex,
      audioFilters,
      lastVideoLabel,
      outputFormat,
      hasAudio,
      backgroundColor
    );

    const finalCommand = commandParts.filter(Boolean).join(" ");
    console.log("FFmpeg command:", finalCommand);
    return finalCommand;
  }

  /**
   * Calculates the total duration of the video in seconds
   * @param elements - Array of media elements
   * @param captions - Optional array of captions
   * @returns Duration in seconds, or default duration if no elements and captions
   */
  calculateVideoDuration(
    elements: MediaElement[],
    captions?: { endTime: string }[]
  ): number {
    // 默认最小持续时间（如果没有元素和字幕）
    const DEFAULT_DURATION = 2;

    if (!elements.length && (!captions || !captions.length)) {
      return DEFAULT_DURATION;
    }

    // 计算媒体元素的最大结束时间
    let maxEndTimeMs = elements.length
      ? Math.max(...elements.map((el) => el.timeFrame.end))
      : 0;

    // 如果有字幕，计算字幕的最大结束时间
    if (captions && captions.length > 0) {
      // 将字幕的结束时间从HH:MM:SS格式转换为毫秒
      const captionEndTimesMs = captions.map((caption) => {
        const [hours, minutes, seconds] = caption.endTime
          .split(":")
          .map(Number);
        return (hours * 3600 + minutes * 60 + seconds) * 1000;
      });

      // 计算字幕的最大结束时间
      const maxCaptionEndTimeMs = Math.max(...captionEndTimesMs);

      // 取媒体元素和字幕的最大结束时间
      maxEndTimeMs = Math.max(maxEndTimeMs, maxCaptionEndTimeMs);
    }

    // 确保至少有最小持续时间
    if (maxEndTimeMs === 0) {
      return DEFAULT_DURATION;
    }

    // 转换为秒
    return maxEndTimeMs / 1000;
  }

  /**
   * Gets the default output format configuration
   * @returns Default output format
   */
  getDefaultOutputFormat(): OutputFormat {
    return FFMPEG_CONFIG.DEFAULT_OUTPUT_FORMAT;
  }

  /**
   * Builds FFmpeg command for audio-only export (MP3)
   * @param inputs - Array of input source commands
   * @param audioFilters - Array of audio filter commands
   * @param outputFormat - Output format configuration
   * @returns Complete FFmpeg command string for audio export
   */
  private buildAudioOnlyCommand(
    inputs: string[],
    audioFilters: string[],
    outputFormat: OutputFormat
  ): string {
    // Generate unique output filename with timestamp
    const timestamp = FFmpegUtils.generateTimestamp();

    // 确保输出目录存在
    if (!fs.existsSync(this.outputDir)) {
      try {
        fs.mkdirSync(this.outputDir, { recursive: true });
        console.log(`Created output directory: ${this.outputDir}`);
      } catch (error) {
        console.error(`Error creating output directory: ${error}`);
      }
    }

    // 使用绝对路径
    let outputDirAbsolute = this.outputDir;
    if (!path.isAbsolute(outputDirAbsolute)) {
      outputDirAbsolute = path.resolve(process.cwd(), outputDirAbsolute);
    }

    // 完整的输出文件路径
    const outputFilename = path.join(
      outputDirAbsolute,
      `output_${timestamp}.${outputFormat.format}`
    );

    console.log(`Audio output file will be: ${outputFilename}`);

    // 如果没有音频内容，创建静音音频
    if (audioFilters.length === 0) {
      console.log("没有音频内容，创建静音MP3文件");
      return [
        "ffmpeg",
        "-f lavfi",
        "-i anullsrc=channel_layout=stereo:sample_rate=44100",
        "-t 1", // 1秒静音
        "-c:a mp3",
        "-b:a 128k",
        outputFilename,
      ].join(" ");
    }

    // 构建音频混合滤镜
    const audioMixFilter = this.buildAudioMixFilter(audioFilters);

    // 构建纯音频命令
    const commandParts = [
      "ffmpeg",
      "-threads auto",
      "-thread_queue_size 512",
      ...inputs,
      "-filter_complex",
      `"${audioMixFilter}"`,
      '-map "[aout]"',
      "-c:a mp3",
      "-b:a 128k", // 128kbps音频比特率
      "-ar 44100", // 44.1kHz采样率
      outputFilename,
    ];

    const finalCommand = commandParts.filter(Boolean).join(" ");
    console.log("Audio-only FFmpeg command:", finalCommand);
    return finalCommand;
  }

  /**
   * Builds the individual parts of the FFmpeg command
   * @param inputs - Array of input source commands
   * @param filterComplex - Array of video filter commands
   * @param audioFilters - Array of audio filter commands
   * @param lastVideoLabel - Label of the last video stream
   * @param outputFormat - Output format configuration
   * @param hasAudio - Whether the output has audio
   * @param backgroundColor - Background color to determine pixel format
   * @returns Array of command parts
   */
  private buildCommandParts(
    inputs: string[],
    filterComplex: string[],
    audioFilters: string[],
    lastVideoLabel: string,
    outputFormat: OutputFormat,
    hasAudio: boolean,
    backgroundColor?: string
  ): string[] {
    // Add audio mixing if we have audio filters
    if (hasAudio) {
      this.addAudioMix(filterComplex, audioFilters);
    }

    // Get encoding parameters based on quality setting
    const encodingParams = FFmpegUtils.getEncodingParams(outputFormat.quality);

    // Generate unique output filename with timestamp
    const timestamp = FFmpegUtils.generateTimestamp();

    // 确保输出目录存在
    if (!fs.existsSync(this.outputDir)) {
      try {
        fs.mkdirSync(this.outputDir, { recursive: true });
        console.log(`Created output directory: ${this.outputDir}`);
      } catch (error) {
        console.error(`Error creating output directory: ${error}`);
      }
    }

    // 使用绝对路径
    let outputDirAbsolute = this.outputDir;
    if (!path.isAbsolute(outputDirAbsolute)) {
      outputDirAbsolute = path.resolve(process.cwd(), outputDirAbsolute);
    }

    // 完整的输出文件路径
    const outputFilename = path.join(
      outputDirAbsolute,
      `output_${timestamp}.${outputFormat.format}`
    );

    console.log(`Output file will be: ${outputFilename}`);

    // Determine optimal thread count based on CPU cores
    const cpuCount = os.cpus().length;
    const threadCount = Math.max(1, Math.min(cpuCount - 1, 16)); // Leave one core free, max 16 threads

    // Check for hardware acceleration
    const hwAccel = this.getHardwareAccelerationOptions(outputFormat.codec);

    // 获取硬件加速选项
    const hwAccelOptions = this.useHardwareAcceleration ? hwAccel : [];

    // 获取硬件加速编码器（如果可用）
    const videoCodec = this.useHardwareAcceleration
      ? this.getHwAccelCodec(outputFormat.codec)
      : outputFormat.codec;

    // 根据是否使用硬件加速调整编码参数
    const encoderOptions = [];

    // 检查是否有复杂转场
    const hasTransitions = this.hasComplexTransitions();

    // 如果使用硬件加速，某些参数可能需要调整
    if (this.useHardwareAcceleration && videoCodec !== outputFormat.codec) {
      // 不同的硬件编码器有不同的参数要求
      if (videoCodec.includes("nvenc")) {
        // NVIDIA 编码器参数
        // 如果有转场效果，使用更高质量的参数
        if (hasTransitions) {
          // 高质量模式
          encoderOptions.push(`-preset p7`); // 最高质量预设
          encoderOptions.push(`-rc vbr_hq`); // 高质量VBR模式
          encoderOptions.push(`-cq 15`); // 更低的CQ值意味着更高质量
          encoderOptions.push(`-qmin 1`); // 最小量化参数
          encoderOptions.push(`-qmax 20`); // 最大量化参数
          encoderOptions.push(`-b:v 0`); // 使用CQ模式
          encoderOptions.push(`-spatial-aq 1`); // 启用空间自适应量化
          encoderOptions.push(`-temporal-aq 1`); // 启用时间自适应量化
          encoderOptions.push(`-aq-strength 15`); // 自适应量化强度
          encoderOptions.push(`-bf 4`); // 增加B帧数量
          encoderOptions.push(`-b_ref_mode each`); // 每个B帧都可以作为参考
        } else {
          // 标准模式
          encoderOptions.push(
            `-preset ${
              encodingParams.preset === "slow"
                ? "p7"
                : encodingParams.preset === "medium"
                ? "p4"
                : "p1"
            }`
          );
          encoderOptions.push(`-b:v 0`); // 使用 VBR 模式
          encoderOptions.push(`-cq ${encodingParams.crf}`); // 质量参数
        }
      } else if (videoCodec.includes("qsv")) {
        // Intel QuickSync 参数
        if (hasTransitions) {
          // 高质量模式
          encoderOptions.push(`-preset veryslow`); // 最高质量预设
          encoderOptions.push(`-global_quality 15`); // 更低的值意味着更高质量
          encoderOptions.push(`-look_ahead 1`); // 启用前瞻
          encoderOptions.push(`-look_ahead_depth 60`); // 增加前瞻深度
        } else {
          // 标准模式
          encoderOptions.push(
            `-preset ${
              encodingParams.preset === "slow"
                ? "veryslow"
                : encodingParams.preset === "medium"
                ? "medium"
                : "veryfast"
            }`
          );
          encoderOptions.push(`-global_quality ${encodingParams.crf * 2}`); // Intel 使用不同的质量范围
        }
      } else if (videoCodec.includes("videotoolbox")) {
        // Apple VideoToolbox 参数
        if (hasTransitions) {
          // 高质量模式
          encoderOptions.push(`-q:v 20`); // 更低的值意味着更高质量 (0-100)
          encoderOptions.push(`-allow_sw 1`); // 允许软件处理复杂部分
          encoderOptions.push(`-realtime 0`); // 禁用实时模式以提高质量
        } else {
          // 标准模式
          encoderOptions.push(`-q:v ${Math.min(100, encodingParams.crf * 2)}`); // 质量参数 (0-100)
        }
      } else if (videoCodec.includes("vaapi")) {
        // VAAPI 参数
        if (hasTransitions) {
          // 高质量模式
          encoderOptions.push(`-global_quality 15`); // 更低的值意味着更高质量
          encoderOptions.push(`-compression_level 1`); // 最低压缩级别（最高质量）
          encoderOptions.push(`-quality best`); // 最高质量
        } else {
          // 标准模式
          encoderOptions.push(`-global_quality ${encodingParams.crf * 2}`);
          encoderOptions.push(
            `-compression_level ${
              encodingParams.preset === "slow"
                ? 1
                : encodingParams.preset === "medium"
                ? 4
                : 7
            }`
          );
        }
      } else if (videoCodec.includes("amf")) {
        // AMD AMF 参数
        if (hasTransitions) {
          // 高质量模式
          encoderOptions.push(`-quality quality`); // 质量优先
          encoderOptions.push(`-rc cqp`); // 恒定质量模式
          encoderOptions.push(`-qp_i 15`); // I帧量化参数
          encoderOptions.push(`-qp_p 17`); // P帧量化参数
          encoderOptions.push(`-preanalysis 1`); // 启用预分析
          encoderOptions.push(`-vbaq 1`); // 启用基于方差的自适应量化
        } else {
          // 标准模式
          encoderOptions.push(
            `-quality ${
              encodingParams.preset === "slow"
                ? "quality"
                : encodingParams.preset === "medium"
                ? "balanced"
                : "speed"
            }`
          );
          encoderOptions.push(`-rc cqp`); // 恒定质量模式
          encoderOptions.push(`-qp_i ${encodingParams.crf}`);
          encoderOptions.push(`-qp_p ${encodingParams.crf + 2}`);
        }
      } else {
        // 默认软件编码参数
        encoderOptions.push(`-preset ${encodingParams.preset}`);
        encoderOptions.push(`-crf ${encodingParams.crf}`);
        encoderOptions.push(`-profile:v ${encodingParams.profile}`);
      }
    } else {
      // 软件编码使用标准参数
      encoderOptions.push(`-preset ${encodingParams.preset}`);
      encoderOptions.push(`-crf ${encodingParams.crf}`);
      encoderOptions.push(`-profile:v ${encodingParams.profile}`);
    }

    // 构建最终命令
    return [
      "ffmpeg",
      "-threads auto",
      "-thread_queue_size 512", // 增加线程队列大小
      ...hwAccelOptions, // 添加硬件加速选项
      ...inputs,
      "-filter_complex",
      `"${filterComplex.join(";")}"`,
      `-map "[${lastVideoLabel}]"`,
      hasAudio ? '-map "[aout]"' : "",
      `-c:v ${videoCodec}`, // 使用可能的硬件加速编码器
      ...encoderOptions, // 根据编码器类型使用不同的编码参数
      // Use appropriate pixel format based on background transparency
      backgroundColor === "transparent"
        ? "-pix_fmt yuva420p"
        : "-pix_fmt yuv420p",
      hasAudio ? "-c:a aac -b:a 128k" : "", // 指定音频比特率
      `-r ${outputFormat.frameRate}`,
      // "-shortest",
      "-movflags +faststart",
      "-max_muxing_queue_size 1024", // 增加复用队列大小
      outputFilename,
    ];
  }

  /**
   * Builds audio mix filter for audio-only export
   * @param audioFilters - Array of audio filter commands
   * @returns Audio mix filter string
   */
  private buildAudioMixFilter(audioFilters: string[]): string {
    if (audioFilters.length === 0) {
      return "";
    }

    const filterParts: string[] = [];

    // 处理多个音频流的情况
    if (audioFilters.length > 1) {
      // 首先添加所有音频过滤器
      for (let i = 0; i < audioFilters.length; i++) {
        filterParts.push(audioFilters[i]);
      }

      // 创建音频输入标签数组
      const audioInputs = Array.from(
        { length: audioFilters.length },
        (_, i) => `[a${i}]`
      ).join("");

      // 添加混音命令
      filterParts.push(
        `${audioInputs}amix=inputs=${audioFilters.length}:duration=longest,aresample=async=1000[aout]`
      );
    } else if (audioFilters.length === 1) {
      // 如果只有一个音频流，直接修改其输出标签为[aout]
      let filter = audioFilters[0];
      const lastBracketIndex = filter.lastIndexOf("[");

      if (lastBracketIndex !== -1) {
        // 替换最后的输出标签为[aout]
        filter = filter.substring(0, lastBracketIndex) + "[aout]";
        filterParts.push(filter);
      } else {
        filterParts.push(filter);
      }
    }

    return filterParts.join(";");
  }

  /**
   * Adds audio mixing filters to the filter complex
   * @param filterComplex - Array of filter commands to append to
   * @param audioFilters - Array of audio filter commands
   */
  private addAudioMix(filterComplex: string[], audioFilters: string[]): void {
    if (audioFilters.length === 0) {
      return;
    }

    // 处理多个音频流的情况
    if (audioFilters.length > 1) {
      // 首先添加所有音频过滤器，但保持它们的原始输出标签
      for (let i = 0; i < audioFilters.length; i++) {
        filterComplex.push(audioFilters[i]);
      }

      // 如果有多个音频流，我们需要混合它们
      // 创建音频输入标签数组
      const audioInputs = Array.from(
        { length: audioFilters.length },
        (_, i) => `[a${i}]`
      ).join("");

      // 添加混音命令
      // 注意：我们不再在这里添加asetpts，因为每个音频流已经有了自己的时间戳
      // 这样可以确保分割视频的音频在混音后仍然保持正确的时间戳
      filterComplex.push(
        `${audioInputs}amix=inputs=${audioFilters.length}:duration=longest,aresample=async=1000[aout]`
      );
    } else if (audioFilters.length === 1) {
      // 如果只有一个音频流，直接修改其输出标签为[aout]
      let filter = audioFilters[0];
      const lastBracketIndex = filter.lastIndexOf("[");

      if (lastBracketIndex !== -1) {
        // 替换最后的输出标签为[aout]
        filter = filter.substring(0, lastBracketIndex) + "[aout]";
        filterComplex.push(filter);
      } else {
        // 如果没有找到输出标签，直接添加
        filterComplex.push(filter + "[aout]");
      }
    }
  }

  /**
   * Optimizes the filter chain by grouping similar operations
   * @param filters - Array of filter commands
   * @returns Optimized array of filter commands
   */
  private optimizeFilterChain(filters: string[]): string[] {
    // Group filters by type for more efficient processing
    const grouped: { [key: string]: string[] } = {
      scale: [],
      rotate: [],
      colorspace: [],
      effects: [],
      overlay: [],
    };

    // Sort filters into groups
    filters.forEach((filter) => {
      if (filter.includes("scale=")) {
        grouped.scale.push(filter);
      } else if (filter.includes("rotate=")) {
        grouped.rotate.push(filter);
      } else if (filter.includes("format=") || filter.includes("colorspace=")) {
        grouped.colorspace.push(filter);
      } else if (filter.includes("overlay=")) {
        grouped.overlay.push(filter);
      } else {
        grouped.effects.push(filter);
      }
    });

    // Return filters in optimized order
    return [
      ...grouped.scale,
      ...grouped.rotate,
      ...grouped.colorspace,
      ...grouped.effects,
      ...grouped.overlay,
    ];
  }

  /**
   * Gets hardware acceleration options based on available hardware
   * @param codec - The requested codec
   * @returns Array of hardware acceleration command options
   */
  private getHardwareAccelerationOptions(codec: string): string[] {
    if (!this.useHardwareAcceleration) {
      console.log("硬件加速已禁用，使用软件编码");
      return [];
    }

    // 获取最佳的硬件加速器类型
    const acceleratorType = FFMPEG_CONFIG.HW_ACCELERATION.getBestAccelerator();
    if (!acceleratorType) {
      return [];
    }

    // 根据不同的硬件加速器类型返回相应的命令选项
    switch (acceleratorType) {
      case "nvidia":
        return ["-hwaccel cuda", "-hwaccel_output_format cuda"];
      case "apple":
        return ["-hwaccel videotoolbox"];
      case "intel":
        return ["-hwaccel qsv", "-hwaccel_output_format qsv"];
      case "amd":
        return ["-hwaccel amf"];
      case "vaapi":
        return [
          "-hwaccel vaapi",
          "-hwaccel_device /dev/dri/renderD128",
          "-hwaccel_output_format vaapi",
        ];
      default:
        return [];
    }
  }

  /**
   * Gets the appropriate hardware-accelerated codec name
   * @param codec - The base codec name
   * @returns Hardware-accelerated codec name if available, or the original codec
   */
  private getHwAccelCodec(codec: string): string {
    if (!this.useHardwareAcceleration) {
      return codec;
    }

    // 获取最佳的硬件加速器类型
    const acceleratorType = FFMPEG_CONFIG.HW_ACCELERATION.getBestAccelerator();
    if (!acceleratorType) {
      return codec;
    }

    // 检查是否有转场效果
    const hasTransitions = this.hasComplexTransitions();

    // 根据转场处理策略决定是否使用硬件加速
    if (hasTransitions) {
      const strategy = FFMPEG_CONFIG.HW_ACCELERATION.transitionStrategy;

      if (strategy === "auto" || strategy === "software") {
        console.log("检测到复杂转场效果，根据策略使用软件编码以保证质量");
        return codec; // 使用软件编码以保证转场质量
      } else if (strategy === "hardware") {
        console.log("检测到复杂转场效果，根据策略使用高质量硬件编码参数");
        // 继续使用硬件编码，但会在后续使用更高质量的参数
      }
    }

    // 根据不同的硬件加速器类型和原始编码器选择对应的硬件加速编码器
    const hwCodecMap: Record<string, Record<string, string>> = {
      nvidia: {
        libx264: "h264_nvenc",
        libx265: "hevc_nvenc",
      },
      apple: {
        libx264: "h264_videotoolbox",
        libx265: "hevc_videotoolbox",
      },
      intel: {
        libx264: "h264_qsv",
        libx265: "hevc_qsv",
      },
      amd: {
        libx264: "h264_amf",
        libx265: "hevc_amf",
      },
      vaapi: {
        libx264: "h264_vaapi",
        libx265: "hevc_vaapi",
      },
    };

    // 如果找到对应的硬件加速编码器，则使用它
    if (hwCodecMap[acceleratorType] && hwCodecMap[acceleratorType][codec]) {
      const hwCodec = hwCodecMap[acceleratorType][codec];
      console.log(`使用硬件加速编码器: ${hwCodec} (替代 ${codec})`);
      return hwCodec;
    }

    // 如果没有找到对应的硬件加速编码器，则使用原始编码器
    console.log(`没有找到对应的硬件加速编码器，使用原始编码器: ${codec}`);
    return codec;
  }

  /**
   * 检查是否存在复杂的转场效果
   * @returns 是否存在复杂转场效果
   */
  private hasComplexTransitions(): boolean {
    // 检查filterComplex中是否包含特定的转场相关滤镜关键字
    const transitionKeywords = [
      "xfade",
      "fade",
      "dissolve",
      "wipe",
      "slide",
      "push",
      "cover",
      "uncover",
      "blend",
      "overlay=",
      "displace",
      "zoompan",
      "crop=",
      "scale=",
      "rotate=",
      "transpose=",
    ];

    // 将所有滤镜字符串连接起来，便于检索
    const filterString = this.currentFilterComplex.join(";");

    // 检查是否包含任何转场关键字
    for (const keyword of transitionKeywords) {
      if (filterString.includes(keyword)) {
        console.log(`检测到转场关键字: ${keyword}`);

        // 特殊处理：某些关键字可能是常见操作而非转场
        // 例如简单的overlay可能不需要软件编码
        if (
          keyword === "overlay=" &&
          !filterString.includes("overlay=enable=")
        ) {
          // 简单的叠加可能不需要软件编码
          continue;
        }

        // 简单的缩放和裁剪可能不需要软件编码
        if (
          (keyword === "scale=" || keyword === "crop=") &&
          !filterString.includes("scale=w=") &&
          !filterString.includes("crop=w=")
        ) {
          continue;
        }

        return true;
      }
    }

    // 检查是否有多个元素叠加（可能表示复杂场景）
    const overlayCount = (filterString.match(/overlay/g) || []).length;
    if (overlayCount > 2) {
      console.log(`检测到多个叠加操作: ${overlayCount}个`);
      return true;
    }

    // 检查是否有多个元素（可能表示复杂场景）
    if (this.currentFilterComplex.length > 10) {
      console.log(
        `检测到复杂滤镜链: ${this.currentFilterComplex.length}个滤镜`
      );
      return true;
    }

    console.log("未检测到复杂转场效果，可以使用硬件加速编码");
    return false;
  }
}
