import fs from "fs/promises";
import path from "path";
import logger from "../utils/logger";

/**
 * 初始化模板数据
 * 如果模板文件不存在，则从示例模板创建
 */
export async function initializeTemplates() {
  const templatesDir = path.join(process.cwd(), "data", "templates");
  const templatesFile = path.join(templatesDir, "templates.json");
  const sampleTemplatesFile = path.join(templatesDir, "sample-templates.json");
  const thumbnailsDir = path.join(templatesDir, "thumbnails");

  try {
    // 创建必要的目录
    await fs.mkdir(templatesDir, { recursive: true });
    await fs.mkdir(thumbnailsDir, { recursive: true });

    // 检查模板文件是否存在
    try {
      await fs.access(templatesFile);
      logger.info("模板文件已存在，跳过初始化");
      return;
    } catch {
      // 文件不存在，继续初始化
    }

    // 检查示例模板文件是否存在
    try {
      await fs.access(sampleTemplatesFile);

      // 复制示例模板到正式模板文件
      const sampleData = await fs.readFile(sampleTemplatesFile, "utf-8");
      await fs.writeFile(templatesFile, sampleData);

      logger.info("已从示例模板初始化模板数据");

      // 创建示例缩略图（占位符）
      await createPlaceholderThumbnails();
    } catch {
      // 示例文件也不存在，创建空的模板数组
      await fs.writeFile(templatesFile, JSON.stringify([], null, 2));
      logger.info("已创建空的模板文件");
    }
  } catch (error) {
    logger.error("初始化模板失败:", error);
    throw error;
  }
}

/**
 * 创建占位符缩略图
 */
async function createPlaceholderThumbnails() {
  const thumbnailsDir = path.join(
    process.cwd(),
    "data",
    "templates",
    "thumbnails"
  );

  const placeholderThumbnails = [
    "business-intro.jpg",
    "social-story.jpg",
    "education-course.jpg",
    "product-promo.jpg",
  ];

  for (const thumbnail of placeholderThumbnails) {
    const thumbnailPath = path.join(thumbnailsDir, thumbnail);

    try {
      await fs.access(thumbnailPath);
      // 文件已存在，跳过
    } catch {
      // 创建简单的占位符文件
      // 在实际应用中，这里应该是真实的图片文件
      await fs.writeFile(thumbnailPath, "placeholder-image-data");
      logger.info(`已创建占位符缩略图: ${thumbnail}`);
    }
  }
}

/**
 * 重置模板数据（开发用）
 */
export async function resetTemplates() {
  const templatesDir = path.join(process.cwd(), "data", "templates");
  const templatesFile = path.join(templatesDir, "templates.json");
  const sampleTemplatesFile = path.join(templatesDir, "sample-templates.json");

  try {
    // 检查示例模板文件是否存在
    await fs.access(sampleTemplatesFile);

    // 复制示例模板到正式模板文件
    const sampleData = await fs.readFile(sampleTemplatesFile, "utf-8");
    await fs.writeFile(templatesFile, sampleData);

    logger.info("已重置模板数据");
  } catch (error) {
    logger.error("重置模板失败:", error);
    throw error;
  }
}

// 如果直接运行此脚本，执行初始化
if (require.main === module) {
  initializeTemplates()
    .then(() => {
      console.log("模板初始化完成");
      process.exit(0);
    })
    .catch((error) => {
      console.error("模板初始化失败:", error);
      process.exit(1);
    });
}
