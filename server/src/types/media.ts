/**
 * 媒体文件相关的类型定义
 */

/**
 * 媒体元数据接口
 */
export interface MediaMetadata {
  /** 唯一标识符 */
  id: string;
  /** 文件所有者的用户ID */
  userId: string;
  /** 原始文件名 */
  fileName: string;
  /** S3对象键 */
  fileKey: string;
  /** MIME类型 */
  fileType: string;
  /** 文件大小（字节） */
  fileSize: number;
  /** 上传日期 */
  uploadDate: Date;
  /** 公共访问URL（如果适用） */
  url: string;
  /** 缩略图URL */
  thumbnailUrl?: string;
  /** 图片/视频宽度 */
  width?: number;
  /** 图片/视频高度 */
  height?: number;
  /** 音频/视频时长（秒） */
  duration?: number;
  /** 处理状态 */
  status: "processing" | "ready" | "error";
  /** 错误信息（如果有） */
  errorMessage?: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 签名URL请求接口
 */
export interface SignedUrlRequest {
  /** 原始文件名 */
  fileName: string;
  /** MIME类型 */
  fileType: string;
  /** 文件大小（字节） */
  fileSize: number;
  /** 用户ID（从认证中获取） */
  userId?: string;
}

/**
 * 签名URL响应接口
 */
export interface SignedUrlResponse {
  /** 签名URL */
  url: string;
  /** S3对象键 */
  fileKey: string;
  /** POST上传的额外字段 */
  fields?: {
    [key: string]: string;
  };
  /** 过期时间戳 */
  expiresAt: number;
  /** 上传方法 */
  method: "PUT" | "POST";
}

/**
 * 上传完成通知接口
 */
export interface UploadCompleteRequest {
  /** S3对象键 */
  fileKey: string;
  /** 原始文件名 */
  fileName: string;
  /** MIME类型 */
  fileType: string;
  /** 文件大小 */
  fileSize: number;
  /** 额外的元数据 */
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    [key: string]: any;
  };
}

/**
 * 媒体列表查询参数
 */
export interface MediaListQuery {
  /** 页码（从1开始） */
  page?: number;
  /** 每页数量 */
  limit?: number;
  /** 文件类型过滤 */
  fileType?: string;
  /** 搜索关键词 */
  search?: string;
  /** 排序字段 */
  sortBy?: "uploadDate" | "fileName" | "fileSize";
  /** 排序方向 */
  sortOrder?: "asc" | "desc";
  /** 状态过滤 */
  status?: "processing" | "ready" | "error";
}

/**
 * 媒体列表响应接口
 */
export interface MediaListResponse {
  /** 媒体文件列表 */
  items: MediaMetadata[];
  /** 总数量 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 总页数 */
  totalPages: number;
  /** 是否有下一页 */
  hasNext: boolean;
  /** 是否有上一页 */
  hasPrev: boolean;
}

/**
 * 多部分上传初始化请求
 */
export interface MultipartUploadRequest {
  /** 文件名 */
  fileName: string;
  /** 文件类型 */
  fileType: string;
  /** 文件大小 */
  fileSize: number;
  /** 分片大小（可选，默认5MB） */
  partSize?: number;
}

/**
 * 多部分上传初始化响应
 */
export interface MultipartUploadResponse {
  /** 上传ID */
  uploadId: string;
  /** S3对象键 */
  fileKey: string;
  /** 分片大小 */
  partSize: number;
  /** 总分片数 */
  totalParts: number;
}

/**
 * 分片上传URL请求
 */
export interface PartUploadUrlRequest {
  /** 上传ID */
  uploadId: string;
  /** S3对象键 */
  fileKey: string;
  /** 分片号（从1开始） */
  partNumber: number;
}

/**
 * 分片上传URL响应
 */
export interface PartUploadUrlResponse {
  /** 签名URL */
  url: string;
  /** 分片号 */
  partNumber: number;
  /** 过期时间 */
  expiresAt: number;
}

/**
 * 完成多部分上传请求
 */
export interface CompleteMultipartUploadRequest {
  /** 上传ID */
  uploadId: string;
  /** S3对象键 */
  fileKey: string;
  /** 分片信息 */
  parts: Array<{
    partNumber: number;
    etag: string;
  }>;
  /** 文件元数据 */
  metadata?: {
    fileName: string;
    fileType: string;
    fileSize: number;
    width?: number;
    height?: number;
    duration?: number;
  };
}

/**
 * 用户存储配额信息
 */
export interface UserStorageQuota {
  /** 用户ID */
  userId: string;
  /** 已使用存储空间（字节） */
  usedStorage: number;
  /** 存储配额限制（字节） */
  storageLimit: number;
  /** 文件数量限制 */
  fileCountLimit: number;
  /** 当前文件数量 */
  currentFileCount: number;
  /** 最后更新时间 */
  lastUpdated: Date;
}

/**
 * 缩略图生成任务
 */
export interface ThumbnailTask {
  /** 任务ID */
  taskId: string;
  /** 媒体ID */
  mediaId: string;
  /** S3对象键 */
  fileKey: string;
  /** 文件类型 */
  fileType: string;
  /** 缩略图尺寸 */
  sizes: string[];
  /** 任务状态 */
  status: "pending" | "processing" | "completed" | "failed";
  /** 创建时间 */
  createdAt: Date;
  /** 完成时间 */
  completedAt?: Date;
  /** 错误信息 */
  error?: string;
}

/**
 * 文件验证错误类型
 */
export interface FileValidationError {
  /** 错误代码 */
  code: "FILE_TOO_LARGE" | "INVALID_FILE_TYPE" | "QUOTA_EXCEEDED" | "INVALID_FILE_NAME";
  /** 错误消息 */
  message: string;
  /** 相关的值 */
  value?: any;
  /** 限制值 */
  limit?: any;
}
