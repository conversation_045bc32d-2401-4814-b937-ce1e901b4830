import express from "express";
import multer from "multer";
import path from "path";
import { TemplateController } from "../controllers/TemplateController";
import logger from "../utils/logger";

const router = express.Router();
const templateController = new TemplateController();

// 配置 multer 用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(
      process.cwd(),
      "data",
      "templates",
      "thumbnails"
    );
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, `template-${uniqueSuffix}${ext}`);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
  fileFilter: (req, file, cb) => {
    // 只允许图片文件
    if (file.mimetype.startsWith("image/")) {
      cb(null, true);
    } else {
      cb(new Error("Only image files are allowed"));
    }
  },
});

// 验证中间件
const validateTemplateData = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
): void => {
  const { name, canvasState } = req.body;

  if (!name || typeof name !== "string" || name.trim().length === 0) {
    res.status(400).json({ error: "Template name is required" });
    return;
  }

  if (!canvasState || typeof canvasState !== "object") {
    res.status(400).json({ error: "Canvas state is required" });
    return;
  }

  // 验证画布状态的基本结构
  if (!canvasState.width || !canvasState.height) {
    res.status(400).json({ error: "Canvas width and height are required" });
    return;
  }

  if (!Array.isArray(canvasState.elements)) {
    res.status(400).json({ error: "Canvas elements must be an array" });
    return;
  }

  next();
};

// 错误处理中间件
const handleMulterError = (
  error: any,
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
): void => {
  if (error instanceof multer.MulterError) {
    if (error.code === "LIMIT_FILE_SIZE") {
      res.status(400).json({ error: "File too large. Maximum size is 5MB." });
      return;
    }
    res.status(400).json({ error: error.message });
    return;
  }

  if (error.message === "Only image files are allowed") {
    res
      .status(400)
      .json({ error: "Only image files are allowed for thumbnails" });
    return;
  }

  next(error);
};

// 路由定义

/**
 * @route GET /api/templates
 * @desc 获取模板列表
 * @query {number} page - 页码 (默认: 1)
 * @query {number} limit - 每页数量 (默认: 20)
 * @query {string} category - 分类过滤
 * @query {string} tags - 标签过滤 (逗号分隔)
 * @query {boolean} hasVideo - 是否包含视频
 * @query {boolean} hasAudio - 是否包含音频
 * @query {boolean} hasText - 是否包含文本
 * @query {boolean} hasAnimations - 是否包含动画
 * @query {number} durationMin - 最小时长 (毫秒)
 * @query {number} durationMax - 最大时长 (毫秒)
 * @query {string} complexity - 复杂度 (simple|medium|complex)
 * @query {string} aspectRatio - 宽高比
 * @query {string} sortBy - 排序方式 (popular|newest|rating|name)
 * @query {string} sortOrder - 排序顺序 (asc|desc)
 */
router.get("/", async (req, res) => {
  try {
    await templateController.getTemplates(req, res);
  } catch (error) {
    logger.error("Template route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route GET /api/templates/stats
 * @desc 获取模板统计信息
 */
router.get("/stats", async (req, res) => {
  try {
    await templateController.getTemplateStats(req, res);
  } catch (error) {
    logger.error("Template stats route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route GET /api/templates/search
 * @desc 搜索模板
 * @query {string} q - 搜索关键词
 * @query {number} page - 页码 (默认: 1)
 * @query {number} limit - 每页数量 (默认: 20)
 * @query {string} category - 分类过滤
 * @query {string} tags - 标签过滤 (逗号分隔)
 * @query {string} sortBy - 排序方式 (popular|newest|rating|name)
 * @query {string} sortOrder - 排序顺序 (asc|desc)
 */
router.get("/search", async (req, res) => {
  try {
    await templateController.searchTemplates(req, res);
  } catch (error) {
    logger.error("Template search route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route GET /api/templates/:id
 * @desc 根据ID获取模板详情
 * @param {string} id - 模板ID
 */
router.get("/:id", async (req, res) => {
  try {
    await templateController.getTemplateById(req, res);
  } catch (error) {
    logger.error("Template get by ID route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route POST /api/templates
 * @desc 创建新模板
 * @body {string} name - 模板名称
 * @body {string} description - 模板描述
 * @body {string} category - 模板分类
 * @body {string[]} tags - 模板标签
 * @body {boolean} isPublic - 是否公开
 * @body {object} canvasState - 画布状态
 * @body {string} authorId - 作者ID (可选)
 * @body {string} authorName - 作者名称 (可选)
 */
router.post("/", validateTemplateData, async (req, res) => {
  try {
    await templateController.createTemplate(req, res);
  } catch (error) {
    logger.error("Template create route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route PUT /api/templates/:id
 * @desc 更新模板
 * @param {string} id - 模板ID
 * @body {object} updates - 更新数据
 */
router.put("/:id", async (req, res) => {
  try {
    await templateController.updateTemplate(req, res);
  } catch (error) {
    logger.error("Template update route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route DELETE /api/templates/:id
 * @desc 删除模板
 * @param {string} id - 模板ID
 */
router.delete("/:id", async (req, res) => {
  try {
    await templateController.deleteTemplate(req, res);
  } catch (error) {
    logger.error("Template delete route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route POST /api/templates/:id/use
 * @desc 增加模板使用次数
 * @param {string} id - 模板ID
 */
router.post("/:id/use", async (req, res) => {
  try {
    await templateController.incrementUsage(req, res);
  } catch (error) {
    logger.error("Template increment usage route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route POST /api/templates/:id/rate
 * @desc 为模板评分
 * @param {string} id - 模板ID
 * @body {number} rating - 评分 (1-5)
 */
router.post("/:id/rate", async (req, res) => {
  try {
    await templateController.rateTemplate(req, res);
  } catch (error) {
    logger.error("Template rate route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route POST /api/templates/:id/thumbnail
 * @desc 上传模板缩略图
 * @param {string} id - 模板ID
 * @body {file} thumbnail - 缩略图文件
 */
router.post(
  "/:id/thumbnail",
  upload.single("thumbnail"),
  handleMulterError,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { id } = req.params;
      const file = req.file;

      if (!file) {
        res.status(400).json({ error: "No thumbnail file provided" });
        return;
      }

      // 构建缩略图URL
      const thumbnailUrl = `/api/templates/thumbnails/${file.filename}`;

      // 直接更新模板的缩略图字段
      req.body = { thumbnail: thumbnailUrl };
      req.params.id = id;

      await templateController.updateTemplate(req, res);
    } catch (error) {
      logger.error("Template thumbnail upload route error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

/**
 * @route GET /api/templates/thumbnails/:filename
 * @desc 获取模板缩略图
 * @param {string} filename - 文件名
 */
router.get("/thumbnails/:filename", (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(
      process.cwd(),
      "data",
      "templates",
      "thumbnails",
      filename
    );

    res.sendFile(filePath, (err) => {
      if (err) {
        logger.error("Failed to send thumbnail file:", err);
        if (!res.headersSent) {
          res.status(404).json({ error: "Thumbnail not found" });
        }
      }
    });
  } catch (error) {
    logger.error("Template thumbnail route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @route POST /api/templates/:id/preview
 * @desc 生成模板预览视频
 * @param {string} id - 模板ID
 */
router.post("/:id/preview", async (req, res) => {
  try {
    // TODO: 实现预览视频生成逻辑
    // 这里可以调用视频生成服务来创建预览
    res.json({
      message: "Preview generation started",
      taskId: `preview_${req.params.id}_${Date.now()}`,
    });
  } catch (error) {
    logger.error("Template preview route error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

export default router;
