import { Router } from "express";
import { S3Controller } from "../controllers/S3Controller";
import {
  securityMiddleware,
  createUploadRateLimit,
  createAPIRateLimit,
} from "../middleware/securityMiddleware";

const router = Router();
const s3Controller = new S3Controller();

// 应用认证中间件到所有路由
router.use(securityMiddleware.authenticate);

// 应用API速率限制
router.use(createAPIRateLimit(15 * 60 * 1000, 200)); // 15分钟内最多200个请求

// 单文件上传签名URL（需要文件验证和配额检查）
router.post(
  "/signed-url",
  createUploadRateLimit(15 * 60 * 1000, 20), // 15分钟内最多20次上传请求
  securityMiddleware.validateFileUpload,
  s3Controller.generateSignedUrl.bind(s3Controller)
);

// 多部分上传相关路由
router.post(
  "/multipart/initiate",
  createUploadRateLimit(15 * 60 * 1000, 10), // 15分钟内最多10次多部分上传
  securityMiddleware.validateFileUpload,
  s3Controller.initiateMultipartUpload.bind(s3Controller)
);
router.post(
  "/multipart/part-url",
  s3Controller.generatePartUploadUrl.bind(s3Controller)
);
router.post(
  "/multipart/complete",
  s3Controller.completeMultipartUpload.bind(s3Controller)
);
router.post(
  "/multipart/abort",
  s3Controller.abortMultipartUpload.bind(s3Controller)
);

// 上传完成通知
router.post(
  "/upload-complete",
  s3Controller.handleUploadComplete.bind(s3Controller)
);

// 媒体管理路由
router.get("/media", s3Controller.listMedia.bind(s3Controller));
router.get(
  "/media/:id",
  securityMiddleware.validateResourceOwnership,
  s3Controller.getMediaDetails.bind(s3Controller)
);
router.delete(
  "/media/:id",
  securityMiddleware.validateResourceOwnership,
  securityMiddleware.checkPermission("delete:own"),
  s3Controller.deleteMedia.bind(s3Controller)
);

// 用户配额信息
router.get("/quota", s3Controller.getUserQuota.bind(s3Controller));

export default router;
