import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { MediaElement } from "../ffmpeg/types";

/**
 * 测试分割媒体元素的优化效果
 * 使用相同的视频源创建多个分割元素，验证输入源是否被重用
 */
async function testSplitOptimization() {
  const generator = new FFmpegCommandGenerator();

  // 原始视频的URL
  const videoUrl = "https://videos.pexels.com/video-files/854878/854878-uhd_2560_1440_25fps.mp4";

  // 第一个视频元素（原始视频的前半部分）
  const videoElement1: MediaElement = {
    id: "video1",
    type: "video",
    opacity: 1,
    placement: {
      x: 284.44,
      y: 160,
      width: 711.11,
      height: 400,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 0,
      end: 4000,
    },
    properties: {
      elementId: "video-video1",
      src: videoUrl,
      mediaStartTime: 0, // 从视频开始处播放
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
    },
  };

  // 第二个视频元素（原始视频的后半部分）
  const videoElement2: MediaElement = {
    id: "video2",
    type: "video",
    opacity: 1,
    placement: {
      x: 284.44,
      y: 160,
      width: 711.11,
      height: 400,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 4000,
      end: 8000,
    },
    properties: {
      elementId: "video-video2",
      src: videoUrl,
      mediaStartTime: 4, // 从视频的第4秒开始播放
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
    },
  };

  // 第三个视频元素（原始视频的中间部分）
  const videoElement3: MediaElement = {
    id: "video3",
    type: "video",
    opacity: 1,
    placement: {
      x: 284.44,
      y: 160,
      width: 711.11,
      height: 400,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 8000,
      end: 12000,
    },
    properties: {
      elementId: "video-video3",
      src: videoUrl,
      mediaStartTime: 2, // 从视频的第2秒开始播放
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
    },
  };

  // 创建画布状态
  const canvasState = {
    width: 1280,
    height: 720,
    backgroundColor: "black",
    elements: [videoElement1, videoElement2, videoElement3],
    outputFormat: {
      codec: "libx264",
      format: "mp4",
      quality: "medium" as const,
      frameRate: 30,
    },
  };

  console.log("开始测试分割媒体元素的优化效果...");
  console.log("画布状态:", JSON.stringify(canvasState, null, 2));

  try {
    // 生成FFmpeg命令
    const command = await generator.generateCommand(canvasState);
    console.log("生成的FFmpeg命令:", command);
  } catch (error) {
    console.error("生成FFmpeg命令时出错:", error);
  } finally {
    // 清理资源
    generator.destroy();
  }
}

// 运行测试
testSplitOptimization().catch(console.error);
