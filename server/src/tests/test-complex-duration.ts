import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState, MediaElement, Caption } from "../ffmpeg/types";

/**
 * 测试复杂场景下的视频持续时间计算
 * 包含多个媒体元素和多个字幕，验证是否正确取最大结束时间
 */
async function testComplexDuration() {
  console.log("开始测试复杂场景下的视频持续时间计算...");

  // 创建多个媒体元素，结束时间不同
  const elements: MediaElement[] = [
    {
      id: "video1",
      type: "video",
      opacity: 1,
      placement: {
        x: 0,
        y: 0,
        width: 640,
        height: 360,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
      },
      timeFrame: {
        start: 0,
        end: 8000, // 8秒
      },
      properties: {
        src: "https://example.com/video1.mp4",
      },
    },
    {
      id: "image1",
      type: "image",
      opacity: 1,
      placement: {
        x: 640,
        y: 0,
        width: 640,
        height: 360,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
      },
      timeFrame: {
        start: 2000,
        end: 12000, // 12秒 (最大的媒体元素结束时间)
      },
      properties: {
        src: "https://example.com/image1.jpg",
      },
    },
    {
      id: "text1",
      type: "text",
      opacity: 1,
      placement: {
        x: 100,
        y: 400,
        width: 1080,
        height: 100,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
      },
      timeFrame: {
        start: 1000,
        end: 6000, // 6秒
      },
      properties: {
        text: "测试文本",
        fontSize: 24,
        fontColor: "#ffffff",
      },
    },
  ];

  // 创建多个字幕，其中一个结束时间最晚
  const captions: Caption[] = [
    {
      id: "caption1",
      startTime: "00:00:02", // 2秒
      endTime: "00:00:08", // 8秒
      text: "第一个字幕",
    },
    {
      id: "caption2",
      startTime: "00:00:10", // 10秒
      endTime: "00:00:18", // 18秒 (最大的字幕结束时间)
      text: "第二个字幕",
    },
    {
      id: "caption3",
      startTime: "00:00:14", // 14秒
      endTime: "00:00:16", // 16秒
      text: "第三个字幕",
    },
  ];

  // 创建画布状态
  const canvasState: CanvasState = {
    width: 1280,
    height: 720,
    backgroundColor: "#000000",
    elements: elements,
    captions: captions,
  };

  // 创建命令生成器
  const commandGenerator = new FFmpegCommandGenerator();

  try {
    console.log("媒体元素结束时间:");
    elements.forEach((el, index) => {
      console.log(
        `  元素${index + 1} (${el.type}): ${el.timeFrame.end / 1000}秒`
      );
    });

    console.log("字幕结束时间:");
    captions.forEach((caption, index) => {
      const [hours, minutes, seconds] = caption.endTime.split(":").map(Number);
      const endTimeSeconds = hours * 3600 + minutes * 60 + seconds;
      console.log(`  字幕${index + 1}: ${endTimeSeconds}秒`);
    });

    const maxMediaEndTime =
      Math.max(...elements.map((el) => el.timeFrame.end)) / 1000;
    const maxCaptionEndTime = Math.max(
      ...captions.map((caption) => {
        const [hours, minutes, seconds] = caption.endTime
          .split(":")
          .map(Number);
        return hours * 3600 + minutes * 60 + seconds;
      })
    );

    console.log(`媒体元素最大结束时间: ${maxMediaEndTime}秒`);
    console.log(`字幕最大结束时间: ${maxCaptionEndTime}秒`);
    console.log(
      `预期视频总时长: ${Math.max(maxMediaEndTime, maxCaptionEndTime)}秒`
    );

    // 生成FFmpeg命令
    const command = await commandGenerator.generateCommand(canvasState);

    // 检查命令中的背景持续时间
    const durationMatch = command.match(
      /-f lavfi -i color=c=#000000:s=1280x720:d=([0-9.]+)/
    );
    if (durationMatch) {
      const duration = parseFloat(durationMatch[1]);
      console.log(`\n实际生成的背景持续时间: ${duration}秒`);

      // 验证持续时间是否正确（应该是18秒，字幕的最大结束时间）
      if (duration === 18) {
        console.log(
          "✅ 测试通过: 视频持续时间正确地取了所有元素和字幕的最大结束时间"
        );
      } else if (duration === 12) {
        console.log(
          "❌ 测试失败: 视频持续时间只考虑了媒体元素的最大结束时间，忽略了字幕"
        );
      } else {
        console.log(
          `❓ 测试结果不确定: 视频持续时间为 ${duration} 秒，不符合预期的18秒`
        );
      }
    } else {
      console.log("❌ 测试失败: 无法从命令中提取背景持续时间");
    }
  } catch (error) {
    console.error("测试过程中发生错误:", error);
  } finally {
    // 清理资源
    commandGenerator.destroy();
  }
}

// 运行测试
testComplexDuration().catch(console.error);
