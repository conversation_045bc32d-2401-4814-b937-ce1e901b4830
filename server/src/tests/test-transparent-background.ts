import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState, MediaElement } from "../ffmpeg/types";

/**
 * 测试透明背景的FFmpeg命令生成
 * 验证当画布背景色设置为transparent时，后端能正确处理
 */
async function testTransparentBackground() {
  const generator = new FFmpegCommandGenerator();

  // 创建一个简单的文本元素用于测试
  const textElement: MediaElement = {
    id: "text-1",
    type: "text",
    opacity: 1,
    placement: {
      x: 640,
      y: 360,
      width: 400,
      height: 100,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 0,
      end: 5000, // 5秒
    },
    properties: {
      text: "透明背景测试",
      fontSize: 48,
      fontColor: "#FFFFFF",
      fontWeight: 700,
      textAlign: "center",
      backgroundColor: "transparent",
    },
  };

  // 测试透明背景的画布状态
  const canvasStateTransparent: CanvasState = {
    backgroundColor: "transparent", // 设置为透明背景
    width: 1280,
    height: 720,
    elements: [textElement],
    captions: [],
  };

  // 测试普通背景的画布状态（用于对比）
  const canvasStateNormal: CanvasState = {
    backgroundColor: "#000000", // 设置为黑色背景
    width: 1280,
    height: 720,
    elements: [textElement],
    captions: [],
  };

  try {
    console.log("=== 测试透明背景 ===");
    const commandTransparent = await generator.generateCommand(
      canvasStateTransparent,
      "test-transparent-bg"
    );
    console.log("透明背景生成的FFmpeg命令:");
    console.log(commandTransparent);

    // 验证透明背景的关键特征
    console.log("\n=== 验证透明背景特征 ===");
    
    // 1. 检查背景色输入是否使用了black@0
    if (commandTransparent.includes("color=c=black@0")) {
      console.log("✓ 背景色正确使用了透明黑色 (black@0)");
    } else {
      console.log("✗ 背景色未正确设置为透明");
    }

    // 2. 检查是否使用了RGBA格式
    if (commandTransparent.includes("format=rgba")) {
      console.log("✓ 正确使用了RGBA格式保持透明度");
    } else {
      console.log("✗ 未使用RGBA格式，可能丢失透明度信息");
    }

    // 3. 检查像素格式是否为yuva420p
    if (commandTransparent.includes("-pix_fmt yuva420p")) {
      console.log("✓ 输出像素格式正确设置为yuva420p（支持透明度）");
    } else {
      console.log("✗ 输出像素格式未设置为支持透明度的格式");
    }

    console.log("\n=== 测试普通背景（对比） ===");
    const commandNormal = await generator.generateCommand(
      canvasStateNormal,
      "test-normal-bg"
    );
    console.log("普通背景生成的FFmpeg命令:");
    console.log(commandNormal);

    // 验证普通背景的特征
    console.log("\n=== 验证普通背景特征 ===");
    
    // 1. 检查背景色输入是否使用了指定颜色
    if (commandNormal.includes("color=c=#000000")) {
      console.log("✓ 背景色正确使用了指定颜色 (#000000)");
    } else {
      console.log("✗ 背景色未正确设置");
    }

    // 2. 检查是否使用了yuv420p格式
    if (commandNormal.includes("format=yuv420p")) {
      console.log("✓ 正确使用了yuv420p格式");
    } else {
      console.log("✗ 未使用标准的yuv420p格式");
    }

    // 3. 检查像素格式是否为yuv420p
    if (commandNormal.includes("-pix_fmt yuv420p")) {
      console.log("✓ 输出像素格式正确设置为yuv420p");
    } else {
      console.log("✗ 输出像素格式设置异常");
    }

    console.log("\n=== 测试完成 ===");
    console.log("透明背景支持已实现，前端设置transparent时后端能正确处理");

  } catch (error) {
    console.error("测试过程中出错:", error);
  } finally {
    // 清理资源
    generator.destroy();
  }
}

// 执行测试
testTransparentBackground().catch(console.error);
