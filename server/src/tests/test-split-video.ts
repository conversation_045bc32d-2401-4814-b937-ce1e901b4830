import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState, MediaElement } from "../ffmpeg/types";

/**
 * 测试视频分割命令生成
 * 使用与用户提供的示例相同的输入数据
 */
async function testSplitVideoCommand() {
  const generator = new FFmpegCommandGenerator();

  // 第一个视频元素（原始视频的前半部分）
  const videoElement1: MediaElement = {
    id: "ieapxct",
    type: "video",
    opacity: 1,
    placement: {
      x: 284.44,
      y: 160,
      width: 711.11,
      height: 400,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 0,
      end: 4150,
    },
    properties: {
      elementId: "video-ieapxct",
      src: "https://videos.pexels.com/video-files/854878/854878-uhd_2560_1440_25fps.mp4",
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
    },
  };

  // 第二个视频元素（分割后的视频，从mediaStartTime开始）
  const videoElement2: MediaElement = {
    id: "akuvetn",
    type: "video",
    opacity: 1,

    placement: {
      x: 284.44,
      y: 160,
      width: 711.11,
      height: 400,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 7002,
      end: 15822,
    },
    properties: {
      elementId: "video-akuvetn",
      src: "https://videos.pexels.com/video-files/854878/854878-uhd_2560_1440_25fps.mp4",
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
      mediaStartTime: 4.15,
    },
  };

  const canvasState: CanvasState = {
    backgroundColor: "#6A1B9A",
    width: 1280,
    height: 720,
    elements: [videoElement1, videoElement2], // 修改顺序，先显示第一个视频，再显示第二个视频
    captions: [],
  };

  try {
    // 生成命令
    const command = await generator.generateCommand(canvasState);
    console.log("生成的FFmpeg命令:");
    console.log(command);
  } catch (error) {
    console.error("生成命令时出错:", error);
  }
}

// 执行测试
testSplitVideoCommand().catch(console.error);
