import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState, MediaElement } from "../ffmpeg/types";

/**
 * 测试被分割视频的startTime大于0的情况
 * 确保声音播放时间和画面同步
 */
async function testDelayedSplitVideoCommand() {
  const generator = new FFmpegCommandGenerator();

  // 第一个视频元素（原始视频，startTime大于0）
  const videoElement1: MediaElement = {
    id: "ieapxct",
    type: "video",
    opacity: 1,
    placement: {
      x: 284.44,
      y: 160,
      width: 711.11,
      height: 400,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 3000, // 注意：这里设置为3000毫秒，即3秒
      end: 7150,
    },
    properties: {
      elementId: "video-ieapxct",
      src: "https://videos.pexels.com/video-files/854878/854878-uhd_2560_1440_25fps.mp4",
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
    },
  };

  // 第二个视频元素（分割后的视频，从mediaStartTime开始）
  const videoElement2: MediaElement = {
    id: "akuvetn",
    type: "video",
    opacity: 1,
    placement: {
      x: 284.44,
      y: 160,
      width: 711.11,
      height: 400,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 10002, // 注意：这里设置为10002毫秒，即10.002秒
      end: 18822,
    },
    properties: {
      elementId: "video-akuvetn",
      src: "https://videos.pexels.com/video-files/854878/854878-uhd_2560_1440_25fps.mp4",
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
      mediaStartTime: 4.15, // 从原始视频的4.15秒开始
    },
  };

  const canvasState: CanvasState = {
    backgroundColor: "#6A1B9A",
    width: 1280,
    height: 720,
    elements: [videoElement1, videoElement2], // 先显示第一个视频，再显示第二个视频
    captions: [],
  };

  try {
    // 生成命令
    const command = await generator.generateCommand(canvasState);
    console.log("生成的FFmpeg命令:");
    console.log(command);
  } catch (error) {
    console.error("生成命令时出错:", error);
  }
}

// 执行测试
testDelayedSplitVideoCommand().catch(console.error);
