import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState } from "../ffmpeg/types";

/**
 * 测试视频时长修复
 * 验证生成的FFmpeg命令中的时长设置是否正确
 */
async function testDurationFix() {
  console.log("开始测试视频时长修复...");

  // 使用你提供的测试数据
  const canvasState: CanvasState = {
    backgroundColor: "#66BB6A",
    width: 1280,
    height: 640,
    elements: [
      {
        id: "4jekvpo",
        opacity: 1,
        type: "image",
        placement: {
          x: 424.99,
          y: 170,
          width: 430.01,
          height: 300,
          rotation: 0,
          scaleX: 1,
          scaleY: 1,
          flipX: false,
          flipY: false,
        },
        timeFrame: { start: 16488, end: 25035 },
        properties: {
          elementId: "image-4jekvpo",
          src: "https://pixabay.com/get/gbe534670d37d07e4c0315de6f3dd57c692f1d2003452ad9e630fa92f48d37dfc6618e8f9dec2cbf15f28d193ef5abdebc8b89f051e198bc86eaec496f73f5e85_1280.jpg",
          effect: { type: "none" },
          filters: { type: "none" },
          border: {
            width: 0,
            color: "black",
            style: "solid",
            borderRadius: 0,
          },
        },
      },
      {
        id: "jg23pvi",
        opacity: 1,
        type: "video",
        placement: {
          x: 524.62,
          y: 120,
          width: 230.77,
          height: 400,
          rotation: 0,
          scaleX: 1,
          scaleY: 1,
          flipX: false,
          flipY: false,
        },
        timeFrame: { start: 4084, end: 12637 },
        properties: {
          elementId: "video-jg23pvi",
          src: "https://videos.pexels.com/video-files/7438482/7438482-sd_360_624_30fps.mp4",
          originalDuration: 8553.33,
          effect: { type: "none" },
          filters: { type: "none" },
          border: {
            width: 0,
            color: "black",
            style: "solid",
            borderRadius: 0,
          },
          mediaStartTime: 0,
        },
      },
    ],
    captions: [],
    globalCaptionStyle: {
      fontSize: 35,
      fontFamily: "Arial",
      fontColor: "#FFFFFF",
      fontWeight: 700,
      textAlign: "center",
      lineHeight: 1.2,
      charSpacing: 0,
      styles: ["bold", "italic", "underline"],
      strokeWidth: 1.3,
      strokeColor: "#d79329",
      shadowColor: "#000000",
      shadowBlur: 2,
      shadowOffsetX: 1,
      shadowOffsetY: 1,
      backgroundColor: "transparent",
      useGradient: false,
      gradientColors: ["#FFFFFF", "#000000"],
    },
  };

  const generator = new FFmpegCommandGenerator();

  try {
    const command = await generator.generateCommand(
      canvasState,
      "test-duration-fix"
    );

    console.log("生成的FFmpeg命令:");
    console.log(command);

    // 验证关键点
    console.log("\n验证结果:");

    // 1. 检查背景色的持续时间
    const backgroundMatch = command.match(
      /color=c=#66BB6A:s=1280x640:d=([0-9.]+)/
    );
    if (backgroundMatch) {
      const backgroundDuration = parseFloat(backgroundMatch[1]);
      console.log(`✓ 背景色持续时间: ${backgroundDuration}秒`);

      // 应该等于最大元素结束时间 25.035秒
      if (Math.abs(backgroundDuration - 25.035) < 0.001) {
        console.log("✓ 背景色持续时间正确");
      } else {
        console.log(
          `✗ 背景色持续时间错误，期望25.035秒，实际${backgroundDuration}秒`
        );
      }
    }

    // 2. 检查视频元素的trim duration
    const videoTrimMatch = command.match(
      /\[1:v\]trim=start=0\.000:duration=([0-9.]+)/
    );
    if (videoTrimMatch) {
      const videoDuration = parseFloat(videoTrimMatch[1]);
      console.log(`✓ 视频元素trim持续时间: ${videoDuration}秒`);

      // 应该等于视频元素的实际持续时间 (12637-4084)/1000 = 8.553秒
      const expectedVideoDuration = (12637 - 4084) / 1000;
      if (Math.abs(videoDuration - expectedVideoDuration) < 0.001) {
        console.log("✓ 视频元素trim持续时间正确");
      } else {
        console.log(
          `✗ 视频元素trim持续时间错误，期望${expectedVideoDuration}秒，实际${videoDuration}秒`
        );
      }
    }

    // 3. 检查音频元素的atrim duration
    const audioTrimMatch = command.match(
      /\[1:a\]atrim=start=0\.000:duration=([0-9.]+)/
    );
    if (audioTrimMatch) {
      const audioDuration = parseFloat(audioTrimMatch[1]);
      console.log(`✓ 音频元素atrim持续时间: ${audioDuration}秒`);

      // 应该等于视频元素的实际持续时间
      const expectedAudioDuration = (12637 - 4084) / 1000;
      if (Math.abs(audioDuration - expectedAudioDuration) < 0.001) {
        console.log("✓ 音频元素atrim持续时间正确");
      } else {
        console.log(
          `✗ 音频元素atrim持续时间错误，期望${expectedAudioDuration}秒，实际${audioDuration}秒`
        );
      }
    }

    // 4. 检查透明背景的持续时间
    const transparentBgMatches = command.match(
      /color=c=black@0:s=1280x640:d=([0-9.]+)/g
    );
    if (transparentBgMatches) {
      console.log(`✓ 找到${transparentBgMatches.length}个透明背景`);
      transparentBgMatches.forEach((match, index) => {
        const durationMatch = match.match(/d=([0-9.]+)/);
        if (durationMatch) {
          const duration = parseFloat(durationMatch[1]);
          console.log(`  透明背景${index + 1}持续时间: ${duration}秒`);

          // 应该等于总视频时长 25.035秒
          if (Math.abs(duration - 25.035) < 0.001) {
            console.log(`  ✓ 透明背景${index + 1}持续时间正确`);
          } else {
            console.log(
              `  ✗ 透明背景${
                index + 1
              }持续时间错误，期望25.035秒，实际${duration}秒`
            );
          }
        }
      });
    }

    console.log("\n测试完成！");
  } catch (error) {
    console.error("测试失败:", error);
  } finally {
    generator.cleanup();
  }
}

// 运行测试
testDurationFix().catch(console.error);
