import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState } from "../ffmpeg/types";

/**
 * 测试变速视频的音视频同步问题
 * 验证音频和视频的trim持续时间是否一致
 */
async function testVideoSpeedAudioSync() {
  console.log("=== 测试变速视频音视频同步 ===");

  // 模拟用户提供的变速视频元素
  const canvasState: CanvasState = {
    width: 1280,
    height: 720,
    backgroundColor: "#66BB6A",
    elements: [
      {
        id: "vxb35ia",
        opacity: 1,
        type: "video",
        placement: {
          x: 284.44,
          y: 160,
          width: 711.11,
          height: 400,
          rotation: 0,
          scaleX: 1,
          scaleY: 1,
          flipX: false,
          flipY: false,
        },
        timeFrame: {
          start: 0,
          end: 6826.32,
        },
        properties: {
          src: "https://videos.pexels.com/video-files/854878/854878-uhd_2560_1440_25fps.mp4",
        },
        playbackSpeed: 1.9,
      },
    ],
  };

  // 创建命令生成器
  const commandGenerator = new FFmpegCommandGenerator();

  try {
    console.log("测试场景:");
    console.log("- 视频时长：6.83秒");
    console.log("- 播放速度：1.9倍");
    console.log("- 期望源视频时长：6.83 × 1.9 ≈ 12.97秒");
    console.log("- 期望源音频时长：6.83 × 1.9 ≈ 12.97秒");

    // 生成FFmpeg命令
    const command = await commandGenerator.generateCommand(
      canvasState,
      "test-video-speed-audio-sync"
    );

    console.log("\n=== 分析生成的命令 ===");

    // 分析视频处理
    const videoTrimMatch = command.match(
      /\[1:v\]trim=start=([0-9.]+):duration=([0-9.]+)/
    );
    if (videoTrimMatch) {
      const videoStart = parseFloat(videoTrimMatch[1]);
      const videoDuration = parseFloat(videoTrimMatch[2]);
      console.log(
        `✓ 视频处理: start=${videoStart}s, duration=${videoDuration}s`
      );
    }

    // 分析音频处理
    const audioTrimMatch = command.match(
      /\[1:a\]atrim=start=([0-9.]+):duration=([0-9.]+)/
    );
    if (audioTrimMatch) {
      const audioStart = parseFloat(audioTrimMatch[1]);
      const audioDuration = parseFloat(audioTrimMatch[2]);
      console.log(
        `✓ 音频处理: start=${audioStart}s, duration=${audioDuration}s`
      );

      // 检查音视频时长是否一致
      if (videoTrimMatch) {
        const videoDuration = parseFloat(videoTrimMatch[2]);
        const timeDiff = Math.abs(videoDuration - audioDuration);

        if (timeDiff < 0.01) {
          console.log(
            `✅ 音视频时长一致: 差异${timeDiff.toFixed(3)}s (< 0.01s)`
          );
        } else {
          console.log(`❌ 音视频时长不一致: 差异${timeDiff.toFixed(3)}s`);
          console.log(`  视频时长: ${videoDuration}s`);
          console.log(`  音频时长: ${audioDuration}s`);
        }
      }
    }

    // 分析播放速度处理
    const videoSpeedMatch = command.match(/setpts=([0-9.]+)\*\(PTS-STARTPTS\)/);
    if (videoSpeedMatch) {
      const ptsMultiplier = parseFloat(videoSpeedMatch[1]);
      const calculatedSpeed = 1 / ptsMultiplier;
      console.log(
        `✓ 视频播放速度: PTS倍数=${ptsMultiplier}, 计算速度=${calculatedSpeed.toFixed(
          2
        )}x`
      );
    }

    const audioSpeedMatch = command.match(/atempo=([0-9.]+)/);
    if (audioSpeedMatch) {
      const audioSpeed = parseFloat(audioSpeedMatch[1]);
      console.log(`✓ 音频播放速度: atempo=${audioSpeed}`);
    }

    // 验证期望值
    console.log("\n=== 验证期望值 ===");
    const expectedSourceDuration = 6.82632 * 1.9; // ≈ 12.97
    console.log(`期望源时长: ${expectedSourceDuration.toFixed(3)}s`);

    if (videoTrimMatch && audioTrimMatch) {
      const videoDuration = parseFloat(videoTrimMatch[2]);
      const audioDuration = parseFloat(audioTrimMatch[2]);

      const videoMatch =
        Math.abs(videoDuration - expectedSourceDuration) < 0.01;
      const audioMatch =
        Math.abs(audioDuration - expectedSourceDuration) < 0.01;

      console.log(
        `视频时长匹配: ${
          videoMatch ? "✅" : "❌"
        } (${videoDuration}s vs ${expectedSourceDuration.toFixed(3)}s)`
      );
      console.log(
        `音频时长匹配: ${
          audioMatch ? "✅" : "❌"
        } (${audioDuration}s vs ${expectedSourceDuration.toFixed(3)}s)`
      );

      if (videoMatch && audioMatch) {
        console.log("\n🎉 修复成功！音视频时长计算正确");
      } else {
        console.log("\n❌ 仍有问题，需要进一步调试");
      }
    }

    console.log("\n完整的FFmpeg命令:");
    console.log(command);
  } catch (error) {
    console.error("测试失败:", error);
  } finally {
    // 清理资源
    commandGenerator.cleanup();
  }
}

// 运行测试
if (require.main === module) {
  testVideoSpeedAudioSync().catch(console.error);
}

export { testVideoSpeedAudioSync };
