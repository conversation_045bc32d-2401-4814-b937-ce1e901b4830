import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState, MediaElement } from "../ffmpeg/types";

/**
 * 测试单个视频命令生成
 * 使用与用户提供的示例相同的输入数据
 */
async function testSingleVideoCommand() {
  const generator = new FFmpegCommandGenerator();

  // 单个视频元素
  const videoElement: MediaElement = {
    id: "p1wsbxf",
    type: "video",
    opacity: 1,
    placement: {
      x: 284.44,
      y: 160,
      width: 711.11,
      height: 400,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    },
    timeFrame: {
      start: 17374,
      end: 26601,
    },
    properties: {
      elementId: "video-p1wsbxf",
      src: "https://videos.pexels.com/video-files/854878/854878-uhd_2560_1440_25fps.mp4",
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
    },
  };

  const canvasState: CanvasState = {
    backgroundColor: "#6A1B9A",
    width: 1280,
    height: 720,
    elements: [videoElement],
    captions: [],
  };

  try {
    // 生成命令
    const command = await generator.generateCommand(canvasState);
    console.log("生成的FFmpeg命令:");
    console.log(command);
  } catch (error) {
    console.error("生成命令时出错:", error);
  }
}

// 执行测试
testSingleVideoCommand().catch(console.error);
