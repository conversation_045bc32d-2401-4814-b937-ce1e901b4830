import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState, MediaElement, Caption } from "../ffmpeg/types";

/**
 * 测试字幕持续时间对视频总时长的影响
 * 这个测试创建一个包含媒体元素和字幕的画布状态，
 * 其中字幕的结束时间晚于媒体元素的结束时间，
 * 验证生成的FFmpeg命令是否使用了正确的视频总时长
 */
async function testCaptionDuration() {
  console.log("开始测试字幕持续时间对视频总时长的影响...");

  // 创建一个视频元素，结束时间为10秒
  const videoElement: MediaElement = {
    id: "video1",
    type: "video",
    opacity: 1,
    placement: {
      x: 0,
      y: 0,
      width: 1280,
      height: 720,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
    },
    timeFrame: {
      start: 0,
      end: 10000, // 10秒
    },
    properties: {
      src: "https://example.com/test-video.mp4",
    },
  };

  // 创建一个字幕，结束时间为15秒
  const captions: Caption[] = [
    {
      id: "caption1",
      startTime: "00:00:05", // 5秒
      endTime: "00:00:15", // 15秒
      text: "这是一个测试字幕",
    },
  ];

  // 创建画布状态
  const canvasState: CanvasState = {
    width: 1280,
    height: 720,
    backgroundColor: "#000000",
    elements: [videoElement],
    captions: captions,
  };

  // 创建命令生成器
  const commandGenerator = new FFmpegCommandGenerator();

  try {
    // 生成FFmpeg命令
    const command = await commandGenerator.generateCommand(canvasState);
    
    // 检查命令中的背景持续时间
    const durationMatch = command.match(/-f lavfi -i color=c=#000000:s=1280x720:d=([0-9.]+)/);
    if (durationMatch) {
      const duration = parseFloat(durationMatch[1]);
      console.log(`命令中的背景持续时间: ${duration}秒`);
      
      // 验证持续时间是否正确（应该是15秒，而不是10秒）
      if (duration === 15) {
        console.log("✅ 测试通过: 视频持续时间正确地考虑了字幕的最大结束时间");
      } else if (duration === 10) {
        console.log("❌ 测试失败: 视频持续时间只考虑了媒体元素的最大结束时间，忽略了字幕");
      } else {
        console.log(`❓ 测试结果不确定: 视频持续时间为 ${duration} 秒，既不是10秒也不是15秒`);
      }
    } else {
      console.log("❌ 测试失败: 无法从命令中提取背景持续时间");
    }
    
    // 输出完整命令以供检查
    console.log("\n生成的FFmpeg命令:");
    console.log(command);
  } catch (error) {
    console.error("测试过程中发生错误:", error);
  } finally {
    // 清理资源
    commandGenerator.destroy();
  }
}

// 运行测试
testCaptionDuration().catch(console.error);
