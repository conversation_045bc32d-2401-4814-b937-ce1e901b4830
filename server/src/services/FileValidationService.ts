import { getS3Config } from "../config/s3Config";
import { FileValidationError } from "../types/media";
import logger from "../utils/logger";
import { createReadStream } from "fs";
import { pipeline } from "stream/promises";
import { createHash } from "crypto";

/**
 * 文件验证配置
 */
export interface FileValidationConfig {
  maxFileSize: number;
  allowedMimeTypes: string[];
  allowedExtensions: string[];
  scanForMalware?: boolean;
  checkFileIntegrity?: boolean;
}

/**
 * 文件验证结果
 */
export interface FileValidationResult {
  isValid: boolean;
  errors: FileValidationError[];
  warnings?: string[];
  metadata?: {
    actualMimeType?: string;
    fileSize: number;
    checksum?: string;
  };
}

/**
 * 文件验证服务
 */
export class FileValidationService {
  private config: FileValidationConfig;

  constructor() {
    const s3Config = getS3Config();
    this.config = {
      maxFileSize: s3Config.maxFileSize,
      allowedMimeTypes: s3Config.allowedFileTypes,
      allowedExtensions: this.extractExtensionsFromMimeTypes(
        s3Config.allowedFileTypes
      ),
      scanForMalware: false, // 可以集成第三方恶意软件扫描服务
      checkFileIntegrity: true,
    };
  }

  /**
   * 验证文件
   */
  async validateFile(
    fileName: string,
    fileSize: number,
    mimeType: string,
    filePath?: string
  ): Promise<FileValidationResult> {
    const errors: FileValidationError[] = [];
    const warnings: string[] = [];
    let metadata: any = { fileSize };

    // 1. 验证文件名
    const fileNameValidation = this.validateFileName(fileName);
    if (!fileNameValidation.isValid) {
      errors.push(...fileNameValidation.errors);
    }

    // 2. 验证文件大小
    const sizeValidation = this.validateFileSize(fileSize);
    if (!sizeValidation.isValid) {
      errors.push(...sizeValidation.errors);
    }

    // 3. 验证MIME类型
    const mimeValidation = this.validateMimeType(mimeType);
    if (!mimeValidation.isValid) {
      errors.push(...mimeValidation.errors);
    }

    // 4. 验证文件扩展名
    const extensionValidation = this.validateFileExtension(fileName);
    if (!extensionValidation.isValid) {
      errors.push(...extensionValidation.errors);
    }

    // 5. 检查MIME类型和扩展名是否匹配
    const consistencyValidation = this.validateMimeExtensionConsistency(
      fileName,
      mimeType
    );
    if (
      consistencyValidation.warnings &&
      consistencyValidation.warnings.length > 0
    ) {
      warnings.push(...consistencyValidation.warnings);
    }

    // 6. 如果提供了文件路径，进行更深入的验证
    if (filePath) {
      try {
        // 计算文件校验和
        if (this.config.checkFileIntegrity) {
          metadata.checksum = await this.calculateFileChecksum(filePath);
        }

        // 检测实际MIME类型（可以使用file-type库）
        // metadata.actualMimeType = await this.detectActualMimeType(filePath);
      } catch (error) {
        logger.warn(
          `Failed to perform advanced file validation for ${fileName}:`,
          error
        );
        warnings.push("Advanced file validation failed");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined,
      metadata,
    };
  }

  /**
   * 验证文件名
   */
  private validateFileName(fileName: string): {
    isValid: boolean;
    errors: FileValidationError[];
  } {
    const errors: FileValidationError[] = [];

    // 检查文件名是否为空
    if (!fileName || fileName.trim().length === 0) {
      errors.push({
        code: "INVALID_FILE_NAME",
        message: "File name cannot be empty",
        value: fileName,
      });
      return { isValid: false, errors };
    }

    // 检查文件名长度
    if (fileName.length > 255) {
      errors.push({
        code: "INVALID_FILE_NAME",
        message: "File name is too long (maximum 255 characters)",
        value: fileName,
        limit: 255,
      });
    }

    // 检查危险字符
    const dangerousChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (dangerousChars.test(fileName)) {
      errors.push({
        code: "INVALID_FILE_NAME",
        message: "File name contains invalid characters",
        value: fileName,
      });
    }

    // 检查保留名称（Windows）
    const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
    if (reservedNames.test(fileName)) {
      errors.push({
        code: "INVALID_FILE_NAME",
        message: "File name is reserved",
        value: fileName,
      });
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证文件大小
   */
  private validateFileSize(fileSize: number): {
    isValid: boolean;
    errors: FileValidationError[];
  } {
    const errors: FileValidationError[] = [];

    if (fileSize <= 0) {
      errors.push({
        code: "FILE_TOO_LARGE",
        message: "File size must be greater than 0",
        value: fileSize,
      });
    }

    if (fileSize > this.config.maxFileSize) {
      errors.push({
        code: "FILE_TOO_LARGE",
        message: `File size exceeds maximum allowed size`,
        value: fileSize,
        limit: this.config.maxFileSize,
      });
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证MIME类型
   */
  private validateMimeType(mimeType: string): {
    isValid: boolean;
    errors: FileValidationError[];
  } {
    const errors: FileValidationError[] = [];

    if (!mimeType) {
      errors.push({
        code: "INVALID_FILE_TYPE",
        message: "MIME type is required",
        value: mimeType,
      });
      return { isValid: false, errors };
    }

    // 如果没有配置允许的MIME类型，则允许所有类型
    if (this.config.allowedMimeTypes.length === 0) {
      return { isValid: true, errors };
    }

    const isAllowed = this.config.allowedMimeTypes.some((allowedType) => {
      // 支持通配符匹配，如 "image/*"
      if (allowedType.endsWith("/*")) {
        const prefix = allowedType.slice(0, -2);
        return mimeType.startsWith(prefix + "/");
      }
      return mimeType === allowedType;
    });

    if (!isAllowed) {
      errors.push({
        code: "INVALID_FILE_TYPE",
        message: `File type ${mimeType} is not allowed`,
        value: mimeType,
        limit: this.config.allowedMimeTypes,
      });
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证文件扩展名
   */
  private validateFileExtension(fileName: string): {
    isValid: boolean;
    errors: FileValidationError[];
  } {
    const errors: FileValidationError[] = [];

    const extension = this.getFileExtension(fileName);
    if (!extension) {
      errors.push({
        code: "INVALID_FILE_TYPE",
        message: "File must have an extension",
        value: fileName,
      });
      return { isValid: false, errors };
    }

    // 如果没有配置允许的扩展名，则允许所有扩展名
    if (this.config.allowedExtensions.length === 0) {
      return { isValid: true, errors };
    }

    const isAllowed = this.config.allowedExtensions.includes(
      extension.toLowerCase()
    );
    if (!isAllowed) {
      errors.push({
        code: "INVALID_FILE_TYPE",
        message: `File extension ${extension} is not allowed`,
        value: extension,
        limit: this.config.allowedExtensions,
      });
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证MIME类型和扩展名的一致性
   */
  private validateMimeExtensionConsistency(
    fileName: string,
    mimeType: string
  ): { isValid: boolean; warnings?: string[] } {
    const warnings: string[] = [];
    const extension = this.getFileExtension(fileName);

    if (!extension) {
      return { isValid: true };
    }

    // 常见的MIME类型和扩展名映射
    const mimeExtensionMap: { [key: string]: string[] } = {
      "image/jpeg": [".jpg", ".jpeg"],
      "image/png": [".png"],
      "image/gif": [".gif"],
      "image/webp": [".webp"],
      "video/mp4": [".mp4"],
      "video/quicktime": [".mov"],
      "video/x-msvideo": [".avi"],
      "audio/mpeg": [".mp3"],
      "audio/wav": [".wav"],
      "audio/aac": [".aac"],
    };

    const expectedExtensions = mimeExtensionMap[mimeType];
    if (
      expectedExtensions &&
      !expectedExtensions.includes(extension.toLowerCase())
    ) {
      warnings.push(
        `File extension ${extension} may not match MIME type ${mimeType}. Expected: ${expectedExtensions.join(
          ", "
        )}`
      );
    }

    return { isValid: true, warnings };
  }

  /**
   * 计算文件校验和
   */
  private async calculateFileChecksum(filePath: string): Promise<string> {
    const hash = createHash("sha256");
    const stream = createReadStream(filePath);

    await pipeline(stream, hash);
    return hash.digest("hex");
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(fileName: string): string | null {
    const lastDotIndex = fileName.lastIndexOf(".");
    if (lastDotIndex === -1 || lastDotIndex === fileName.length - 1) {
      return null;
    }
    return fileName.substring(lastDotIndex);
  }

  /**
   * 从MIME类型列表中提取扩展名
   */
  private extractExtensionsFromMimeTypes(mimeTypes: string[]): string[] {
    const extensionMap: { [key: string]: string[] } = {
      "image/jpeg": [".jpg", ".jpeg"],
      "image/png": [".png"],
      "image/gif": [".gif"],
      "image/webp": [".webp"],
      "video/mp4": [".mp4"],
      "video/quicktime": [".mov"],
      "video/x-msvideo": [".avi"],
      "audio/mpeg": [".mp3"],
      "audio/wav": [".wav"],
      "audio/aac": [".aac"],
    };

    const extensions: string[] = [];
    for (const mimeType of mimeTypes) {
      const typeExtensions = extensionMap[mimeType];
      if (typeExtensions) {
        extensions.push(...typeExtensions);
      }
    }

    return [...new Set(extensions)]; // 去重
  }

  /**
   * 更新验证配置
   */
  updateConfig(newConfig: Partial<FileValidationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info("File validation config updated:", newConfig);
  }

  /**
   * 获取当前配置
   */
  getConfig(): FileValidationConfig {
    return { ...this.config };
  }
}
