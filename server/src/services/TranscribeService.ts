import {
  TranscribeClient,
  StartTranscriptionJobCommand,
  GetTranscriptionJobCommand,
  TranscriptionJobStatus,
} from "@aws-sdk/client-transcribe";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import axios from "axios";
import * as crypto from "crypto";
import logger from "../utils/logger";

interface TranscriptionResult {
  jobName: string;
  status: string;
  results?: {
    transcripts: { transcript: string }[];
    items: Array<{
      start_time?: string;
      end_time?: string;
      alternatives: Array<{ content: string }>;
      type: string;
    }>;
  };
  srtFilePath?: string;
}

interface TranscriptionStatus {
  taskId: string;
  status: string;
  progress: number;
  srtFilePath?: string;
  error?: string;
}

export class TranscribeService {
  private transcribeClient: TranscribeClient;
  private jobStatusMap: Map<string, TranscriptionStatus>;
  private tmpDir: string;

  constructor() {
    // Initialize AWS Transcribe client with credentials
    this.transcribeClient = new TranscribeClient({
      region: process.env.AWS_REGION || "us-east-1",
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
      },
    });

    this.jobStatusMap = new Map<string, TranscriptionStatus>();
    this.tmpDir = path.join(os.tmpdir(), "transcriptions");

    // Create temp directory if it doesn't exist
    if (!fs.existsSync(this.tmpDir)) {
      fs.mkdirSync(this.tmpDir, { recursive: true });
    }
  }

  /**
   * Start the transcription process for a media file
   * @param mediaUrl URL of the media file to transcribe
   * @param taskId Task identifier
   */
  async transcribeMedia(mediaUrl: string, taskId: string): Promise<void> {
    try {
      // Update job status
      this.jobStatusMap.set(taskId, {
        taskId,
        status: "INITIALIZING",
        progress: 0,
      });

      // Create a unique job name for AWS Transcribe
      const jobName = `transcription-${taskId}-${crypto
        .randomBytes(4)
        .toString("hex")}`;

      // Start transcription job
      const params = {
        TranscriptionJobName: jobName,
        Media: { MediaFileUri: mediaUrl },
        MediaFormat: this.getMediaFormat(mediaUrl),
        LanguageCode: "zh-CN", // Default to Chinese, can be made configurable
        OutputBucketName: process.env.AWS_S3_BUCKET || undefined, // Optional S3 bucket for results
      };

      const command = new StartTranscriptionJobCommand(params as any);
      await this.transcribeClient.send(command);

      // Update job status
      this.jobStatusMap.set(taskId, {
        taskId,
        status: "IN_PROGRESS",
        progress: 10,
      });

      // Start polling for job completion
      this.pollJobStatus(jobName, taskId);
    } catch (error) {
      logger.error(`Error starting transcription for taskId ${taskId}:`, error);
      this.jobStatusMap.set(taskId, {
        taskId,
        status: "FAILED",
        progress: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * Poll AWS for job status until completion
   * @param jobName AWS Transcribe job name
   * @param taskId Task identifier
   */
  private async pollJobStatus(jobName: string, taskId: string): Promise<void> {
    try {
      const command = new GetTranscriptionJobCommand({
        TranscriptionJobName: jobName,
      });

      const response = await this.transcribeClient.send(command);
      const job = response.TranscriptionJob;

      if (!job) {
        throw new Error("Job information not found");
      }

      const status = job.TranscriptionJobStatus;
      let progress = 0;

      switch (status) {
        case TranscriptionJobStatus.IN_PROGRESS:
          progress = 50; // Estimate 50% done while in progress
          setTimeout(() => this.pollJobStatus(jobName, taskId), 10000); // Poll every 10 seconds
          break;
        case TranscriptionJobStatus.COMPLETED:
          progress = 100;
          // Process the transcripts
          if (job.Transcript?.TranscriptFileUri) {
            await this.processTranscript(
              job.Transcript.TranscriptFileUri,
              taskId
            );
          } else {
            throw new Error("Transcript URI not found in completed job");
          }
          break;
        case TranscriptionJobStatus.FAILED:
          throw new Error(
            `Transcription job failed: ${job.FailureReason || "Unknown reason"}`
          );
        default:
          progress = 25; // Other states are early in the process
          setTimeout(() => this.pollJobStatus(jobName, taskId), 10000);
      }

      this.jobStatusMap.set(taskId, {
        taskId,
        status: status || "UNKNOWN",
        progress,
        error: job.FailureReason,
      });
    } catch (error) {
      logger.error(`Error polling job status for ${jobName}:`, error);
      this.jobStatusMap.set(taskId, {
        taskId,
        status: "FAILED",
        progress: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * Process the transcript data and convert to SRT format
   * @param transcriptUri URI of the transcript JSON file
   * @param taskId Task identifier
   */
  private async processTranscript(
    transcriptUri: string,
    taskId: string
  ): Promise<void> {
    try {
      // Download the transcript JSON
      const response = await axios.get(transcriptUri);
      const transcriptionData = response.data;

      // Convert JSON to SRT format
      const srtContent = this.convertToSRT(transcriptionData);

      // Save SRT to file
      const srtFilePath = path.join(this.tmpDir, `subtitles_${taskId}.srt`);
      fs.writeFileSync(srtFilePath, srtContent, "utf8");

      // Update status with SRT file path
      const currentStatus = this.jobStatusMap.get(taskId);
      if (currentStatus) {
        this.jobStatusMap.set(taskId, {
          ...currentStatus,
          srtFilePath,
          status: "COMPLETED",
          progress: 100,
        });
      }
    } catch (error) {
      logger.error(`Error processing transcript for task ${taskId}:`, error);
      this.jobStatusMap.set(taskId, {
        taskId,
        status: "FAILED",
        progress: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * Convert AWS Transcribe JSON format to SRT subtitle format
   * @param transcriptionData AWS Transcribe results
   * @returns SRT formatted string
   */
  private convertToSRT(transcriptionData: TranscriptionResult): string {
    if (
      !transcriptionData.results ||
      !transcriptionData.results.items ||
      transcriptionData.results.items.length === 0
    ) {
      return "";
    }

    const items = transcriptionData.results.items;
    let srtContent = "";
    let index = 1;
    let currentLine = "";
    let startTime = "";
    let endTime = "";
    let wordCount = 0;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      // Skip items without time information (often punctuation)
      if (!item.start_time && i > 0) {
        if (item.alternatives && item.alternatives.length > 0) {
          currentLine += item.alternatives[0].content;
        }
        continue;
      }

      // Start a new line if this is the first word or if we've reached a reasonable line length
      if (currentLine === "" && item.start_time) {
        startTime = item.start_time;
        currentLine = item.alternatives[0].content;
        wordCount = 1;
      } else if (
        wordCount >= 10 ||
        (i < items.length - 1 &&
          items[i + 1].type === "punctuation" &&
          items[i + 1].alternatives[0].content === ".")
      ) {
        // End the current line at a reasonable length or at the end of a sentence
        if (item.end_time) {
          endTime = item.end_time;
        }

        if (startTime && endTime) {
          srtContent += `${index}\n`;
          srtContent += `${this.formatSRTTime(
            startTime
          )} --> ${this.formatSRTTime(endTime)}\n`;
          srtContent += `${currentLine}\n\n`;

          index++;
          currentLine = "";
          wordCount = 0;
        }
      } else if (item.alternatives && item.alternatives.length > 0) {
        // Add word to current line
        currentLine += ` ${item.alternatives[0].content}`;
        wordCount++;

        if (item.end_time) {
          endTime = item.end_time;
        }
      }
    }

    // Add the last line if there's any content left
    if (currentLine !== "" && startTime && endTime) {
      srtContent += `${index}\n`;
      srtContent += `${this.formatSRTTime(startTime)} --> ${this.formatSRTTime(
        endTime
      )}\n`;
      srtContent += `${currentLine}\n\n`;
    }

    return srtContent;
  }

  /**
   * Format time from AWS Transcribe (seconds) to SRT format (HH:MM:SS,mmm)
   * @param timeInSeconds Time in seconds as string
   * @returns Time in SRT format
   */
  private formatSRTTime(timeInSeconds: string): string {
    const seconds = parseFloat(timeInSeconds);
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const milliseconds = Math.round((seconds - Math.floor(seconds)) * 1000);

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")},${milliseconds
      .toString()
      .padStart(3, "0")}`;
  }

  /**
   * Get the file format of the media from its URL
   * @param mediaUrl URL of the media file
   * @returns MediaFormat for AWS Transcribe
   */
  private getMediaFormat(mediaUrl: string): string {
    const extension = path.extname(mediaUrl).toLowerCase();

    switch (extension) {
      case ".mp3":
        return "mp3";
      case ".mp4":
      case ".m4a":
        return "mp4";
      case ".wav":
        return "wav";
      case ".flac":
        return "flac";
      case ".ogg":
        return "ogg";
      case ".amr":
        return "amr";
      case ".webm":
        return "webm";
      default:
        // Default to mp4 if format can't be determined
        return "mp4";
    }
  }

  /**
   * Get the status of a transcription task
   * @param taskId Task identifier
   * @returns Task status information
   */
  async getTranscriptionStatus(
    taskId: string
  ): Promise<TranscriptionStatus | null> {
    return this.jobStatusMap.get(taskId) || null;
  }

  /**
   * Get the path to the SRT file for a completed transcription
   * @param taskId Task identifier
   * @returns Path to SRT file or null if not available
   */
  async getTranscriptionSrtPath(taskId: string): Promise<string | null> {
    const status = this.jobStatusMap.get(taskId);
    if (status && status.srtFilePath && status.status === "COMPLETED") {
      return status.srtFilePath;
    }
    return null;
  }
}
