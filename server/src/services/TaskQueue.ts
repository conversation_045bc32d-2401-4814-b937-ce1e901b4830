import * as os from "os";
import logger from "../utils/logger";

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  QUEUED = "queued",
  RUNNING = "running",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled"
}

/**
 * 任务信息接口
 */
export interface TaskInfo {
  taskId: string;
  status: TaskStatus;
  queuedAt: number;
  startedAt?: number;
  completedAt?: number;
  error?: string;
}

/**
 * 任务队列类
 * 管理并发的视频生成任务，避免系统资源过载
 */
export class TaskQueue {
  /** 任务队列 */
  private queue: Array<{
    taskId: string;
    execute: () => Promise<void>;
    queuedAt: number;
  }> = [];
  
  /** 正在运行的任务集合 */
  private running: Set<string> = new Set();
  
  /** 任务信息映射 */
  private taskInfo: Map<string, TaskInfo> = new Map();
  
  /** 最大并发任务数 */
  private maxConcurrent: number;
  
  /** 任务完成回调 */
  private onTaskComplete?: (taskId: string, success: boolean) => void;
  
  /**
   * 构造函数
   * @param maxConcurrentOverride 可选的最大并发任务数覆盖值
   */
  constructor(maxConcurrentOverride?: number) {
    // 根据CPU核心数设置最大并发任务数
    const cpuCount = os.cpus().length;
    
    // 如果提供了覆盖值，使用覆盖值；否则根据CPU核心数计算
    // 默认使用CPU核心数减1，但不少于1个，不多于4个
    this.maxConcurrent = maxConcurrentOverride !== undefined
      ? maxConcurrentOverride
      : Math.max(1, Math.min(cpuCount - 1, 4));
    
    logger.info(`任务队列初始化，最大并发任务数: ${this.maxConcurrent}`);
  }
  
  /**
   * 将任务添加到队列
   * @param taskId 任务ID
   * @param execute 任务执行函数
   * @returns 队列中的位置（0表示立即执行）
   */
  public enqueue(taskId: string, execute: () => Promise<void>): number {
    // 检查任务是否已经在队列中
    if (this.isTaskQueued(taskId) || this.isTaskRunning(taskId)) {
      logger.warn(`任务 ${taskId} 已经在队列中或正在运行`);
      return this.getQueuePosition(taskId);
    }
    
    // 添加任务到队列
    this.queue.push({ 
      taskId, 
      execute, 
      queuedAt: Date.now() 
    });
    
    // 更新任务信息
    this.taskInfo.set(taskId, {
      taskId,
      status: TaskStatus.QUEUED,
      queuedAt: Date.now()
    });
    
    const queuePosition = this.getQueuePosition(taskId);
    logger.info(`任务 ${taskId} 已添加到队列，当前位置: ${queuePosition}`);
    
    // 尝试处理队列
    this.processQueue();
    
    return queuePosition;
  }
  
  /**
   * 从队列中移除任务
   * @param taskId 任务ID
   * @returns 是否成功移除
   */
  public dequeue(taskId: string): boolean {
    // 如果任务正在运行，不能直接移除
    if (this.isTaskRunning(taskId)) {
      logger.warn(`任务 ${taskId} 正在运行，无法从队列中移除`);
      return false;
    }
    
    // 从队列中移除任务
    const initialLength = this.queue.length;
    this.queue = this.queue.filter(task => task.taskId !== taskId);
    
    // 如果任务被移除，更新任务信息
    if (initialLength > this.queue.length) {
      const info = this.taskInfo.get(taskId);
      if (info) {
        info.status = TaskStatus.CANCELLED;
        info.completedAt = Date.now();
      }
      logger.info(`任务 ${taskId} 已从队列中移除`);
      return true;
    }
    
    logger.warn(`任务 ${taskId} 不在队列中，无法移除`);
    return false;
  }
  
  /**
   * 标记任务完成
   * @param taskId 任务ID
   * @param success 是否成功完成
   */
  public markTaskComplete(taskId: string, success: boolean, error?: string): void {
    // 从运行集合中移除任务
    this.running.delete(taskId);
    
    // 更新任务信息
    const info = this.taskInfo.get(taskId);
    if (info) {
      info.status = success ? TaskStatus.COMPLETED : TaskStatus.FAILED;
      info.completedAt = Date.now();
      if (error) {
        info.error = error;
      }
    }
    
    // 调用完成回调
    if (this.onTaskComplete) {
      this.onTaskComplete(taskId, success);
    }
    
    logger.info(`任务 ${taskId} 已${success ? '成功' : '失败'}完成`);
    
    // 处理队列中的下一个任务
    this.processQueue();
  }
  
  /**
   * 设置任务完成回调
   * @param callback 回调函数
   */
  public setTaskCompleteCallback(callback: (taskId: string, success: boolean) => void): void {
    this.onTaskComplete = callback;
  }
  
  /**
   * 获取任务在队列中的位置
   * @param taskId 任务ID
   * @returns 队列位置（0表示正在运行，-1表示不在队列中）
   */
  public getQueuePosition(taskId: string): number {
    // 如果任务正在运行，返回0
    if (this.isTaskRunning(taskId)) {
      return 0;
    }
    
    // 查找任务在队列中的位置
    const index = this.queue.findIndex(task => task.taskId === taskId);
    return index >= 0 ? index + 1 : -1;
  }
  
  /**
   * 获取任务信息
   * @param taskId 任务ID
   * @returns 任务信息，如果不存在则返回undefined
   */
  public getTaskInfo(taskId: string): TaskInfo | undefined {
    return this.taskInfo.get(taskId);
  }
  
  /**
   * 获取所有任务信息
   * @returns 所有任务信息的数组
   */
  public getAllTaskInfo(): TaskInfo[] {
    return Array.from(this.taskInfo.values());
  }
  
  /**
   * 清理旧的任务信息
   * @param maxAgeHours 最大保留时间（小时）
   */
  public cleanupOldTasks(maxAgeHours: number = 24): void {
    const now = Date.now();
    const maxAgeMs = maxAgeHours * 3600000; // 转换为毫秒
    const cutoffTime = now - maxAgeMs;
    
    // 找出需要清理的任务
    const tasksToCleanup: string[] = [];
    
    for (const [taskId, info] of this.taskInfo.entries()) {
      // 只清理已完成或失败的任务
      if (
        (info.status === TaskStatus.COMPLETED || 
         info.status === TaskStatus.FAILED || 
         info.status === TaskStatus.CANCELLED) && 
        info.completedAt && 
        info.completedAt < cutoffTime
      ) {
        tasksToCleanup.push(taskId);
      }
    }
    
    // 清理任务信息
    for (const taskId of tasksToCleanup) {
      this.taskInfo.delete(taskId);
    }
    
    if (tasksToCleanup.length > 0) {
      logger.info(`已清理 ${tasksToCleanup.length} 个旧任务信息`);
    }
  }
  
  /**
   * 检查任务是否在队列中
   * @param taskId 任务ID
   * @returns 是否在队列中
   */
  private isTaskQueued(taskId: string): boolean {
    return this.queue.some(task => task.taskId === taskId);
  }
  
  /**
   * 检查任务是否正在运行
   * @param taskId 任务ID
   * @returns 是否正在运行
   */
  private isTaskRunning(taskId: string): boolean {
    return this.running.has(taskId);
  }
  
  /**
   * 处理队列中的任务
   */
  private async processQueue(): Promise<void> {
    // 如果没有任务或已达到最大并发数，直接返回
    if (this.queue.length === 0 || this.running.size >= this.maxConcurrent) {
      return;
    }
    
    // 获取队列中的下一个任务
    const task = this.queue.shift();
    if (!task) return;
    
    // 将任务添加到运行集合
    this.running.add(task.taskId);
    
    // 更新任务信息
    const info = this.taskInfo.get(task.taskId);
    if (info) {
      info.status = TaskStatus.RUNNING;
      info.startedAt = Date.now();
    }
    
    logger.info(`开始执行任务 ${task.taskId}，队列中剩余 ${this.queue.length} 个任务`);
    
    // 执行任务
    try {
      await task.execute();
      // 任务成功完成
      this.markTaskComplete(task.taskId, true);
    } catch (error) {
      // 任务执行失败
      const errorMessage = error instanceof Error ? error.message : "未知错误";
      logger.error(`任务 ${task.taskId} 执行失败: ${errorMessage}`);
      this.markTaskComplete(task.taskId, false, errorMessage);
    }
  }
}
