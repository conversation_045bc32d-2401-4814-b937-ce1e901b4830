import { ChildProcess } from "child_process";
import logger from "../utils/logger";

// 常量定义
const CONSTANTS = {
  CLEANUP_INTERVAL: 300000, // 5分钟
  STALE_TASK_THRESHOLD: 3600000, // 1小时
  COMMAND_GENERATION_PROGRESS_RATIO: 0.1, // 命令生成占总进度的20%
  RENDERING_PROGRESS_RATIO: 0.78, // 渲染占总进度的78%
  MIN_PROGRESS_UPDATE_INTERVAL: 2000, // 最小进度更新间隔2秒
  FINALIZATION_TIMEOUT: 15000, // 最终化超时15秒
  PROGRESS_UPDATE_THRESHOLD: 1, // 进度更新阈值1%
  DEFAULT_FPS: 30, // 默认帧率
  MAX_PROGRESS_PERCENT: 95, // 最大进度百分比
} as const;

// 渲染阶段定义
const RENDERING_STAGES = [
  { threshold: 10, name: "initializing" },
  { threshold: 25, name: "video processing" },
  { threshold: 50, name: "audio processing" },
  { threshold: 75, name: "effect rendering" },
  { threshold: 90, name: "generating" },
] as const;

export interface ProgressInfo {
  status: "pending" | "processing" | "completed" | "failed" | "cancelled";
  progress: number;
  currentFrame: number | null;
  totalFrames: number | null;
  fps: number | null;
  speed: number | null;
  error?: string;
  lastUpdate: number;
  stage?: string;
  downloadUrl?: string;
}

interface FFmpegProgressData {
  duration: number | null;
  totalFrames: number | null;
  lastTimeProgress: number;
  wasCancelled: boolean;
  hasStartedProcessing: boolean;
  lastProgressUpdate: number;
  framesSinceLastProgressUpdate: number;
  secondsPassed: number;
  finalizationStage: number;
  finalizationStartTime: number;
  endProcessingTimeout: NodeJS.Timeout | null;
}

interface ParsedFFmpegData {
  time?: number;
  fps?: number;
  speed?: number;
  frame?: number;
}

export class ProgressTracker {
  private readonly progressMap = new Map<string, ProgressInfo>();
  private readonly cleanupInterval: NodeJS.Timeout;

  constructor() {
    this.cleanupInterval = setInterval(
      this.cleanupStaleTasks.bind(this),
      CONSTANTS.CLEANUP_INTERVAL
    );
  }

  private cleanupStaleTasks(): void {
    const staleThreshold = Date.now() - CONSTANTS.STALE_TASK_THRESHOLD;
    for (const [taskId, info] of this.progressMap.entries()) {
      if (info.lastUpdate < staleThreshold) {
        logger.info(`Cleaning up stale task ${taskId}`);
        this.progressMap.delete(taskId);
      }
    }
  }

  public initializeTask(taskId: string): void {
    this.progressMap.set(taskId, {
      status: "pending",
      progress: 0,
      currentFrame: null,
      totalFrames: null,
      fps: null,
      speed: null,
      lastUpdate: Date.now(),
      stage: "initializing",
    });
    logger.info(`Initialized new task ${taskId}`);
  }

  public getProgress(taskId: string): ProgressInfo | undefined {
    return this.progressMap.get(taskId);
  }

  public setDownloadUrl(taskId: string, downloadUrl: string): void {
    const currentInfo = this.progressMap.get(taskId);
    if (currentInfo) {
      this.updateTaskProgress(taskId, {
        ...currentInfo,
        downloadUrl,
      });
      logger.info(`Set download URL for task ${taskId}: ${downloadUrl}`);
    }
  }

  public updateCommandGenerationProgress(
    taskId: string,
    stage: string,
    progress: number
  ): void {
    const currentInfo = this.progressMap.get(taskId);
    if (!currentInfo) return;

    const adjustedProgress = Math.floor(
      progress * CONSTANTS.COMMAND_GENERATION_PROGRESS_RATIO
    );

    this.updateTaskProgress(taskId, {
      ...currentInfo,
      status: "processing",
      progress: adjustedProgress,
      stage,
    });

    logger.debug(
      `Command generation progress update for task ${taskId}: ${stage} - ${adjustedProgress}%`
    );
  }

  public trackFFmpegProgress(taskId: string, ffmpeg: ChildProcess): void {
    const progressData: FFmpegProgressData = {
      duration: null,
      totalFrames: null,
      lastTimeProgress: 0,
      wasCancelled: false,
      hasStartedProcessing: false,
      lastProgressUpdate: Date.now(),
      framesSinceLastProgressUpdate: 0,
      secondsPassed: 0,
      finalizationStage: 0,
      finalizationStartTime: 0,
      endProcessingTimeout: null,
    };

    this.setupFFmpegStderrHandlers(taskId, ffmpeg, progressData);
    this.setupFFmpegCloseHandler(taskId, ffmpeg, progressData);
  }

  private setupFFmpegStderrHandlers(
    taskId: string,
    ffmpeg: ChildProcess,
    progressData: FFmpegProgressData
  ): void {
    ffmpeg.stderr?.on("data", (data: Buffer) => {
      const line = data.toString();

      this.handleFFmpegInitialization(taskId, line, progressData);
      this.extractDurationInfo(line, progressData);
      this.handleProgressUpdate(taskId, line, progressData);
      this.handleFinalizationStage(taskId, line, progressData);
    });
  }

  private handleFFmpegInitialization(
    taskId: string,
    line: string,
    progressData: FFmpegProgressData
  ): void {
    if (!progressData.hasStartedProcessing) {
      progressData.hasStartedProcessing =
        line.includes("frame=") ||
        line.includes("time=") ||
        line.includes("stream mapping");

      if (progressData.hasStartedProcessing) {
        const currentInfo = this.progressMap.get(taskId);
        if (currentInfo) {
          this.updateTaskProgress(taskId, {
            ...currentInfo,
            status: "processing",
            stage: "initializing",
            progress: Math.floor(
              CONSTANTS.COMMAND_GENERATION_PROGRESS_RATIO * 100
            ),
          });
        }
      }
    }
  }

  private extractDurationInfo(
    line: string,
    progressData: FFmpegProgressData
  ): void {
    if (!progressData.duration) {
      const durationMatch = line.match(
        /Duration: (\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?/
      );
      if (durationMatch) {
        const [, hours, minutes, seconds, milliseconds = "0"] = durationMatch;
        progressData.duration =
          parseInt(hours) * 3600 +
          parseInt(minutes) * 60 +
          parseInt(seconds) +
          parseInt(milliseconds.padEnd(3, "0")) / 1000;

        const fpsMatch = line.match(/(\d+(?:\.\d+)?) fps/);
        const fps = fpsMatch ? parseFloat(fpsMatch[1]) : CONSTANTS.DEFAULT_FPS;
        progressData.totalFrames = Math.round(progressData.duration * fps);

        logger.debug(
          `精确检测到视频时长: ${progressData.duration}s, 估计帧率: ${fps}, 总帧数: ${progressData.totalFrames}`
        );
      }
    }
  }

  private parseFFmpegLine(line: string): ParsedFFmpegData {
    const result: ParsedFFmpegData = {};

    const timeMatch = line.match(/time=(\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?/);
    if (timeMatch) {
      const [, hours, minutes, seconds, milliseconds = "0"] = timeMatch;
      result.time =
        parseInt(hours) * 3600 +
        parseInt(minutes) * 60 +
        parseInt(seconds) +
        parseInt(milliseconds.padEnd(3, "0")) / 1000;
    }

    const fpsMatch = line.match(/fps=\s*(\d+(?:\.\d+)?)/);
    if (fpsMatch) {
      result.fps = parseFloat(fpsMatch[1]);
    }

    const speedMatch = line.match(/speed=\s*(\d+\.?\d*x)/);
    if (speedMatch) {
      result.speed = parseFloat(speedMatch[1]);
    }

    const frameMatch = line.match(/frame=\s*(\d+)/);
    if (frameMatch) {
      result.frame = parseInt(frameMatch[1]);
    }

    return result;
  }

  private handleProgressUpdate(
    taskId: string,
    line: string,
    progressData: FFmpegProgressData
  ): void {
    const parsedData = this.parseFFmpegLine(line);

    if (!Object.keys(parsedData).length) return;

    const currentInfo = this.progressMap.get(taskId);
    if (!currentInfo || currentInfo.status === "cancelled") {
      if (currentInfo?.status === "cancelled") {
        progressData.wasCancelled = true;
      }
      return;
    }

    const updatedInfo = this.calculateProgress(
      currentInfo,
      parsedData,
      progressData
    );
    this.updateTaskProgress(taskId, updatedInfo);
    this.logProgressIfNeeded(taskId, updatedInfo, progressData);
  }

  private calculateProgress(
    currentInfo: ProgressInfo,
    parsedData: ParsedFFmpegData,
    progressData: FFmpegProgressData
  ): ProgressInfo {
    const updatedInfo: ProgressInfo = {
      ...currentInfo,
      status: "processing",
      stage: currentInfo.stage || "rendering",
      lastUpdate: Date.now(),
    };

    // 处理时间进度
    if (parsedData.time !== undefined) {
      progressData.secondsPassed = parsedData.time;

      if (progressData.duration) {
        const progressPercent = (parsedData.time / progressData.duration) * 100;
        const rawProgress = Math.min(
          CONSTANTS.MAX_PROGRESS_PERCENT,
          progressPercent
        );
        const scaledProgress =
          CONSTANTS.COMMAND_GENERATION_PROGRESS_RATIO * 100 +
          rawProgress * CONSTANTS.RENDERING_PROGRESS_RATIO;
        updatedInfo.progress = Math.floor(scaledProgress);

        // 更新渲染阶段
        this.updateRenderingStage(updatedInfo, progressPercent);

        if (
          Math.abs(updatedInfo.progress - progressData.lastTimeProgress) >=
          CONSTANTS.PROGRESS_UPDATE_THRESHOLD
        ) {
          progressData.lastTimeProgress = updatedInfo.progress;
        }
      }
    }

    // 处理帧进度
    if (parsedData.frame !== undefined) {
      updatedInfo.currentFrame = parsedData.frame;
      updatedInfo.totalFrames = progressData.totalFrames;

      this.updateFrameProgress(updatedInfo, parsedData, progressData);
    }

    // 处理FPS和速度
    if (parsedData.fps !== undefined) {
      updatedInfo.fps = parsedData.fps;
      this.recalculateTotalFrames(progressData, parsedData.fps);
    }

    if (parsedData.speed !== undefined) {
      updatedInfo.speed = parsedData.speed;
      this.adjustProgressForSpeed(updatedInfo, parsedData.speed);
    }

    return updatedInfo;
  }

  private updateRenderingStage(
    updatedInfo: ProgressInfo,
    progressPercent: number
  ): void {
    for (let i = RENDERING_STAGES.length - 1; i >= 0; i--) {
      if (progressPercent >= RENDERING_STAGES[i].threshold) {
        updatedInfo.stage = RENDERING_STAGES[i].name;
        break;
      }
    }
  }

  private updateFrameProgress(
    updatedInfo: ProgressInfo,
    parsedData: ParsedFFmpegData,
    progressData: FFmpegProgressData
  ): void {
    const now = Date.now();
    const elapsedMs = now - progressData.lastProgressUpdate;

    if (elapsedMs > CONSTANTS.MIN_PROGRESS_UPDATE_INTERVAL) {
      progressData.framesSinceLastProgressUpdate = 0;
      progressData.lastProgressUpdate = now;
    }
    progressData.framesSinceLastProgressUpdate++;

    // 使用帧数作为进度补充
    if (
      progressData.totalFrames &&
      parsedData.frame &&
      (!parsedData.time || !progressData.duration)
    ) {
      const frameProgress = Math.min(
        CONSTANTS.MAX_PROGRESS_PERCENT,
        (parsedData.frame / progressData.totalFrames) * 100
      );
      const scaledProgress =
        CONSTANTS.COMMAND_GENERATION_PROGRESS_RATIO * 100 +
        frameProgress * CONSTANTS.RENDERING_PROGRESS_RATIO;
      updatedInfo.progress = Math.floor(scaledProgress);
    }
  }

  private recalculateTotalFrames(
    progressData: FFmpegProgressData,
    fps: number
  ): void {
    if (!progressData.totalFrames && progressData.duration) {
      progressData.totalFrames = Math.round(progressData.duration * fps);
    }
  }

  private adjustProgressForSpeed(
    updatedInfo: ProgressInfo,
    speed: number
  ): void {
    // 如果处理速度很慢(<0.5x)，适当降低显示进度
    if (speed < 0.5 && updatedInfo.progress > 50) {
      updatedInfo.progress = Math.max(50, updatedInfo.progress - 5);
    }
  }

  private handleFinalizationStage(
    taskId: string,
    line: string,
    progressData: FFmpegProgressData
  ): void {
    this.handleEncodingCompletion(taskId, line, progressData);
    this.handleStreamWriting(taskId, line, progressData);
    this.handleMuxingCompletion(taskId, line, progressData);
  }

  private handleEncodingCompletion(
    taskId: string,
    line: string,
    progressData: FFmpegProgressData
  ): void {
    if (
      progressData.finalizationStage === 0 &&
      line.includes("frame=") &&
      line.includes("time=") &&
      line.includes("fps=") &&
      (line.includes("L q=") || line.includes("speed="))
    ) {
      const timeMatch = line.match(/time=(\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?/);
      if (timeMatch && progressData.duration) {
        const [, hours, minutes, seconds] = timeMatch;
        const currentTime =
          parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds);
        const progressPercent = (currentTime / progressData.duration) * 100;

        if (progressPercent >= 98) {
          progressData.finalizationStage = 1;
          progressData.finalizationStartTime = Date.now();
          this.updateFinalizationProgress(taskId, 96, "finalizing");
        }
      }
    }
  }

  private handleStreamWriting(
    taskId: string,
    line: string,
    progressData: FFmpegProgressData
  ): void {
    if (
      (progressData.finalizationStage === 0 ||
        progressData.finalizationStage === 1) &&
      (line.includes("Writing stream header") ||
        line.includes("Starting second pass") ||
        line.includes("Qavg:") ||
        line.includes("video:"))
    ) {
      progressData.finalizationStage = 2;
      this.updateFinalizationProgress(taskId, 98, "写入文件");
    }
  }

  private handleMuxingCompletion(
    taskId: string,
    line: string,
    progressData: FFmpegProgressData
  ): void {
    if (
      (progressData.finalizationStage === 1 ||
        progressData.finalizationStage === 2) &&
      (line.includes("muxing overhead") ||
        line.includes("video:") ||
        line.includes("audio:") ||
        line.includes("global headers") ||
        line.includes("Writing trailer"))
    ) {
      progressData.finalizationStage = 3;
      this.updateFinalizationProgress(taskId, 99, "封装完成");
      this.setupFinalizationTimeout(taskId, progressData);
    }
  }

  private updateFinalizationProgress(
    taskId: string,
    progress: number,
    stage: string
  ): void {
    const currentInfo = this.progressMap.get(taskId);
    if (currentInfo && currentInfo.status === "processing") {
      this.updateTaskProgress(taskId, {
        ...currentInfo,
        progress,
        stage,
      });
      logger.info(`任务 ${taskId} ${stage} (${progress}%)`);
    }
  }

  private setupFinalizationTimeout(
    taskId: string,
    progressData: FFmpegProgressData
  ): void {
    const now = Date.now();
    const finalizationElapsed = now - progressData.finalizationStartTime;
    const timeoutDuration =
      finalizationElapsed > CONSTANTS.FINALIZATION_TIMEOUT
        ? 5000
        : CONSTANTS.FINALIZATION_TIMEOUT;

    this.clearTimeout(progressData);

    progressData.endProcessingTimeout = setTimeout(() => {
      const info = this.progressMap.get(taskId);
      if (info && info.status === "processing" && info.progress >= 99) {
        this.updateTaskProgress(taskId, {
          ...info,
          status: "completed",
          progress: 100,
          stage: "已完成",
        });
        logger.warn(`任务 ${taskId} 完成超时，强制更新到100%`);
      }
    }, timeoutDuration);
  }

  private setupFFmpegCloseHandler(
    taskId: string,
    ffmpeg: ChildProcess,
    progressData: FFmpegProgressData
  ): void {
    ffmpeg.on("close", (code: number) => {
      this.clearTimeout(progressData);

      const currentStatus = this.progressMap.get(taskId)?.status;
      if (currentStatus === "cancelled" || progressData.wasCancelled) {
        logger.info(`任务 ${taskId} 已被手动取消`);
        return;
      }

      if (code === 0) {
        this.handleSuccessfulCompletion(taskId, progressData);
      } else {
        this.handleFailedCompletion(taskId, code);
      }
    });
  }

  private handleSuccessfulCompletion(
    taskId: string,
    progressData: FFmpegProgressData
  ): void {
    const elapsedTime = (Date.now() - progressData.lastProgressUpdate) / 1000;
    logger.info(`任务 ${taskId} 成功完成 (耗时: ${elapsedTime.toFixed(1)}秒)`);

    this.updateTaskProgress(taskId, {
      status: "completed",
      progress: 100,
      currentFrame: progressData.totalFrames,
      totalFrames: progressData.totalFrames,
      fps: null,
      speed: null,
      stage: "已完成",
    });
  }

  private handleFailedCompletion(taskId: string, code: number): void {
    logger.error(`任务 ${taskId} 失败，退出代码: ${code}`);

    this.updateTaskProgress(taskId, {
      status: "failed",
      progress: 0,
      currentFrame: null,
      totalFrames: null,
      fps: null,
      speed: null,
      stage: "处理失败",
      error: `进程退出，代码 ${code}`,
    });
  }

  private updateTaskProgress(
    taskId: string,
    update: Partial<ProgressInfo>
  ): void {
    const currentInfo = this.progressMap.get(taskId);
    if (!currentInfo) return;

    this.progressMap.set(taskId, {
      ...currentInfo,
      ...update,
      lastUpdate: Date.now(),
    });
  }

  private logProgressIfNeeded(
    taskId: string,
    updatedInfo: ProgressInfo,
    progressData: FFmpegProgressData
  ): void {
    if (
      progressData.secondsPassed > 10 ||
      Math.abs(updatedInfo.progress - progressData.lastTimeProgress) >= 5
    ) {
      logger.debug(
        `任务 ${taskId} 进度更新: ${updatedInfo.progress}%, 阶段: ${updatedInfo.stage}`
      );
    }
  }

  private clearTimeout(progressData: FFmpegProgressData): void {
    if (progressData.endProcessingTimeout) {
      clearTimeout(progressData.endProcessingTimeout);
      progressData.endProcessingTimeout = null;
    }
  }

  public cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.progressMap.clear();
    logger.info("Progress tracker cleaned up");
  }
}
