import { MediaRepository } from "../repositories/MediaRepository";
import { UserStorageQuota } from "../types/media";
import logger from "../utils/logger";
import { quotaConfig, UserQuotaConfig } from "../config/appConfig";

/**
 * 配额策略配置
 */
export interface QuotaPolicy {
  /** 用户类型 */
  userType: "free" | "premium" | "enterprise" | "admin";
  /** 存储限制（字节） */
  storageLimit: number;
  /** 文件数量限制 */
  fileCountLimit: number;
  /** 单个文件大小限制（字节） */
  maxFileSize: number;
  /** 每日上传限制（字节） */
  dailyUploadLimit?: number;
  /** 每月上传限制（字节） */
  monthlyUploadLimit?: number;
  /** 允许的文件类型 */
  allowedFileTypes?: string[];
}

/**
 * 配额使用统计
 */
export interface QuotaUsageStats {
  userId: string;
  userType: string;
  currentUsage: {
    storageUsed: number;
    fileCount: number;
    storagePercentage: number;
    fileCountPercentage: number;
  };
  limits: {
    storageLimit: number;
    fileCountLimit: number;
    maxFileSize: number;
  };
  dailyUsage?: {
    uploadedToday: number;
    dailyLimit?: number;
    dailyPercentage?: number;
  };
  monthlyUsage?: {
    uploadedThisMonth: number;
    monthlyLimit?: number;
    monthlyPercentage?: number;
  };
  warnings: string[];
}

/**
 * 配额检查结果
 */
export interface QuotaCheckResult {
  allowed: boolean;
  reason?: string;
  remainingStorage?: number;
  remainingFileCount?: number;
  warnings?: string[];
}

/**
 * 用户配额管理服务
 */
export class QuotaManagementService {
  private mediaRepository: MediaRepository;
  private quotaPolicies: Map<string, QuotaPolicy> = new Map();

  constructor() {
    this.mediaRepository = new MediaRepository();
    this.initializeDefaultPolicies();
  }

  /**
   * 初始化默认配额策略
   */
  private initializeDefaultPolicies(): void {
    // 从配置中加载用户配额策略
    this.quotaPolicies.set("free", quotaConfig.free);
    this.quotaPolicies.set("premium", quotaConfig.premium);
    this.quotaPolicies.set("enterprise", quotaConfig.enterprise);
    this.quotaPolicies.set("admin", quotaConfig.admin);

    logger.info("Default quota policies initialized from config");
    logger.debug("Quota policies:", {
      free: quotaConfig.free,
      premium: quotaConfig.premium,
      enterprise: quotaConfig.enterprise,
      admin: quotaConfig.admin,
    });
  }

  /**
   * 检查用户是否可以上传文件
   */
  async checkUploadQuota(
    userId: string,
    fileSize: number,
    userType: string = "free"
  ): Promise<QuotaCheckResult> {
    const policy = this.quotaPolicies.get(userType);
    if (!policy) {
      return {
        allowed: false,
        reason: `Unknown user type: ${userType}`,
      };
    }

    // 检查单个文件大小限制
    if (fileSize > policy.maxFileSize) {
      return {
        allowed: false,
        reason: `File size ${this.formatBytes(
          fileSize
        )} exceeds maximum allowed size ${this.formatBytes(
          policy.maxFileSize
        )}`,
      };
    }

    // 获取用户当前配额
    const quota = await this.mediaRepository.getUserStorageQuota(userId);
    const currentUsage = await this.mediaRepository.calculateUserStorage(
      userId
    );

    // 检查存储空间限制
    if (currentUsage.usedStorage + fileSize > policy.storageLimit) {
      return {
        allowed: false,
        reason: `Storage quota exceeded. Current: ${this.formatBytes(
          currentUsage.usedStorage
        )}, Limit: ${this.formatBytes(policy.storageLimit)}`,
        remainingStorage: Math.max(
          0,
          policy.storageLimit - currentUsage.usedStorage
        ),
      };
    }

    // 检查文件数量限制
    if (currentUsage.fileCount >= policy.fileCountLimit) {
      return {
        allowed: false,
        reason: `File count limit exceeded. Current: ${currentUsage.fileCount}, Limit: ${policy.fileCountLimit}`,
        remainingFileCount: Math.max(
          0,
          policy.fileCountLimit - currentUsage.fileCount
        ),
      };
    }

    // 检查每日上传限制
    if (policy.dailyUploadLimit) {
      const dailyUsage = await this.getDailyUploadUsage(userId);
      if (dailyUsage + fileSize > policy.dailyUploadLimit) {
        return {
          allowed: false,
          reason: `Daily upload limit exceeded. Today: ${this.formatBytes(
            dailyUsage
          )}, Limit: ${this.formatBytes(policy.dailyUploadLimit)}`,
        };
      }
    }

    // 检查每月上传限制
    if (policy.monthlyUploadLimit) {
      const monthlyUsage = await this.getMonthlyUploadUsage(userId);
      if (monthlyUsage + fileSize > policy.monthlyUploadLimit) {
        return {
          allowed: false,
          reason: `Monthly upload limit exceeded. This month: ${this.formatBytes(
            monthlyUsage
          )}, Limit: ${this.formatBytes(policy.monthlyUploadLimit)}`,
        };
      }
    }

    // 生成警告
    const warnings: string[] = [];
    const storagePercentage =
      ((currentUsage.usedStorage + fileSize) / policy.storageLimit) * 100;
    const fileCountPercentage =
      ((currentUsage.fileCount + 1) / policy.fileCountLimit) * 100;

    if (storagePercentage > 80) {
      warnings.push(
        `Storage usage will be ${storagePercentage.toFixed(1)}% after upload`
      );
    }

    if (fileCountPercentage > 80) {
      warnings.push(
        `File count will be ${fileCountPercentage.toFixed(
          1
        )}% of limit after upload`
      );
    }

    return {
      allowed: true,
      remainingStorage:
        policy.storageLimit - currentUsage.usedStorage - fileSize,
      remainingFileCount: policy.fileCountLimit - currentUsage.fileCount - 1,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  /**
   * 获取用户配额使用统计
   */
  async getQuotaUsageStats(
    userId: string,
    userType: string = "free"
  ): Promise<QuotaUsageStats> {
    const policy = this.quotaPolicies.get(userType);
    if (!policy) {
      throw new Error(`Unknown user type: ${userType}`);
    }

    const currentUsage = await this.mediaRepository.calculateUserStorage(
      userId
    );
    const dailyUsage = await this.getDailyUploadUsage(userId);
    const monthlyUsage = await this.getMonthlyUploadUsage(userId);

    const warnings: string[] = [];
    const storagePercentage =
      (currentUsage.usedStorage / policy.storageLimit) * 100;
    const fileCountPercentage =
      (currentUsage.fileCount / policy.fileCountLimit) * 100;

    if (storagePercentage > 90) {
      warnings.push("Storage usage is over 90%");
    } else if (storagePercentage > 80) {
      warnings.push("Storage usage is over 80%");
    }

    if (fileCountPercentage > 90) {
      warnings.push("File count is over 90% of limit");
    } else if (fileCountPercentage > 80) {
      warnings.push("File count is over 80% of limit");
    }

    const stats: QuotaUsageStats = {
      userId,
      userType,
      currentUsage: {
        storageUsed: currentUsage.usedStorage,
        fileCount: currentUsage.fileCount,
        storagePercentage,
        fileCountPercentage,
      },
      limits: {
        storageLimit: policy.storageLimit,
        fileCountLimit: policy.fileCountLimit,
        maxFileSize: policy.maxFileSize,
      },
      warnings,
    };

    if (policy.dailyUploadLimit) {
      stats.dailyUsage = {
        uploadedToday: dailyUsage,
        dailyLimit: policy.dailyUploadLimit,
        dailyPercentage: (dailyUsage / policy.dailyUploadLimit) * 100,
      };
    }

    if (policy.monthlyUploadLimit) {
      stats.monthlyUsage = {
        uploadedThisMonth: monthlyUsage,
        monthlyLimit: policy.monthlyUploadLimit,
        monthlyPercentage: (monthlyUsage / policy.monthlyUploadLimit) * 100,
      };
    }

    return stats;
  }

  /**
   * 获取每日上传使用量
   */
  private async getDailyUploadUsage(userId: string): Promise<number> {
    // 获取今日开始时间
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      // 查询今日上传的文件
      const mediaList = await this.mediaRepository.getMediaByUser(userId, {
        page: 1,
        limit: 1000, // 假设用户一天不会上传超过1000个文件
      });

      // 计算今日上传的文件总大小
      const todayUploads = mediaList.items.filter(
        (media) => new Date(media.uploadDate) >= today
      );

      return todayUploads.reduce((total, media) => total + media.fileSize, 0);
    } catch (error) {
      logger.error(
        `Failed to calculate daily upload usage for user ${userId}:`,
        error
      );
      return 0;
    }
  }

  /**
   * 获取每月上传使用量
   */
  private async getMonthlyUploadUsage(userId: string): Promise<number> {
    // 获取本月开始时间
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);

    try {
      // 查询本月上传的文件
      const mediaList = await this.mediaRepository.getMediaByUser(userId, {
        page: 1,
        limit: 10000, // 假设用户一个月不会上传超过10000个文件
      });

      // 计算本月上传的文件总大小
      const thisMonthUploads = mediaList.items.filter(
        (media) => new Date(media.uploadDate) >= thisMonth
      );

      return thisMonthUploads.reduce(
        (total, media) => total + media.fileSize,
        0
      );
    } catch (error) {
      logger.error(
        `Failed to calculate monthly upload usage for user ${userId}:`,
        error
      );
      return 0;
    }
  }

  /**
   * 更新用户配额策略
   */
  async updateUserQuota(
    userId: string,
    updates: Partial<UserStorageQuota>
  ): Promise<UserStorageQuota> {
    return await this.mediaRepository.updateUserStorageQuota(userId, updates);
  }

  /**
   * 设置配额策略
   */
  setQuotaPolicy(userType: string, policy: QuotaPolicy): void {
    this.quotaPolicies.set(userType, policy);
    logger.info(`Quota policy updated for user type: ${userType}`, policy);
  }

  /**
   * 获取配额策略
   */
  getQuotaPolicy(userType: string): QuotaPolicy | undefined {
    return this.quotaPolicies.get(userType);
  }

  /**
   * 获取所有配额策略
   */
  getAllQuotaPolicies(): { [userType: string]: QuotaPolicy } {
    const policies: { [userType: string]: QuotaPolicy } = {};
    for (const [userType, policy] of this.quotaPolicies.entries()) {
      policies[userType] = policy;
    }
    return policies;
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
}
