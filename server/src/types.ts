export interface CanvasState {
  width: number;
  height: number;
  backgroundColor: string;
  elements: CanvasElement[];
  outputFormat?: OutputFormat;
}

export interface OutputFormat {
  codec: string;
  format: string;
  quality: "low" | "medium" | "high";
  frameRate: number;
}

export interface CanvasElement {
  type: "video" | "image" | "audio" | "text" | "shape" | "gif";
  id: string;
  properties: any;
  opacity?: number;
  placement?: ElementPlacement;
  timeFrame: TimeFrame;
  effects?: Effect[];
  border?: Border;
  transition?: Transition;
}

/**
 * Defines transition effects for a media element
 */
export interface Transition {
  /** Transition effect for appearing */
  in?: string;
  /** Transition effect for disappearing */
  out?: string;
  /** Transition duration in seconds (for backward compatibility) */
  duration?: number;
  /** Specific duration for in transition in seconds */
  inDuration?: number;
  /** Specific duration for out transition in seconds */
  outDuration?: number;
}

export interface ElementPlacement {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  scaleX: number;
  scaleY: number;
  flipX?: boolean;
  flipY?: boolean;
  // 剪裁参数
  cropX?: number;
  cropY?: number;
  cropWidth?: number;
  cropHeight?: number;
}

export interface TimeFrame {
  start: number;
  end: number;
}

export interface Effect {
  type: string;
  params: any;
}

export interface Border {
  color: string;
  width: number;
  style: string;
  borderRadius?: number;
}

export interface TaskProgress {
  taskId: string;
  progress: number;
  status: "pending" | "processing" | "completed" | "failed";
  error?: string;
}

export interface VideoGenerationResponse {
  taskId: string;
}

export interface ProgressResponse {
  progress: number;
  status: "pending" | "processing" | "completed" | "failed";
  error?: string;
}
