import fs from "fs/promises";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import logger from "../utils/logger";
import {
  MediaMetadata,
  MediaListQuery,
  MediaListResponse,
  UserStorageQuota,
} from "../types/media";

/**
 * 媒体元数据存储库
 * 使用文件系统存储，与现有模板系统保持一致
 */
export class MediaRepository {
  private readonly dataDir: string;
  private readonly mediaFile: string;
  private readonly quotaFile: string;

  constructor() {
    this.dataDir = path.join(process.cwd(), "data");
    this.mediaFile = path.join(this.dataDir, "media.json");
    this.quotaFile = path.join(this.dataDir, "user-quotas.json");
    this.ensureDataDirectory();
  }

  /**
   * 确保数据目录存在
   */
  private async ensureDataDirectory(): Promise<void> {
    try {
      await fs.access(this.dataDir);
    } catch {
      await fs.mkdir(this.dataDir, { recursive: true });
      logger.info(`Created data directory: ${this.dataDir}`);
    }
  }

  /**
   * 加载媒体元数据
   */
  private async loadMedia(): Promise<MediaMetadata[]> {
    try {
      const data = await fs.readFile(this.mediaFile, "utf-8");
      const media = JSON.parse(data);

      // 转换日期字符串为Date对象
      return media.map((item: any) => ({
        ...item,
        uploadDate: new Date(item.uploadDate),
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt),
      }));
    } catch (error) {
      if ((error as any).code === "ENOENT") {
        // 文件不存在，返回空数组
        return [];
      }
      logger.error("Failed to load media metadata:", error);
      throw error;
    }
  }

  /**
   * 保存媒体元数据
   */
  private async saveMedia(media: MediaMetadata[]): Promise<void> {
    try {
      await fs.writeFile(this.mediaFile, JSON.stringify(media, null, 2));
    } catch (error) {
      logger.error("Failed to save media metadata:", error);
      throw error;
    }
  }

  /**
   * 加载用户配额信息
   */
  private async loadUserQuotas(): Promise<UserStorageQuota[]> {
    try {
      const data = await fs.readFile(this.quotaFile, "utf-8");
      const quotas = JSON.parse(data);
      console.log(quotas);
      return quotas.map((quota: any) => ({
        ...quota,
        lastUpdated: new Date(quota.lastUpdated),
      }));
    } catch (error) {
      if ((error as any).code === "ENOENT") {
        return [];
      }
      logger.error("Failed to load user quotas:", error);
      throw error;
    }
  }

  /**
   * 保存用户配额信息
   */
  private async saveUserQuotas(quotas: UserStorageQuota[]): Promise<void> {
    try {
      await fs.writeFile(this.quotaFile, JSON.stringify(quotas, null, 2));
    } catch (error) {
      logger.error("Failed to save user quotas:", error);
      throw error;
    }
  }

  /**
   * 保存媒体元数据
   */
  async saveMediaMetadata(
    metadata: Omit<MediaMetadata, "id" | "createdAt" | "updatedAt">
  ): Promise<MediaMetadata> {
    const media = await this.loadMedia();

    const newMedia: MediaMetadata = {
      ...metadata,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    media.push(newMedia);
    await this.saveMedia(media);

    logger.info(`Media metadata saved: ${newMedia.id}`);
    return newMedia;
  }

  /**
   * 根据ID获取媒体元数据
   */
  async getMediaById(id: string): Promise<MediaMetadata | null> {
    const media = await this.loadMedia();
    return media.find((item) => item.id === id) || null;
  }

  /**
   * 根据文件键获取媒体元数据
   */
  async getMediaByFileKey(fileKey: string): Promise<MediaMetadata | null> {
    const media = await this.loadMedia();
    return media.find((item) => item.fileKey === fileKey) || null;
  }

  /**
   * 获取用户的媒体列表
   */
  async getMediaByUser(
    userId: string,
    query: MediaListQuery = {}
  ): Promise<MediaListResponse> {
    const media = await this.loadMedia();

    // 过滤用户的媒体
    let userMedia = media.filter((item) => item.userId === userId);

    // 应用过滤条件
    if (query.fileType) {
      userMedia = userMedia.filter((item) =>
        item.fileType.startsWith(query.fileType!)
      );
    }

    if (query.status) {
      userMedia = userMedia.filter((item) => item.status === query.status);
    }

    if (query.search) {
      const searchLower = query.search.toLowerCase();
      userMedia = userMedia.filter((item) =>
        item.fileName.toLowerCase().includes(searchLower)
      );
    }

    // 排序
    const sortBy = query.sortBy || "uploadDate";
    const sortOrder = query.sortOrder || "desc";

    userMedia.sort((a, b) => {
      let aValue: any = a[sortBy as keyof MediaMetadata];
      let bValue: any = b[sortBy as keyof MediaMetadata];

      if (aValue instanceof Date) {
        aValue = aValue.getTime();
        bValue = (bValue as Date).getTime();
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // 分页
    const page = query.page || 1;
    const limit = query.limit || 20;
    const offset = (page - 1) * limit;
    const total = userMedia.length;
    const totalPages = Math.ceil(total / limit);

    const items = userMedia.slice(offset, offset + limit);

    return {
      items,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 更新媒体元数据
   */
  async updateMedia(
    id: string,
    updates: Partial<MediaMetadata>
  ): Promise<MediaMetadata | null> {
    const media = await this.loadMedia();
    const index = media.findIndex((item) => item.id === id);

    if (index === -1) {
      return null;
    }

    media[index] = {
      ...media[index],
      ...updates,
      updatedAt: new Date(),
    };

    await this.saveMedia(media);
    logger.info(`Media metadata updated: ${id}`);

    return media[index];
  }

  /**
   * 删除媒体元数据
   */
  async deleteMedia(id: string): Promise<boolean> {
    const media = await this.loadMedia();
    const index = media.findIndex((item) => item.id === id);

    if (index === -1) {
      return false;
    }

    media.splice(index, 1);
    await this.saveMedia(media);

    logger.info(`Media metadata deleted: ${id}`);
    return true;
  }

  /**
   * 获取用户存储配额
   */
  async getUserStorageQuota(userId: string): Promise<UserStorageQuota> {
    const quotas = await this.loadUserQuotas();
    let quota = quotas.find((q) => q.userId === userId);

    if (!quota) {
      // 创建默认配额
      quota = {
        userId,
        usedStorage: 0,
        storageLimit: 1024 * 1024 * 1024, // 1GB默认限制
        fileCountLimit: 1000,
        currentFileCount: 0,
        lastUpdated: new Date(),
      };

      quotas.push(quota);
      await this.saveUserQuotas(quotas);
    }

    return quota;
  }

  /**
   * 更新用户存储配额
   */
  async updateUserStorageQuota(
    userId: string,
    updates: Partial<UserStorageQuota>
  ): Promise<UserStorageQuota> {
    const quotas = await this.loadUserQuotas();
    const index = quotas.findIndex((q) => q.userId === userId);

    if (index === -1) {
      // 创建新配额
      const newQuota: UserStorageQuota = {
        userId,
        usedStorage: 0,
        storageLimit: 1024 * 1024 * 1024,
        fileCountLimit: 1000,
        currentFileCount: 0,
        lastUpdated: new Date(),
        ...updates,
      };

      quotas.push(newQuota);
      await this.saveUserQuotas(quotas);
      return newQuota;
    }

    quotas[index] = {
      ...quotas[index],
      ...updates,
      lastUpdated: new Date(),
    };

    await this.saveUserQuotas(quotas);
    return quotas[index];
  }

  /**
   * 计算用户实际使用的存储空间
   */
  async calculateUserStorage(
    userId: string
  ): Promise<{ usedStorage: number; fileCount: number }> {
    const media = await this.loadMedia();
    const userMedia = media.filter(
      (item) => item.userId === userId && item.status === "ready"
    );

    const usedStorage = userMedia.reduce(
      (total, item) => total + item.fileSize,
      0
    );
    const fileCount = userMedia.length;

    return { usedStorage, fileCount };
  }
}
