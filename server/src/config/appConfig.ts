/**
 * 应用配置管理
 * 统一管理所有环境变量和配额限制
 */

// 解析字节大小的辅助函数
const parseBytes = (value: string): number => {
  if (!value) return 0;

  // 如果是纯数字，直接返回
  if (/^\d+$/.test(value)) {
    return parseInt(value);
  }

  // 解析带单位的值
  const units: { [key: string]: number } = {
    B: 1,
    KB: 1024,
    MB: 1024 * 1024,
    GB: 1024 * 1024 * 1024,
  };

  const match = value.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)?$/i);
  if (match) {
    const num = parseFloat(match[1]);
    const unit = (match[2] || "B").toUpperCase();
    return Math.floor(num * (units[unit] || 1));
  }

  return parseInt(value) || 0;
};

// 解析时间间隔的辅助函数
const parseTimeInterval = (value: string): number => {
  if (!value) return 0;

  // 如果是纯数字，直接返回（毫秒）
  if (/^\d+$/.test(value)) {
    return parseInt(value);
  }

  // 解析带单位的值
  const units: { [key: string]: number } = {
    ms: 1,
    s: 1000,
    m: 60 * 1000,
    h: 60 * 60 * 1000,
    d: 24 * 60 * 60 * 1000,
  };

  const match = value.match(/^(\d+(?:\.\d+)?)\s*(ms|s|m|h|d)?$/i);
  if (match) {
    const num = parseFloat(match[1]);
    const unit = (match[2] || "ms").toLowerCase();
    return Math.floor(num * (units[unit] || 1));
  }

  return parseInt(value) || 0;
};

/**
 * 服务器基础配置
 */
export const serverConfig = {
  port: parseInt(process.env.PORT || "8080"),
  nodeEnv: process.env.NODE_ENV || "development",
  expressJsonLimit: process.env.EXPRESS_JSON_LIMIT || "100mb",
  requestSizeLimit: parseBytes(process.env.REQUEST_SIZE_LIMIT || "100MB"),
};

/**
 * 速率限制配置
 */
export const rateLimitConfig = {
  general: {
    windowMs: parseTimeInterval(process.env.GENERAL_RATE_LIMIT_WINDOW || "15m"),
    max: parseInt(process.env.GENERAL_RATE_LIMIT_MAX || "2000"),
  },
  progress: {
    windowMs: parseTimeInterval(process.env.PROGRESS_RATE_LIMIT_WINDOW || "1m"),
    max: parseInt(process.env.PROGRESS_RATE_LIMIT_MAX || "120"),
  },
  ai: {
    windowMs: parseTimeInterval(process.env.AI_RATE_LIMIT_WINDOW || "15m"),
    max: parseInt(process.env.AI_RATE_LIMIT_MAX || "50"),
  },
  upload: {
    windowMs: parseTimeInterval(process.env.UPLOAD_RATE_LIMIT_WINDOW || "15m"),
    max: parseInt(process.env.UPLOAD_RATE_LIMIT_MAX || "20"),
  },
};

/**
 * 用户配额配置
 */
export interface UserQuotaConfig {
  userType: "free" | "premium" | "enterprise" | "admin";
  storageLimit: number;
  fileCountLimit: number;
  maxFileSize: number;
  dailyUploadLimit?: number;
  monthlyUploadLimit?: number;
}

export const quotaConfig = {
  free: {
    userType: "free",
    storageLimit: parseBytes(process.env.FREE_STORAGE_LIMIT || "1GB"),
    fileCountLimit: parseInt(process.env.FREE_FILE_COUNT_LIMIT || "100"),
    maxFileSize: parseBytes(process.env.FREE_MAX_FILE_SIZE || "50MB"),
    dailyUploadLimit: parseBytes(
      process.env.FREE_DAILY_UPLOAD_LIMIT || "100MB"
    ),
    monthlyUploadLimit: parseBytes(
      process.env.FREE_MONTHLY_UPLOAD_LIMIT || "1GB"
    ),
  } as UserQuotaConfig,

  premium: {
    userType: "premium",
    storageLimit: parseBytes(process.env.PREMIUM_STORAGE_LIMIT || "10GB"),
    fileCountLimit: parseInt(process.env.PREMIUM_FILE_COUNT_LIMIT || "1000"),
    maxFileSize: parseBytes(process.env.PREMIUM_MAX_FILE_SIZE || "200MB"),
    dailyUploadLimit: parseBytes(
      process.env.PREMIUM_DAILY_UPLOAD_LIMIT || "500MB"
    ),
    monthlyUploadLimit: parseBytes(
      process.env.PREMIUM_MONTHLY_UPLOAD_LIMIT || "5GB"
    ),
  } as UserQuotaConfig,

  enterprise: {
    userType: "enterprise",
    storageLimit: parseBytes(process.env.ENTERPRISE_STORAGE_LIMIT || "100GB"),
    fileCountLimit: parseInt(
      process.env.ENTERPRISE_FILE_COUNT_LIMIT || "10000"
    ),
    maxFileSize: parseBytes(process.env.ENTERPRISE_MAX_FILE_SIZE || "1GB"),
    dailyUploadLimit: parseBytes(
      process.env.ENTERPRISE_DAILY_UPLOAD_LIMIT || "2GB"
    ),
    monthlyUploadLimit: parseBytes(
      process.env.ENTERPRISE_MONTHLY_UPLOAD_LIMIT || "20GB"
    ),
  } as UserQuotaConfig,

  admin: {
    userType: "admin",
    storageLimit: parseInt(
      process.env.ADMIN_STORAGE_LIMIT || String(Number.MAX_SAFE_INTEGER)
    ),
    fileCountLimit: parseInt(
      process.env.ADMIN_FILE_COUNT_LIMIT || String(Number.MAX_SAFE_INTEGER)
    ),
    maxFileSize: parseInt(
      process.env.ADMIN_MAX_FILE_SIZE || String(Number.MAX_SAFE_INTEGER)
    ),
  } as UserQuotaConfig,
};

/**
 * 文件清理配置
 */
export const cleanupConfig = {
  tempFile: {
    interval: parseTimeInterval(process.env.TEMP_FILE_CLEANUP_INTERVAL || "1h"),
    age: parseInt(process.env.TEMP_FILE_CLEANUP_AGE || "1"), // 小时
  },
  outputFile: {
    interval: parseTimeInterval(
      process.env.OUTPUT_FILE_CLEANUP_INTERVAL || "1d"
    ),
    age: parseInt(process.env.OUTPUT_FILE_CLEANUP_AGE || "7"), // 天
  },
  thumbnail: {
    interval: parseTimeInterval(process.env.THUMBNAIL_CLEANUP_INTERVAL || "1d"),
    age: parseInt(process.env.THUMBNAIL_CLEANUP_AGE || "24"), // 小时
  },
};

/**
 * 系统监控配置
 */
export const monitorConfig = {
  resourceMonitorInterval: parseTimeInterval(
    process.env.RESOURCE_MONITOR_INTERVAL || "5m"
  ),
  lowMemoryThreshold: parseInt(process.env.LOW_MEMORY_THRESHOLD || "10"), // 百分比
};

/**
 * 获取当前配置摘要（用于调试）
 */
export const getConfigSummary = () => {
  return {
    server: serverConfig,
    rateLimit: rateLimitConfig,
    quota: quotaConfig,
    cleanup: cleanupConfig,
    monitor: monitorConfig,
  };
};
