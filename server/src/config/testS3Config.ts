import { S3Client } from "@aws-sdk/client-s3";

/**
 * 测试用S3配置
 * 当没有真实AWS凭证时使用
 */
export const createTestS3Client = (): S3Client => {
  // 检查是否有真实的AWS凭证
  const hasRealCredentials = 
    process.env.S3_ACCESS_KEY_ID && 
    process.env.S3_ACCESS_KEY_ID !== 'your_aws_access_key_id' &&
    process.env.S3_SECRET_ACCESS_KEY && 
    process.env.S3_SECRET_ACCESS_KEY !== 'your_aws_secret_access_key';

  if (hasRealCredentials) {
    // 使用真实的AWS凭证
    return new S3Client({
      region: process.env.S3_REGION || 'ap-southeast-1',
      credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY_ID!,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!,
      },
    });
  } else {
    // 使用模拟的S3服务（用于开发测试）
    console.warn('⚠️  使用模拟S3服务 - 请配置真实的AWS凭证以使用实际的S3服务');
    
    return new S3Client({
      region: 'us-east-1',
      endpoint: 'http://localhost:9000', // MinIO或其他S3兼容服务
      credentials: {
        accessKeyId: 'minioadmin',
        secretAccessKey: 'minioadmin',
      },
      forcePathStyle: true, // 对于MinIO等S3兼容服务
    });
  }
};

/**
 * 检查是否使用真实的AWS服务
 */
export const isUsingRealAWS = (): boolean => {
  return !!(
    process.env.S3_ACCESS_KEY_ID && 
    process.env.S3_ACCESS_KEY_ID !== 'your_aws_access_key_id' &&
    process.env.S3_SECRET_ACCESS_KEY && 
    process.env.S3_SECRET_ACCESS_KEY !== 'your_aws_secret_access_key'
  );
};
