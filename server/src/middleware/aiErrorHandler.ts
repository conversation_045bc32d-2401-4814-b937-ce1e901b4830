import { Request, Response, NextFunction } from "express";
import logger from "../utils/logger";
import { rateLimitConfig } from "../config/appConfig";
import { AiErrorResponse, AiErrorCode } from "../types/ai";

/**
 * AI相关错误处理中间件
 */
export class AiError extends Error {
  public code: AiErrorCode;
  public statusCode: number;
  public details?: string;

  constructor(
    message: string,
    code: AiErrorCode,
    statusCode: number = 500,
    details?: string
  ) {
    super(message);
    this.name = "AiError";
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * 创建标准化的AI错误响应
 */
export function createAiErrorResponse(
  error: Error | AiError,
  code?: AiErrorCode
): AiErrorResponse {
  const timestamp = new Date().toISOString();

  if (error instanceof AiError) {
    return {
      error: error.message,
      code: error.code,
      details: error.details,
      timestamp,
    };
  }

  // 处理AWS相关错误
  if (error.message.includes("credentials")) {
    return {
      error: "AWS凭证配置错误",
      code: AiErrorCode.AWS_CREDENTIALS_ERROR,
      details:
        "请检查AWS_ACCESS_KEY_ID、AWS_SECRET_ACCESS_KEY和AWS_REGION环境变量",
      timestamp,
    };
  }

  if (error.message.includes("Bedrock") || error.message.includes("bedrock")) {
    return {
      error: "AI服务调用失败",
      code: AiErrorCode.BEDROCK_API_ERROR,
      details: error.message,
      timestamp,
    };
  }

  // 默认内部错误
  return {
    error: "内部服务器错误",
    code: code || AiErrorCode.INTERNAL_ERROR,
    details: error.message,
    timestamp,
  };
}

/**
 * AI错误处理中间件
 */
export function aiErrorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  logger.error("AI API错误:", {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
  });

  const errorResponse = createAiErrorResponse(error);
  const statusCode = error instanceof AiError ? error.statusCode : 500;

  res.status(statusCode).json(errorResponse);
}

/**
 * 验证AI请求参数
 */
export function validateAiRequest(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  try {
    const { userInput, canvasState } = req.body;

    // 验证用户输入
    if (
      !userInput ||
      typeof userInput !== "string" ||
      userInput.trim().length === 0
    ) {
      throw new AiError(
        "用户输入不能为空",
        AiErrorCode.INVALID_INPUT,
        400,
        "userInput字段必须是非空字符串"
      );
    }

    // 验证用户输入长度
    if (userInput.length > 2000) {
      throw new AiError(
        "用户输入过长",
        AiErrorCode.INVALID_INPUT,
        400,
        "用户输入不能超过2000个字符"
      );
    }

    // 验证画布状态
    if (!canvasState || typeof canvasState !== "object") {
      throw new AiError(
        "画布状态不能为空",
        AiErrorCode.MISSING_CANVAS_STATE,
        400,
        "canvasState字段必须是有效的对象"
      );
    }

    // 验证画布状态基本结构
    if (
      typeof canvasState.width !== "number" ||
      typeof canvasState.height !== "number"
    ) {
      throw new AiError(
        "画布尺寸无效",
        AiErrorCode.INVALID_INPUT,
        400,
        "画布的width和height必须是数字"
      );
    }

    if (!Array.isArray(canvasState.elements)) {
      throw new AiError(
        "画布元素格式无效",
        AiErrorCode.INVALID_INPUT,
        400,
        "elements字段必须是数组"
      );
    }

    next();
  } catch (error) {
    next(error);
  }
}

/**
 * 限制AI请求频率的中间件
 */
export function aiRateLimit(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // 这里可以实现更复杂的频率限制逻辑
  // 目前只是一个占位符，实际实现可以使用Redis或内存存储

  const clientIp = req.ip || req.connection.remoteAddress || "unknown";

  // 简单的内存存储（生产环境应该使用Redis）
  const requestKey = `ai_request_${clientIp}`;
  const now = Date.now();
  const windowMs = rateLimitConfig.ai.windowMs; // 使用配置的时间窗口
  const maxRequests = rateLimitConfig.ai.max; // 使用配置的最大请求数

  // 这里应该实现实际的频率限制逻辑
  // 目前直接通过
  logger.debug(
    `AI速率限制检查: IP=${clientIp}, 窗口=${windowMs}ms, 最大请求=${maxRequests}`
  );
  next();
}
