import { Request, Response } from "express";
import { TranscribeService } from "../services/TranscribeService";
import logger from "../utils/logger";

export class TranscribeController {
  private transcribeService: TranscribeService;

  constructor() {
    this.transcribeService = new TranscribeService();
  }

  /**
   * Generate SRT subtitles from an audio or video URL
   * @param req Request object containing media URL
   * @param res Response object
   */
  async generateSubtitles(req: Request, res: Response): Promise<void> {
    try {
      const { mediaUrl } = req.body;

      if (!mediaUrl) {
        res.status(400).json({ error: "Media URL is required" });
        return;
      }

      const taskId = Date.now().toString();
      logger.info(
        `Starting transcription task ${taskId} for media: ${mediaUrl}`
      );

      // Return task ID immediately to client
      res.json({ taskId });

      // Start transcription process in the background
      this.transcribeService
        .transcribeMedia(mediaUrl, taskId)
        .then(() => {
          logger.info(`Transcription task ${taskId} completed successfully`);
        })
        .catch((error) => {
          logger.error(`Transcription task ${taskId} failed:`, error);
        });
    } catch (error) {
      logger.error("Error in generateSubtitles:", error);
      res.status(500).json({
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * Get the status and results of a transcription task
   * @param req Request object containing taskId parameter
   * @param res Response object
   */
  async getSubtitlesStatus(req: Request, res: Response): Promise<void> {
    try {
      const taskId = req.params.taskId;

      if (!taskId) {
        res.status(400).json({ error: "Task ID is required" });
        return;
      }

      const status = await this.transcribeService.getTranscriptionStatus(
        taskId
      );

      if (!status) {
        res.status(404).json({ error: "Transcription task not found" });
        return;
      }

      res.json(status);
    } catch (error) {
      logger.error("Error in getSubtitlesStatus:", error);
      res.status(500).json({
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * Download the generated SRT file for a completed transcription task
   * @param req Request object containing taskId parameter
   * @param res Response object
   */
  async downloadSubtitles(req: Request, res: Response): Promise<void> {
    try {
      const taskId = req.params.taskId;

      if (!taskId) {
        res.status(400).json({ error: "Task ID is required" });
        return;
      }

      const srtPath = await this.transcribeService.getTranscriptionSrtPath(
        taskId
      );

      if (!srtPath) {
        res
          .status(404)
          .json({ error: "SRT file not found or transcription incomplete" });
        return;
      }

      res.download(srtPath, `subtitles_${taskId}.srt`);
    } catch (error) {
      logger.error("Error in downloadSubtitles:", error);
      res.status(500).json({
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
}
