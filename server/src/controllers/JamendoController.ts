import { Request, Response } from "express";
import axios from "axios";
import logger from "../utils/logger";

export class JamendoController {
  /**
   * 处理 CORS 预检请求
   */
  public handlePreflightRequest(req: Request, res: Response): void {
    res.set({
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
      "Access-Control-Allow-Headers":
        "Range, Content-Range, Content-Length, Content-Type",
      "Access-Control-Max-Age": "86400",
    });
    res.status(200).end();
  }

  /**
   * 代理 Jamendo 音频请求
   */
  public async proxyAudioRequest(req: Request, res: Response): Promise<void> {
    try {
      const { url } = req.query;
      logger.info("proxy-audio", url);

      if (!url || typeof url !== "string") {
        res.status(400).json({ error: "Missing or invalid URL parameter" });
        return;
      }

      // 验证URL是否来自Jamendo
      if (!url.includes("jamendo.com") && !url.includes("jamen.do")) {
        res.status(400).json({ error: "Only Jamendo URLs are allowed" });
        return;
      }

      // 准备请求头，包括Range头（如果有的话）
      const requestHeaders: any = {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        Accept: "audio/*,*/*;q=0.9",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        Referer: "https://www.jamendo.com/",
        Origin: "https://www.jamendo.com",
      };

      // 如果客户端发送了Range头，转发给Jamendo
      if (req.headers.range) {
        requestHeaders.Range = req.headers.range;
        logger.info("转发Range请求:", req.headers.range);
      }

      // 代理请求到Jamendo
      const response = await axios.get(url, {
        responseType: "stream",
        headers: requestHeaders,
        timeout: 30000,
      });

      // 检查响应的Content-Type和Content-Disposition
      const contentType = response.headers["content-type"] || "";
      const contentDisposition = response.headers["content-disposition"] || "";
      logger.info("Jamendo响应Content-Type:", contentType);
      logger.info("Jamendo响应Content-Disposition:", contentDisposition);
      logger.info("Jamendo响应状态:", response.status);

      // 确定正确的Content-Type
      const finalContentType = this.determineContentType(
        contentType,
        contentDisposition
      );

      logger.info("最终Content-Type:", finalContentType);

      // 设置基本的CORS头
      const responseHeaders: any = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
        "Access-Control-Allow-Headers":
          "Range, Content-Range, Content-Length, Content-Type",
        "Content-Type": finalContentType,
        "Accept-Ranges": "bytes",
        "Cache-Control": "public, max-age=3600",
      };

      // 处理Content-Length
      if (response.headers["content-length"]) {
        responseHeaders["Content-Length"] = response.headers["content-length"];
      }

      // 如果是Range请求，设置相应的状态码和头
      if (req.headers.range && response.status === 206) {
        logger.info("处理Range响应，状态码:", response.status);
        if (response.headers["content-range"]) {
          responseHeaders["Content-Range"] = response.headers["content-range"];
          logger.info("设置Content-Range:", response.headers["content-range"]);
        }
        res.status(206);
      } else {
        res.status(200);
      }

      res.set(responseHeaders);

      // 流式传输音频数据
      response.data.pipe(res);
    } catch (error) {
      logger.error("Jamendo proxy error:", error);
      res.status(500).json({
        error: "Failed to proxy audio request",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 根据Content-Type和Content-Disposition确定最终的Content-Type
   */
  private determineContentType(
    contentType: string,
    contentDisposition: string
  ): string {
    // 默认值
    let finalContentType = "audio/mpeg";

    if (contentType.includes("audio")) {
      finalContentType = contentType;
    } else if (contentDisposition.includes(".mp3")) {
      finalContentType = "audio/mpeg";
    } else if (contentDisposition.includes(".wav")) {
      finalContentType = "audio/wav";
    } else if (contentDisposition.includes(".ogg")) {
      finalContentType = "audio/ogg";
    } else if (contentDisposition.includes(".m4a")) {
      finalContentType = "audio/mp4";
    }

    return finalContentType;
  }
}
