import { Request, Response } from "express";
import { v4 as uuidv4 } from "uuid";
import fs from "fs/promises";
import path from "path";
import logger from "../utils/logger";

// 模板接口定义
interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  thumbnail: string;
  previewVideo?: string;
  canvasState: any;
  metadata: TemplateMetadata;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  authorId?: string;
  authorName?: string;
  usageCount: number;
  rating: number;
  ratingCount: number;
  platforms?: string[];
  formatDescription?: string;
}

interface TemplateMetadata {
  duration: number;
  elementCount: number;
  hasVideo: boolean;
  hasAudio: boolean;
  hasText: boolean;
  hasImages: boolean;
  hasAnimations: boolean;
  complexity: "simple" | "medium" | "complex";
  aspectRatio: string;
  resolution: string;
  platforms?: string[];
  formatDescription?: string;
}

interface TemplateFilter {
  category?: string;
  tags?: string[];
  hasVideo?: boolean;
  hasAudio?: boolean;
  hasText?: boolean;
  hasAnimations?: boolean;
  duration?: {
    min?: number;
    max?: number;
  };
  complexity?: string;
  aspectRatio?: string;
  sortBy?: "popular" | "newest" | "rating" | "name";
  sortOrder?: "asc" | "desc";
}

export class TemplateController {
  private templatesDir = path.join(process.cwd(), "data", "templates");
  private templatesFile = path.join(this.templatesDir, "templates.json");
  private thumbnailsDir = path.join(this.templatesDir, "thumbnails");

  constructor() {
    this.initializeDirectories();
  }

  private async initializeDirectories() {
    try {
      await fs.mkdir(this.templatesDir, { recursive: true });
      await fs.mkdir(this.thumbnailsDir, { recursive: true });

      // 如果模板文件不存在，创建空的模板数组
      try {
        await fs.access(this.templatesFile);
      } catch {
        await this.saveTemplates([]);
      }
    } catch (error) {
      logger.error("Failed to initialize template directories:", error);
    }
  }

  private async loadTemplates(): Promise<Template[]> {
    try {
      const data = await fs.readFile(this.templatesFile, "utf-8");
      return JSON.parse(data);
    } catch (error) {
      logger.error("Failed to load templates:", error);
      return [];
    }
  }

  private async saveTemplates(templates: Template[]): Promise<void> {
    try {
      await fs.writeFile(
        this.templatesFile,
        JSON.stringify(templates, null, 2)
      );
    } catch (error) {
      logger.error("Failed to save templates:", error);
      throw error;
    }
  }

  private generateTemplateMetadata(
    canvasState: any,
    platforms?: string[],
    formatDescription?: string
  ): TemplateMetadata {
    const elements = canvasState.elements || [];

    return {
      duration: canvasState.duration || 5000,
      elementCount: elements.length,
      hasVideo: elements.some((el: any) => el.type === "video"),
      hasAudio: elements.some((el: any) => el.type === "audio"),
      hasText: elements.some((el: any) => el.type === "text"),
      hasImages: elements.some((el: any) => el.type === "image"),
      hasAnimations: (canvasState.animations || []).length > 0,
      complexity: this.calculateComplexity(
        elements,
        canvasState.animations || []
      ),
      aspectRatio: `${canvasState.width}:${canvasState.height}`,
      resolution: `${canvasState.width}x${canvasState.height}`,
      platforms: platforms || [],
      formatDescription: formatDescription || "",
    };
  }

  private calculateComplexity(
    elements: any[],
    animations: any[]
  ): "simple" | "medium" | "complex" {
    const elementCount = elements.length;
    const animationCount = animations.length;
    const hasVideo = elements.some((el) => el.type === "video");

    if (elementCount <= 3 && animationCount <= 2 && !hasVideo) {
      return "simple";
    } else if (elementCount <= 8 && animationCount <= 5) {
      return "medium";
    } else {
      return "complex";
    }
  }

  private applyFilters(
    templates: Template[],
    filter: TemplateFilter
  ): Template[] {
    return templates.filter((template) => {
      // 分类过滤
      if (filter.category && template.category !== filter.category) {
        return false;
      }

      // 标签过滤
      if (filter.tags && filter.tags.length > 0) {
        const hasMatchingTag = filter.tags.some((tag) =>
          template.tags.some((templateTag) =>
            templateTag.toLowerCase().includes(tag.toLowerCase())
          )
        );
        if (!hasMatchingTag) return false;
      }

      // 媒体类型过滤
      if (
        filter.hasVideo !== undefined &&
        template.metadata.hasVideo !== filter.hasVideo
      ) {
        return false;
      }
      if (
        filter.hasAudio !== undefined &&
        template.metadata.hasAudio !== filter.hasAudio
      ) {
        return false;
      }
      if (
        filter.hasText !== undefined &&
        template.metadata.hasText !== filter.hasText
      ) {
        return false;
      }
      if (
        filter.hasAnimations !== undefined &&
        template.metadata.hasAnimations !== filter.hasAnimations
      ) {
        return false;
      }

      // 时长过滤
      if (filter.duration) {
        if (
          filter.duration.min &&
          template.metadata.duration < filter.duration.min
        ) {
          return false;
        }
        if (
          filter.duration.max &&
          template.metadata.duration > filter.duration.max
        ) {
          return false;
        }
      }

      // 复杂度过滤
      if (
        filter.complexity &&
        template.metadata.complexity !== filter.complexity
      ) {
        return false;
      }

      // 宽高比过滤
      if (
        filter.aspectRatio &&
        template.metadata.aspectRatio !== filter.aspectRatio
      ) {
        return false;
      }

      return true;
    });
  }

  private sortTemplates(
    templates: Template[],
    sortBy?: string,
    sortOrder?: string
  ): Template[] {
    const order = sortOrder === "desc" ? -1 : 1;

    return templates.sort((a, b) => {
      switch (sortBy) {
        case "popular":
          return (b.usageCount - a.usageCount) * order;
        case "rating":
          return (b.rating - a.rating) * order;
        case "name":
          return a.name.localeCompare(b.name) * order;
        case "newest":
        default:
          return (
            (new Date(b.createdAt).getTime() -
              new Date(a.createdAt).getTime()) *
            order
          );
      }
    });
  }

  // 获取模板列表
  async getTemplates(req: Request, res: Response) {
    try {
      const {
        page = 1,
        limit = 20,
        category,
        tags,
        hasVideo,
        hasAudio,
        hasText,
        hasAnimations,
        durationMin,
        durationMax,
        complexity,
        aspectRatio,
        sortBy = "newest",
        sortOrder = "desc",
      } = req.query;

      const templates = await this.loadTemplates();

      // 构建过滤条件
      const filter: TemplateFilter = {
        category: category as string,
        tags: tags
          ? (tags as string).split(",").map((tag) => tag.trim())
          : undefined,
        hasVideo: hasVideo ? hasVideo === "true" : undefined,
        hasAudio: hasAudio ? hasAudio === "true" : undefined,
        hasText: hasText ? hasText === "true" : undefined,
        hasAnimations: hasAnimations ? hasAnimations === "true" : undefined,
        complexity: complexity as string,
        aspectRatio: aspectRatio as string,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any,
      };

      if (durationMin || durationMax) {
        filter.duration = {
          min: durationMin ? parseInt(durationMin as string) : undefined,
          max: durationMax ? parseInt(durationMax as string) : undefined,
        };
      }

      // 应用过滤和排序
      let filteredTemplates = this.applyFilters(templates, filter);
      filteredTemplates = this.sortTemplates(
        filteredTemplates,
        sortBy as string,
        sortOrder as string
      );

      // 分页
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = startIndex + limitNum;
      const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);

      res.json({
        templates: paginatedTemplates,
        total: filteredTemplates.length,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(filteredTemplates.length / limitNum),
      });
    } catch (error) {
      logger.error("Failed to get templates:", error);
      res.status(500).json({ error: "Failed to get templates" });
    }
  }

  // 根据ID获取模板
  async getTemplateById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const templates = await this.loadTemplates();
      const template = templates.find((t) => t.id === id);

      if (!template) {
        return res.status(404).json({ error: "Template not found" });
      }

      res.json(template);
    } catch (error) {
      logger.error("Failed to get template:", error);
      res.status(500).json({ error: "Failed to get template" });
    }
  }

  // 创建模板
  async createTemplate(req: Request, res: Response) {
    try {
      const {
        name,
        description,
        category,
        tags = [],
        isPublic = true,
        canvasState,
        authorId,
        authorName,
        platforms = [],
        formatDescription,
      } = req.body;

      if (!name || !canvasState) {
        return res
          .status(400)
          .json({ error: "Name and canvas state are required" });
      }

      const templates = await this.loadTemplates();
      const metadata = this.generateTemplateMetadata(
        canvasState,
        platforms,
        formatDescription
      );

      const newTemplate: Template = {
        id: uuidv4(),
        name,
        description: description || "",
        category: category || "other",
        tags: Array.isArray(tags) ? tags : [],
        thumbnail: "", // 将在后续上传
        canvasState,
        metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isPublic,
        authorId,
        authorName,
        usageCount: 0,
        rating: 0,
        ratingCount: 0,
        platforms: Array.isArray(platforms) ? platforms : [],
        formatDescription: formatDescription || "",
      };

      templates.push(newTemplate);
      await this.saveTemplates(templates);

      logger.info(`Template created: ${newTemplate.id}`);
      res.status(201).json(newTemplate);
    } catch (error) {
      logger.error("Failed to create template:", error);
      res.status(500).json({ error: "Failed to create template" });
    }
  }

  // 更新模板
  async updateTemplate(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updates = req.body;

      const templates = await this.loadTemplates();
      const templateIndex = templates.findIndex((t) => t.id === id);

      if (templateIndex === -1) {
        return res.status(404).json({ error: "Template not found" });
      }

      const template = templates[templateIndex];

      // 更新模板
      Object.assign(template, updates, {
        updatedAt: new Date().toISOString(),
      });

      // 如果画布状态更新了，重新生成元数据
      if (updates.canvasState) {
        template.metadata = this.generateTemplateMetadata(updates.canvasState);
      }

      templates[templateIndex] = template;
      await this.saveTemplates(templates);

      logger.info(`Template updated: ${id}`);
      res.json(template);
    } catch (error) {
      logger.error("Failed to update template:", error);
      res.status(500).json({ error: "Failed to update template" });
    }
  }

  // 删除模板
  async deleteTemplate(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const templates = await this.loadTemplates();
      const templateIndex = templates.findIndex((t) => t.id === id);

      if (templateIndex === -1) {
        return res.status(404).json({ error: "Template not found" });
      }

      const template = templates[templateIndex];

      // 删除缩略图文件
      if (template.thumbnail) {
        try {
          const thumbnailPath = path.join(
            this.thumbnailsDir,
            path.basename(template.thumbnail)
          );
          await fs.unlink(thumbnailPath);
        } catch (error) {
          logger.warn("Failed to delete thumbnail file:", error);
        }
      }

      templates.splice(templateIndex, 1);
      await this.saveTemplates(templates);

      logger.info(`Template deleted: ${id}`);
      res.json({ message: "Template deleted successfully" });
    } catch (error) {
      logger.error("Failed to delete template:", error);
      res.status(500).json({ error: "Failed to delete template" });
    }
  }

  // 搜索模板
  async searchTemplates(req: Request, res: Response) {
    try {
      const {
        q: query,
        page = 1,
        limit = 20,
        category,
        tags,
        sortBy = "newest",
        sortOrder = "desc",
      } = req.query;

      if (!query) {
        return res.status(400).json({ error: "Search query is required" });
      }

      const templates = await this.loadTemplates();
      const searchQuery = (query as string).toLowerCase();

      // 搜索匹配
      let searchResults = templates.filter((template) => {
        return (
          template.name.toLowerCase().includes(searchQuery) ||
          template.description.toLowerCase().includes(searchQuery) ||
          template.tags.some((tag) =>
            tag.toLowerCase().includes(searchQuery)
          ) ||
          template.category.toLowerCase().includes(searchQuery)
        );
      });

      // 应用额外过滤
      const filter: TemplateFilter = {
        category: category as string,
        tags: tags
          ? (tags as string).split(",").map((tag) => tag.trim())
          : undefined,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any,
      };

      searchResults = this.applyFilters(searchResults, filter);
      searchResults = this.sortTemplates(
        searchResults,
        sortBy as string,
        sortOrder as string
      );

      // 分页
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = startIndex + limitNum;
      const paginatedResults = searchResults.slice(startIndex, endIndex);

      res.json({
        templates: paginatedResults,
        total: searchResults.length,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(searchResults.length / limitNum),
      });
    } catch (error) {
      logger.error("Failed to search templates:", error);
      res.status(500).json({ error: "Failed to search templates" });
    }
  }

  // 增加模板使用次数
  async incrementUsage(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const templates = await this.loadTemplates();
      const template = templates.find((t) => t.id === id);

      if (!template) {
        return res.status(404).json({ error: "Template not found" });
      }

      template.usageCount += 1;
      template.updatedAt = new Date().toISOString();

      await this.saveTemplates(templates);
      res.json({ message: "Usage count updated" });
    } catch (error) {
      logger.error("Failed to increment usage:", error);
      res.status(500).json({ error: "Failed to increment usage" });
    }
  }

  // 为模板评分
  async rateTemplate(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { rating } = req.body;

      if (!rating || rating < 1 || rating > 5) {
        return res
          .status(400)
          .json({ error: "Rating must be between 1 and 5" });
      }

      const templates = await this.loadTemplates();
      const template = templates.find((t) => t.id === id);

      if (!template) {
        return res.status(404).json({ error: "Template not found" });
      }

      // 计算新的平均评分
      const newRatingCount = template.ratingCount + 1;
      const newRating =
        (template.rating * template.ratingCount + rating) / newRatingCount;

      template.rating = newRating;
      template.ratingCount = newRatingCount;
      template.updatedAt = new Date().toISOString();

      await this.saveTemplates(templates);
      res.json({
        message: "Rating updated",
        rating: newRating,
        ratingCount: newRatingCount,
      });
    } catch (error) {
      logger.error("Failed to rate template:", error);
      res.status(500).json({ error: "Failed to rate template" });
    }
  }

  // 获取模板统计信息
  async getTemplateStats(req: Request, res: Response) {
    try {
      const templates = await this.loadTemplates();

      const categoryCounts: Record<string, number> = {};
      const tagCounts: Record<string, number> = {};
      let totalRating = 0;
      let totalRatingCount = 0;

      templates.forEach((template) => {
        // 统计分类
        categoryCounts[template.category] =
          (categoryCounts[template.category] || 0) + 1;

        // 统计标签
        template.tags.forEach((tag) => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });

        // 统计评分
        if (template.ratingCount > 0) {
          totalRating += template.rating * template.ratingCount;
          totalRatingCount += template.ratingCount;
        }
      });

      // 获取热门标签（前10个）
      const popularTags = Object.entries(tagCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([tag, count]) => ({ tag, count }));

      res.json({
        totalTemplates: templates.length,
        categoryCounts,
        popularTags,
        averageRating:
          totalRatingCount > 0 ? totalRating / totalRatingCount : 0,
      });
    } catch (error) {
      logger.error("Failed to get template stats:", error);
      res.status(500).json({ error: "Failed to get template stats" });
    }
  }
}
