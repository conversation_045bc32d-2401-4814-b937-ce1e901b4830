import { Request, Response } from "express";
import { S3Service } from "../services/S3Service";
import { MediaRepository } from "../repositories/MediaRepository";
import logger from "../utils/logger";
import {
  SignedUrlRequest,
  MultipartUploadRequest,
  PartUploadUrlRequest,
  CompleteMultipartUploadRequest,
  UploadCompleteRequest,
  MediaListQuery,
} from "../types/media";

/**
 * S3控制器
 * 处理所有S3相关的API端点
 */
export class S3Controller {
  private s3Service: S3Service;
  private mediaRepository: MediaRepository;

  constructor() {
    this.s3Service = new S3Service();
    this.mediaRepository = new MediaRepository();
  }

  /**
   * 生成签名URL用于单文件上传
   * POST /api/s3/signed-url
   */
  async generateSignedUrl(req: Request, res: Response): Promise<void> {
    try {
      const { fileName, fileType, fileSize }: SignedUrlRequest = req.body;
      
      // TODO: 从认证中间件获取用户ID
      const userId = req.headers["x-user-id"] as string || "anonymous";

      if (!fileName || !fileType || !fileSize) {
        res.status(400).json({
          error: "Missing required fields: fileName, fileType, fileSize",
        });
        return;
      }

      const result = await this.s3Service.generateSignedUrl(
        { fileName, fileType, fileSize },
        userId
      );

      res.json(result);
    } catch (error) {
      logger.error("Error generating signed URL:", error);
      res.status(500).json({
        error: "Failed to generate signed URL",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 初始化多部分上传
   * POST /api/s3/multipart/initiate
   */
  async initiateMultipartUpload(req: Request, res: Response): Promise<void> {
    try {
      const { fileName, fileType, fileSize, partSize }: MultipartUploadRequest = req.body;
      
      const userId = req.headers["x-user-id"] as string || "anonymous";

      if (!fileName || !fileType || !fileSize) {
        res.status(400).json({
          error: "Missing required fields: fileName, fileType, fileSize",
        });
        return;
      }

      const result = await this.s3Service.initiateMultipartUpload(
        { fileName, fileType, fileSize, partSize },
        userId
      );

      res.json(result);
    } catch (error) {
      logger.error("Error initiating multipart upload:", error);
      res.status(500).json({
        error: "Failed to initiate multipart upload",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 生成分片上传URL
   * POST /api/s3/multipart/part-url
   */
  async generatePartUploadUrl(req: Request, res: Response): Promise<void> {
    try {
      const { uploadId, fileKey, partNumber }: PartUploadUrlRequest = req.body;

      if (!uploadId || !fileKey || !partNumber) {
        res.status(400).json({
          error: "Missing required fields: uploadId, fileKey, partNumber",
        });
        return;
      }

      const result = await this.s3Service.generatePartUploadUrl({
        uploadId,
        fileKey,
        partNumber,
      });

      res.json(result);
    } catch (error) {
      logger.error("Error generating part upload URL:", error);
      res.status(500).json({
        error: "Failed to generate part upload URL",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 完成多部分上传
   * POST /api/s3/multipart/complete
   */
  async completeMultipartUpload(req: Request, res: Response): Promise<void> {
    try {
      const { uploadId, fileKey, parts, metadata }: CompleteMultipartUploadRequest = req.body;
      
      const userId = req.headers["x-user-id"] as string || "anonymous";

      if (!uploadId || !fileKey || !parts || !metadata) {
        res.status(400).json({
          error: "Missing required fields: uploadId, fileKey, parts, metadata",
        });
        return;
      }

      const result = await this.s3Service.completeMultipartUpload(
        { uploadId, fileKey, parts, metadata },
        userId
      );

      res.json(result);
    } catch (error) {
      logger.error("Error completing multipart upload:", error);
      res.status(500).json({
        error: "Failed to complete multipart upload",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 取消多部分上传
   * POST /api/s3/multipart/abort
   */
  async abortMultipartUpload(req: Request, res: Response): Promise<void> {
    try {
      const { uploadId, fileKey } = req.body;

      if (!uploadId || !fileKey) {
        res.status(400).json({
          error: "Missing required fields: uploadId, fileKey",
        });
        return;
      }

      await this.s3Service.abortMultipartUpload(uploadId, fileKey);

      res.json({ success: true });
    } catch (error) {
      logger.error("Error aborting multipart upload:", error);
      res.status(500).json({
        error: "Failed to abort multipart upload",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 处理上传完成通知
   * POST /api/s3/upload-complete
   */
  async handleUploadComplete(req: Request, res: Response): Promise<void> {
    try {
      const { fileKey, fileName, fileType, fileSize, metadata }: UploadCompleteRequest = req.body;
      
      const userId = req.headers["x-user-id"] as string || "anonymous";

      if (!fileKey || !fileName || !fileType || !fileSize) {
        res.status(400).json({
          error: "Missing required fields: fileKey, fileName, fileType, fileSize",
        });
        return;
      }

      const result = await this.s3Service.handleUploadComplete(
        { fileKey, fileName, fileType, fileSize, metadata },
        userId
      );

      res.json(result);
    } catch (error) {
      logger.error("Error handling upload complete:", error);
      res.status(500).json({
        error: "Failed to handle upload complete",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 获取用户的媒体列表
   * GET /api/s3/media
   */
  async listMedia(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.headers["x-user-id"] as string || "anonymous";
      
      const query: MediaListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        fileType: req.query.fileType as string,
        search: req.query.search as string,
        sortBy: req.query.sortBy as any,
        sortOrder: req.query.sortOrder as any,
        status: req.query.status as any,
      };

      const result = await this.mediaRepository.getMediaByUser(userId, query);

      res.json(result);
    } catch (error) {
      logger.error("Error listing media:", error);
      res.status(500).json({
        error: "Failed to list media",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 获取媒体详情
   * GET /api/s3/media/:id
   */
  async getMediaDetails(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.headers["x-user-id"] as string || "anonymous";

      const media = await this.mediaRepository.getMediaById(id);

      if (!media) {
        res.status(404).json({ error: "Media not found" });
        return;
      }

      // 验证用户权限
      if (media.userId !== userId) {
        res.status(403).json({ error: "Access denied" });
        return;
      }

      res.json(media);
    } catch (error) {
      logger.error("Error getting media details:", error);
      res.status(500).json({
        error: "Failed to get media details",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 删除媒体文件
   * DELETE /api/s3/media/:id
   */
  async deleteMedia(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.headers["x-user-id"] as string || "anonymous";

      const media = await this.mediaRepository.getMediaById(id);

      if (!media) {
        res.status(404).json({ error: "Media not found" });
        return;
      }

      // 验证用户权限
      if (media.userId !== userId) {
        res.status(403).json({ error: "Access denied" });
        return;
      }

      const success = await this.s3Service.deleteFile(media.fileKey, userId);

      if (success) {
        res.json({ success: true });
      } else {
        res.status(500).json({ error: "Failed to delete media" });
      }
    } catch (error) {
      logger.error("Error deleting media:", error);
      res.status(500).json({
        error: "Failed to delete media",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 获取用户存储配额信息
   * GET /api/s3/quota
   */
  async getUserQuota(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.headers["x-user-id"] as string || "anonymous";

      const quota = await this.mediaRepository.getUserStorageQuota(userId);
      const currentUsage = await this.mediaRepository.calculateUserStorage(userId);

      res.json({
        ...quota,
        usedStorage: currentUsage.usedStorage,
        currentFileCount: currentUsage.fileCount,
        availableStorage: quota.storageLimit - currentUsage.usedStorage,
        availableFileCount: quota.fileCountLimit - currentUsage.fileCount,
      });
    } catch (error) {
      logger.error("Error getting user quota:", error);
      res.status(500).json({
        error: "Failed to get user quota",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
}
