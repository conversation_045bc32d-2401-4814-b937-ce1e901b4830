import { Request, Response } from "express";
import { ThumbnailService } from "../services/ThumbnailService";
import logger from "../utils/logger";

/**
 * 缩略图控制器
 * 处理缩略图生成相关的API端点
 */
export class ThumbnailController {
  private thumbnailService: ThumbnailService;

  constructor() {
    this.thumbnailService = new ThumbnailService();
  }

  /**
   * 创建缩略图生成任务
   * POST /api/thumbnails/generate
   */
  async generateThumbnail(req: Request, res: Response): Promise<void> {
    try {
      const { mediaId, fileKey, fileType, sizes } = req.body;

      if (!mediaId || !fileKey || !fileType) {
        res.status(400).json({
          error: "Missing required fields: mediaId, fileKey, fileType",
        });
        return;
      }

      const taskId = await this.thumbnailService.createThumbnailTask(
        mediaId,
        fileKey,
        fileType,
        sizes || ["medium"]
      );

      res.json({ taskId });
    } catch (error) {
      logger.error("Error creating thumbnail task:", error);
      res.status(500).json({
        error: "Failed to create thumbnail task",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 获取缩略图生成任务状态
   * GET /api/thumbnails/status/:taskId
   */
  async getThumbnailStatus(req: Request, res: Response): Promise<void> {
    try {
      const { taskId } = req.params;

      if (!taskId) {
        res.status(400).json({ error: "Task ID is required" });
        return;
      }

      const task = this.thumbnailService.getTaskStatus(taskId);

      if (!task) {
        res.status(404).json({ error: "Thumbnail task not found" });
        return;
      }

      res.json(task);
    } catch (error) {
      logger.error("Error getting thumbnail status:", error);
      res.status(500).json({
        error: "Failed to get thumbnail status",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * 清理过期的缩略图任务
   * POST /api/thumbnails/cleanup
   */
  async cleanupTasks(req: Request, res: Response): Promise<void> {
    try {
      const { maxAgeHours = 24 } = req.body;

      this.thumbnailService.cleanupExpiredTasks(maxAgeHours);

      res.json({ success: true, message: "Cleanup completed" });
    } catch (error) {
      logger.error("Error cleaning up thumbnail tasks:", error);
      res.status(500).json({
        error: "Failed to cleanup thumbnail tasks",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
}
