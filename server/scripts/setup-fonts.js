#!/usr/bin/env node

/**
 * 字体设置脚本
 * 用于检查和设置支持中文的字体文件
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const https = require('https');

// 字体目录配置
const FONT_DIR = process.env.FONT_DIR || path.join(__dirname, '../assets/fonts');
const SYSTEM_FONTS = {
  darwin: [
    '/System/Library/Fonts/PingFang.ttc',
    '/System/Library/Fonts/STHeiti Light.ttc',
    '/System/Library/Fonts/STHeiti Medium.ttc',
    '/Library/Fonts/Arial Unicode MS.ttf'
  ],
  win32: [
    'C:\\Windows\\Fonts\\simhei.ttf',
    'C:\\Windows\\Fonts\\simsun.ttc',
    'C:\\Windows\\Fonts\\msyh.ttc',
    'C:\\Windows\\Fonts\\msyhbd.ttc'
  ],
  linux: [
    '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
    '/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc',
    '/usr/share/fonts/truetype/arphic/ukai.ttc',
    '/usr/share/fonts/truetype/arphic/uming.ttc'
  ]
};

// 推荐的开源中文字体下载链接
const RECOMMENDED_FONTS = {
  'NotoSansCJK-Regular.ttc': 'https://github.com/googlefonts/noto-cjk/releases/download/Sans2.004/NotoSansCJK.ttc.zip',
  'SourceHanSansCN-Regular.otf': 'https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansCN.zip'
};

/**
 * 检查字体文件是否存在
 */
function checkFontExists(fontPath) {
  try {
    return fs.existsSync(fontPath);
  } catch (error) {
    return false;
  }
}

/**
 * 创建字体目录
 */
function ensureFontDirectory() {
  if (!fs.existsSync(FONT_DIR)) {
    try {
      fs.mkdirSync(FONT_DIR, { recursive: true });
      console.log(`✅ 创建字体目录: ${FONT_DIR}`);
      return true;
    } catch (error) {
      console.error(`❌ 创建字体目录失败: ${error.message}`);
      return false;
    }
  }
  return true;
}

/**
 * 检查系统字体
 */
function checkSystemFonts() {
  const platform = os.platform();
  const systemFonts = SYSTEM_FONTS[platform] || [];
  
  console.log(`\n🔍 检查 ${platform} 系统字体...`);
  
  const availableFonts = [];
  for (const fontPath of systemFonts) {
    if (checkFontExists(fontPath)) {
      console.log(`✅ 找到系统字体: ${fontPath}`);
      availableFonts.push(fontPath);
    } else {
      console.log(`❌ 系统字体不存在: ${fontPath}`);
    }
  }
  
  return availableFonts;
}

/**
 * 检查自定义字体目录
 */
function checkCustomFonts() {
  console.log(`\n🔍 检查自定义字体目录: ${FONT_DIR}`);
  
  if (!fs.existsSync(FONT_DIR)) {
    console.log(`❌ 字体目录不存在: ${FONT_DIR}`);
    return [];
  }
  
  const availableFonts = [];
  try {
    const files = fs.readdirSync(FONT_DIR);
    const fontFiles = files.filter(file => 
      file.endsWith('.ttf') || 
      file.endsWith('.ttc') || 
      file.endsWith('.otf')
    );
    
    for (const fontFile of fontFiles) {
      const fontPath = path.join(FONT_DIR, fontFile);
      console.log(`✅ 找到自定义字体: ${fontPath}`);
      availableFonts.push(fontPath);
    }
    
    if (fontFiles.length === 0) {
      console.log(`⚠️  字体目录为空: ${FONT_DIR}`);
    }
  } catch (error) {
    console.error(`❌ 读取字体目录失败: ${error.message}`);
  }
  
  return availableFonts;
}

/**
 * 生成字体配置报告
 */
function generateFontReport(systemFonts, customFonts) {
  console.log('\n📊 字体配置报告');
  console.log('='.repeat(50));
  
  console.log(`系统平台: ${os.platform()}`);
  console.log(`字体目录: ${FONT_DIR}`);
  console.log(`系统字体数量: ${systemFonts.length}`);
  console.log(`自定义字体数量: ${customFonts.length}`);
  
  const totalFonts = systemFonts.length + customFonts.length;
  
  if (totalFonts === 0) {
    console.log('\n❌ 警告: 未找到任何支持中文的字体！');
    console.log('这可能导致中文文字在视频中显示为乱码。');
    return false;
  } else {
    console.log(`\n✅ 总共找到 ${totalFonts} 个可用字体`);
    return true;
  }
}

/**
 * 提供字体安装建议
 */
function provideFontRecommendations() {
  const platform = os.platform();
  
  console.log('\n💡 字体安装建议');
  console.log('='.repeat(50));
  
  switch (platform) {
    case 'darwin': // macOS
      console.log('macOS 用户建议:');
      console.log('1. 系统已预装 PingFang 字体，通常无需额外安装');
      console.log('2. 如需更多字体，可从 App Store 安装字体应用');
      console.log('3. 或手动下载字体文件到 /Library/Fonts/ 目录');
      break;
      
    case 'win32': // Windows
      console.log('Windows 用户建议:');
      console.log('1. 确保已安装中文语言包');
      console.log('2. 检查 C:\\Windows\\Fonts\\ 目录中的中文字体');
      console.log('3. 可下载微软雅黑、黑体等字体');
      break;
      
    default: // Linux
      console.log('Linux 用户建议:');
      console.log('1. 安装 WenQuanYi 字体包:');
      console.log('   sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei');
      console.log('2. 或安装 Noto CJK 字体:');
      console.log('   sudo apt-get install fonts-noto-cjk');
      console.log('3. 重新生成字体缓存:');
      console.log('   sudo fc-cache -fv');
      break;
  }
  
  console.log('\n📥 开源字体推荐:');
  console.log('- Noto Sans CJK (Google)');
  console.log('- Source Han Sans (Adobe)');
  console.log('- 文泉驿微米黑 (WenQuanYi Micro Hei)');
}

/**
 * 创建环境变量配置文件
 */
function createEnvConfig(hasValidFonts) {
  const envPath = path.join(__dirname, '../.env.fonts');
  
  let envContent = `# 字体配置文件\n`;
  envContent += `# 由 setup-fonts.js 自动生成\n\n`;
  envContent += `FONT_DIR=${FONT_DIR}\n`;
  envContent += `FONTS_AVAILABLE=${hasValidFonts}\n`;
  envContent += `FONT_CHECK_DATE=${new Date().toISOString()}\n`;
  
  try {
    fs.writeFileSync(envPath, envContent);
    console.log(`\n✅ 已创建字体配置文件: ${envPath}`);
  } catch (error) {
    console.error(`❌ 创建配置文件失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🎨 在线视频编辑器 - 字体设置工具');
  console.log('='.repeat(50));
  
  // 1. 确保字体目录存在
  if (!ensureFontDirectory()) {
    process.exit(1);
  }
  
  // 2. 检查系统字体
  const systemFonts = checkSystemFonts();
  
  // 3. 检查自定义字体
  const customFonts = checkCustomFonts();
  
  // 4. 生成报告
  const hasValidFonts = generateFontReport(systemFonts, customFonts);
  
  // 5. 提供建议
  if (!hasValidFonts) {
    provideFontRecommendations();
  }
  
  // 6. 创建配置文件
  createEnvConfig(hasValidFonts);
  
  console.log('\n🎯 设置完成！');
  
  if (!hasValidFonts) {
    console.log('⚠️  请按照上述建议安装中文字体，然后重新运行此脚本。');
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  checkSystemFonts,
  checkCustomFonts,
  generateFontReport,
  ensureFontDirectory
};
