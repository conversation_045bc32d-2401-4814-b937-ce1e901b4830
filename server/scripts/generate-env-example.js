#!/usr/bin/env node

/**
 * 生成 .env.example 文件的脚本
 * 运行方式: node scripts/generate-env-example.js
 */

const fs = require("fs");
const path = require("path");

const envConfig = `# 服务器基础配置
PORT=8080
NODE_ENV=development

# Express JSON 请求体大小限制
EXPRESS_JSON_LIMIT=100mb

# 请求大小限制（字节数，支持单位：B, KB, MB, GB）
REQUEST_SIZE_LIMIT=100MB

# AWS 配置
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# S3 配置
S3_BUCKET_NAME=your_s3_bucket_name
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your_s3_access_key_id
S3_SECRET_ACCESS_KEY=your_s3_secret_access_key
S3_SIGNED_URL_EXPIRES=900
S3_MAX_FILE_SIZE=104857600
S3_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4,video/avi,video/mov,audio/mpeg,audio/wav

# 速率限制配置
# 通用API速率限制时间窗口（支持单位：ms, s, m, h, d）
GENERAL_RATE_LIMIT_WINDOW=15m
GENERAL_RATE_LIMIT_MAX=2000

# 进度查询API速率限制
PROGRESS_RATE_LIMIT_WINDOW=1m
PROGRESS_RATE_LIMIT_MAX=120

# AI API速率限制
AI_RATE_LIMIT_WINDOW=15m
AI_RATE_LIMIT_MAX=50

# 文件上传速率限制
UPLOAD_RATE_LIMIT_WINDOW=15m
UPLOAD_RATE_LIMIT_MAX=20

# 免费用户配额
# 存储空间限制（支持单位：B, KB, MB, GB）
FREE_STORAGE_LIMIT=1GB
FREE_FILE_COUNT_LIMIT=100
FREE_MAX_FILE_SIZE=50MB
FREE_DAILY_UPLOAD_LIMIT=100MB
FREE_MONTHLY_UPLOAD_LIMIT=1GB

# 高级用户配额
PREMIUM_STORAGE_LIMIT=10GB
PREMIUM_FILE_COUNT_LIMIT=1000
PREMIUM_MAX_FILE_SIZE=200MB
PREMIUM_DAILY_UPLOAD_LIMIT=500MB
PREMIUM_MONTHLY_UPLOAD_LIMIT=5GB

# 企业用户配额
ENTERPRISE_STORAGE_LIMIT=100GB
ENTERPRISE_FILE_COUNT_LIMIT=10000
ENTERPRISE_MAX_FILE_SIZE=1GB
ENTERPRISE_DAILY_UPLOAD_LIMIT=2GB
ENTERPRISE_MONTHLY_UPLOAD_LIMIT=20GB

# 管理员配额（设置为极大值表示无限制）
ADMIN_STORAGE_LIMIT=9007199254740991
ADMIN_FILE_COUNT_LIMIT=9007199254740991
ADMIN_MAX_FILE_SIZE=9007199254740991

# 文件清理配置
# 临时文件清理间隔（支持单位：ms, s, m, h, d）
TEMP_FILE_CLEANUP_INTERVAL=1h
# 临时文件清理年龄（小时）
TEMP_FILE_CLEANUP_AGE=1

# 输出文件清理间隔
OUTPUT_FILE_CLEANUP_INTERVAL=1d
# 输出文件清理年龄（天）
OUTPUT_FILE_CLEANUP_AGE=7

# 缩略图清理间隔
THUMBNAIL_CLEANUP_INTERVAL=1d
# 缩略图清理年龄（小时）
THUMBNAIL_CLEANUP_AGE=24

# 系统监控配置
# 资源监控间隔
RESOURCE_MONITOR_INTERVAL=5m
# 低内存阈值（百分比）
LOW_MEMORY_THRESHOLD=10
`;

const outputPath = path.join(__dirname, "..", ".env.example");

try {
  fs.writeFileSync(outputPath, envConfig, "utf8");
  console.log("✅ .env.example 文件已生成:", outputPath);
  console.log("");
  console.log("📋 使用方法:");
  console.log("1. 复制 .env.example 为 .env");
  console.log("2. 根据您的环境修改相应的值");
  console.log("3. 重启服务器以使配置生效");
  console.log("");
  console.log("📖 详细配置说明请参考: docs/ENVIRONMENT_VARIABLES.md");
} catch (error) {
  console.error("❌ 生成 .env.example 文件失败:", error.message);
  process.exit(1);
}
