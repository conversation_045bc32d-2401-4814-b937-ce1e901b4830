# FFmpeg-based Video Composition Server

This project is a Node.js server that generates composite videos using FFmpeg, based on a canvas-like multimedia composition specification.

The server provides an API for creating complex video compositions by combining various media elements such as videos, images, audio, and text. It utilizes FFmpeg's powerful filtering capabilities to apply transformations, effects, and overlays to these elements.

## Repository Structure

```
.
├── ARCHITECTURE.md
├── package.json
├── src
│   ├── ffmpeg
│   │   ├── config.ts
│   │   ├── FFmpegCommandGenerator.ts
│   │   ├── filters
│   │   │   ├── AudioFilterGenerator.ts
│   │   │   ├── BaseFilter.ts
│   │   │   ├── ImageFilterGenerator.ts
│   │   │   ├── TextFilterGenerator.ts
│   │   │   └── VideoFilterGenerator.ts
│   │   ├── filterUtils.ts
│   │   ├── types.ts
│   │   └── utils.ts
│   ├── index.ts
│   ├── services
│   │   ├── ProgressTracker.ts
│   │   └── ValidationService.ts
│   └── types.ts
└── tsconfig.json
```

### Key Files:
- `src/index.ts`: The main entry point of the application, setting up the Express server and API endpoints.
- `src/ffmpeg/FFmpegCommandGenerator.ts`: Responsible for generating the complete FFmpeg command based on the input canvas state.
- `src/services/ProgressTracker.ts`: Tracks the progress of video generation tasks.
- `src/services/ValidationService.ts`: Validates the input data for video generation requests.
- `src/ffmpeg/config.ts`: Contains configuration settings for FFmpeg operations.
- `tsconfig.json`: TypeScript configuration file.

## Usage Instructions

### Installation

Prerequisites:
- Node.js (version 14 or higher)
- FFmpeg (must be installed and accessible in the system PATH)

To install the project dependencies, run:

```bash
npm install
```

### Getting Started

To start the development server:

```bash
npm run dev
```

To build the project:

```bash
npm run build
```

To start the production server:

```bash
npm start
```

### API Endpoints

1. `POST /api/generateVideo`
   - Generates a video based on the provided canvas state.
   - Request body should contain a JSON object representing the canvas state.
   - Returns a task ID for tracking progress.

2. `GET /api/progress/:taskId`
   - Retrieves the progress of a video generation task.
   - Replace `:taskId` with the task ID returned from the generate video endpoint.

### Configuration

The `src/ffmpeg/config.ts` file contains various configuration options for video generation, including:

- Video constraints (max duration, dimensions)
- Default output settings
- Encoding presets
- Font settings
- Filter settings
- Audio settings

Modify these settings as needed for your specific use case.

### Troubleshooting

Common issues and solutions:

1. FFmpeg not found
   - Error: "FFmpeg command not found"
   - Solution: Ensure FFmpeg is installed and its bin directory is added to the system PATH.

2. Invalid input data
   - Error: "ValidationError: [specific error message]"
   - Solution: Check the request body against the expected canvas state structure. Refer to the `ValidationService` for detailed validation rules.

3. Task progress not updating
   - Problem: Progress seems stuck at a certain percentage
   - Diagnostic steps:
     1. Check the server logs for any error messages
     2. Verify that the FFmpeg process is running using system tools (e.g., `ps aux | grep ffmpeg`)
   - Solution: If FFmpeg process is not running, check for any system-level issues preventing process spawning.

For debugging:
- Set the `DEBUG` environment variable to `true` when starting the server for verbose logging.
- Log files are located in the `logs` directory in the project root.

## Data Flow

The video generation process follows these steps:

1. Client sends a POST request to `/api/generateVideo` with the canvas state.
2. The server validates the input using `ValidationService`.
3. A new task is created and tracked by `ProgressTracker`.
4. `FFmpegCommandGenerator` creates the FFmpeg command based on the canvas state.
5. The server spawns an FFmpeg process with the generated command.
6. `ProgressTracker` monitors the FFmpeg process output and updates the task progress.
7. Once complete, the generated video is saved, and the task status is updated.
8. The client can poll the `/api/progress/:taskId` endpoint to check the task status.

```
[Client] -> [Express Server] -> [ValidationService]
                             -> [ProgressTracker]
                             -> [FFmpegCommandGenerator]
                             -> [FFmpeg Process]
                             -> [Output Video File]
[Client] <- [Progress Updates] <- [ProgressTracker]
```

## Infrastructure

The project does not have a dedicated infrastructure stack. It is designed to run as a standalone Node.js application. Deployment and scaling considerations would depend on the specific hosting environment and requirements.